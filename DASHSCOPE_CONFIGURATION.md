# 🎯 阿里百炼平台配置指南

## 📋 **配置概览**

系统已成功配置为默认使用阿里百炼平台，具体配置如下：

### **基础信息**
- **平台**: 阿里云百炼 (DashScope)
- **API Key**: `sk-de657e2f9231440a8fb5585614e94611`
- **默认模型**: `deepseek-r1`
- **API端点**: `https://dashscope.aliyuncs.com/compatible-mode/v1`
- **兼容性**: OpenAI API 标准

## 🚀 **启动应用**

### **方式1：使用百炼专用配置（推荐）**
```bash
mvn spring-boot:run -Dspring.profiles.active=dev,dashscope
```

### **方式2：使用默认配置**
```bash
mvn spring-boot:run
```

### **方式3：Docker启动**
```bash
docker run -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=dev,dashscope \
  -e DASHSCOPE_API_KEY=sk-de657e2f9231440a8fb5585614e94611 \
  your-app:latest
```

## 🧪 **功能测试**

### **1. 基础状态检查**
```bash
# 检查百炼服务状态
curl http://localhost:8080/api/ai/dashscope/status
```

**预期响应**:
```json
{
  "provider": "dashscope",
  "platform": "阿里百炼",
  "model": "deepseek-r1",
  "available": true,
  "endpoint": "https://dashscope.aliyuncs.com/compatible-mode/v1",
  "timestamp": 1703123456789
}
```

### **2. DeepSeek-R1 基础测试**
```bash
curl -X POST http://localhost:8080/api/ai/dashscope/test/deepseek-r1 \
  -H "Content-Type: application/json" \
  -d "你好，请介绍一下DeepSeek-R1的特点和优势"
```

### **3. 数学推理能力测试**
```bash
curl -X POST http://localhost:8080/api/ai/dashscope/test/math
```

### **4. 代码分析能力测试**
```bash
curl -X POST http://localhost:8080/api/ai/dashscope/test/code
```

### **5. 逻辑推理能力测试**
```bash
curl -X POST http://localhost:8080/api/ai/dashscope/test/logic
```

### **6. 中文理解能力测试**
```bash
curl -X POST http://localhost:8080/api/ai/dashscope/test/chinese
```

### **7. 流式响应测试**
```bash
curl -X POST http://localhost:8080/api/ai/dashscope/test/stream \
  -H "Content-Type: application/json" \
  -d "请详细解释什么是人工智能，包括其发展历史、主要技术和应用领域"
```

### **8. 综合功能测试**
```bash
curl -X POST http://localhost:8080/api/ai/dashscope/test/comprehensive
```

## 🎯 **DeepSeek-R1 模型特色**

### **核心优势**
- **🧠 强大推理**: 671B参数，激活37B，专门优化推理能力
- **📊 数学专长**: 在数学、代码、逻辑推理任务上表现优异
- **🔍 思维链**: 提供详细的思考过程（reasoning_content）
- **🇨🇳 中文优化**: 对中文理解和生成有很好的支持
- **💰 免费额度**: 100万Token免费额度

### **适用场景**
- ✅ 数学问题求解
- ✅ 代码分析和生成
- ✅ 逻辑推理题
- ✅ 复杂问题分析
- ✅ 教学内容生成
- ✅ 学术研究辅助

### **技术规格**
- **上下文长度**: 65,536 tokens
- **最大输入**: 57,344 tokens
- **最大思维链长度**: 32,768 tokens
- **最大回复长度**: 8,192 tokens
- **输入成本**: 0.004元/千Token
- **输出成本**: 0.016元/千Token

## 🔧 **高级配置**

### **环境变量配置**
```bash
# 百炼配置
export DASHSCOPE_API_KEY=sk-de657e2f9231440a8fb5585614e94611
export DASHSCOPE_ENABLED=true
export DASHSCOPE_CHAT_MODEL=deepseek-r1

# 可选：其他模型
# export DASHSCOPE_CHAT_MODEL=deepseek-v3
# export DASHSCOPE_CHAT_MODEL=qwen-plus
# export DASHSCOPE_CHAT_MODEL=qwen-max
```

### **application.yml 配置**
```yaml
spring:
  ai:
    dashscope:
      enabled: true
      api-key: sk-de657e2f9231440a8fb5585614e94611
      chat:
        model: deepseek-r1
        temperature: 0.7
        max-tokens: 4096
```

### **支持的模型列表**
- `deepseek-r1` - 最新推理模型（推荐）
- `deepseek-r1-0528` - 升级版推理模型
- `deepseek-v3` - MoE模型，671B参数
- `qwen-plus` - 通义千问Plus
- `qwen-max` - 通义千问Max
- `qwen-turbo` - 通义千问Turbo

## 📊 **监控和日志**

### **启用调试日志**
```yaml
logging:
  level:
    com.nybc.ai: DEBUG
    org.springframework.ai: DEBUG
    org.springframework.web.reactive: DEBUG
```

### **性能监控**
```bash
# 查看响应时间
curl -w "@curl-format.txt" -X POST http://localhost:8080/api/ai/dashscope/test/deepseek-r1 \
  -H "Content-Type: application/json" \
  -d "测试响应时间"
```

## 🔒 **安全配置**

### **API Key 安全**
- ✅ 已配置在应用中，无需额外设置
- ✅ 建议生产环境使用环境变量
- ✅ 定期轮换API Key

### **访问控制**
```yaml
# 可选：添加访问控制
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized
```

## 🎉 **验证成功标志**

### **成功启动标志**
启动日志中应该看到：
```
INFO  - 百炼DashScope配置加载成功
INFO  - DeepSeek-R1模型初始化完成
INFO  - 统一AI服务启动成功，默认提供商: dashscope
```

### **功能验证清单**
- [ ] 基础对话功能正常
- [ ] 数学推理能力正常
- [ ] 代码分析能力正常
- [ ] 逻辑推理能力正常
- [ ] 中文理解能力正常
- [ ] 流式响应功能正常
- [ ] 思维链输出正常

## 📞 **技术支持**

### **常见问题**
1. **API Key无效**: 检查Key是否正确，是否有足够额度
2. **连接超时**: 检查网络连接，可能需要重试
3. **模型不可用**: 尝试切换到其他模型如`deepseek-v3`

### **联系方式**
- 阿里云百炼官方文档: https://help.aliyun.com/document_detail/2868565.html
- 技术支持: 通过阿里云控制台提交工单

---

**🎊 恭喜！阿里百炼配置完成，开始享受强大的AI能力吧！**
