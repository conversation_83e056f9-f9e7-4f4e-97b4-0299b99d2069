# =================================================================
# 统一AI配置 - 基于Spring AI框架
# 所有AI提供商都通过OpenAI兼容API或原生支持统一接入
# =================================================================

spring:
  ai:
    # ===================================================================
    # DeepSeek 配置 (OpenAI兼容API) - 默认启用
    # ===================================================================
    deepseek:
      enabled: ${DEEPSEEK_ENABLED:true}
      api-key: ${DEEPSEEK_API_KEY:sk-your-deepseek-key}
      chat:
        model: ${DEEPSEEK_CHAT_MODEL:deepseek-chat}
        temperature: 0.7
        max-tokens: 4096

    # ===================================================================
    # 阿里百炼 配置 (OpenAI兼容API)
    # ===================================================================
    dashscope:
      enabled: ${DASHSCOPE_ENABLED:false}
      api-key: ${DASHSCOPE_API_KEY:sk-your-dashscope-key}
      chat:
        model: ${DASHSCOPE_CHAT_MODEL:qwen-plus}
        temperature: 0.7
        max-tokens: 4096

    # ===================================================================
    # OpenAI 官方配置
    # ===================================================================
    openai:
      enabled: ${OPENAI_ENABLED:false}
      api-key: ${OPENAI_API_KEY:sk-your-openai-key}
      chat:
        model: ${OPENAI_CHAT_MODEL:gpt-4o}
        temperature: 0.7
        max-tokens: 4096
      embedding:
        model: ${OPENAI_EMBEDDING_MODEL:text-embedding-3-large}
      image:
        model: ${OPENAI_IMAGE_MODEL:dall-e-3}
        quality: hd
        size: 1024x1024

    # ===================================================================
    # Ollama 配置 (Spring AI原生支持)
    # ===================================================================
    ollama:
      enabled: ${OLLAMA_ENABLED:false}
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      chat:
        model: ${OLLAMA_CHAT_MODEL:llama3.2}
        temperature: 0.7
      embedding:
        model: ${OLLAMA_EMBEDDING_MODEL:nomic-embed-text}

    # ===================================================================
    # 向量数据库配置
    # ===================================================================
    vectorstore:
      milvus:
        uri: ${MILVUS_URI:milvus://localhost:19530}
        database-name: ${MILVUS_DATABASE:ai_assistant}
        collection-name: ${MILVUS_COLLECTION:documents}

# =================================================================
# 环境变量配置示例
# =================================================================
# 在生产环境中，通过以下环境变量进行配置：
#
# # DeepSeek (默认)
# export DEEPSEEK_ENABLED=true
# export DEEPSEEK_API_KEY=sk-your-deepseek-key
# export DEEPSEEK_CHAT_MODEL=deepseek-chat
#
# # 阿里百炼
# export DASHSCOPE_ENABLED=true
# export DASHSCOPE_API_KEY=sk-your-dashscope-key
# export DASHSCOPE_CHAT_MODEL=qwen-plus
#
# # OpenAI
# export OPENAI_ENABLED=true
# export OPENAI_API_KEY=sk-your-openai-key
# export OPENAI_CHAT_MODEL=gpt-4o
#
# # Ollama
# export OLLAMA_ENABLED=true
# export OLLAMA_BASE_URL=http://localhost:11434
# export OLLAMA_CHAT_MODEL=llama3.2
#
# =================================================================
