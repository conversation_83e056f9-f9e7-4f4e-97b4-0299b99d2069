# 服务器端口
server:
  shutdown: graceful # 启用优雅停机
  port: 18089
  #tomcat配置
  tomcat:
    uri-encoding: UTF-8
    accept-count: 400
    max-connections: 400
    connection-timeout: 60000
    threads:
      max: 200
      min-spare: 30
  servlet:
    context-path: /
    encoding:
      enabled: true
      charset: UTF-8
      force: true
  error:
    whitelabel:
      enabled: false
spring:
  application:
    name: spring-ai-demo
  lifecycle:
    timeout-per-shutdown-phase: 30s
  # 数据源postgresql
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ********************************************************************************************************************************************
    username: postgres
    password: Root123456.
  data:
    redis:
      timeout: 30s
      host: **********
      port: 6379
      password: Root123456.
      database: 0
      ssl:
        enabled: false
      repositories:
        enabled: false

  # 启用Spring缓存
  cache:
    type: redis

  # ===================================================================
  # Spring AI 配置 - 手动配置模式
  # ===================================================================
  ai:
    # 阿里百炼 配置 (OpenAI兼容API) - 默认启用
    dashscope:
      enabled: ${DASHSCOPE_ENABLED:true}
      api-key: ${DASHSCOPE_API_KEY:sk-de657e2f9231440a8fb5585614e94611}
      chat:
        model: ${DASHSCOPE_CHAT_MODEL:deepseek-r1}

    # OpenAI 官方配置 - 可选
    openai:
      enabled: ${OPENAI_ENABLED:false}
      api-key: ${OPENAI_API_KEY:sk-your-openai-key}
      chat:
        model: ${OPENAI_CHAT_MODEL:gpt-4o}
      embedding:
        model: ${OPENAI_EMBEDDING_MODEL:text-embedding-3-large}

    # Ollama 配置 - 可选
    ollama:
      enabled: ${OLLAMA_ENABLED:false}
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      chat:
        model: ${OLLAMA_CHAT_MODEL:llama3.2}

    # 向量数据库配置
    vectorstore:
      milvus:
        uri: ${MILVUS_URI:milvus://localhost:19530}
        database-name: ${MILVUS_DATABASE:ai_assistant}
        collection-name: ${MILVUS_COLLECTION:documents}
        embedding-dimension: 768
# MyBatis 配置
mybatis:
  # Mapper XML文件的位置
  mapper-locations: classpath:mapper/*.xml
  # 类型别名包，让XML中可以直接使用类名而不是全限定名
  type-aliases-package: com.nybc.ai.domain.entity
  configuration:
    map-underscore-to-camel-case: true

# LiteFlow 配置
liteflow:
  # 规则源类型为sql
  rule-source-ext-data: sql
  # 是否打印LiteFlow Logo
  print-banner: true
  # 开启LiteFlow执行流程的监控
  monitor:
    enable-log: true
  # 并行执行线程池配置
  when-thread-pool-class: "com.yomahub.liteflow.thread.LiteFlowDefaultWhenThreadPool"
  when-max-wait-seconds: 15 # 设置并行执行最长等待时间为15秒
  # 自定义主执行线程池 (可选, 但建议生产环境配置)
  # main-executor-works: 16 # 工作线程数
  # main-executor-queue-size: 512 # 队列大小

# SpringDoc OpenAPI (Swagger) 配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

# ===================================================================
# 应用自定义配置
# ===================================================================
app:
  ai:
    # 统一AI架构配置
    unified-enabled: true
    default-provider: dashscope
  conversation:
    max-context-tokens: 4000
    summary-retention-days: 30
    auto-compression-enabled: true
    compression-threshold: 0.8
    topic-shift-sensitivity: 0.7
    domain-entity-extraction-enabled: true
    max-history-messages: 50
  feedback:
    auto-optimization: true
    analysis-retention-days: 180

# ===================================================================
# 欢迎语
# ===================================================================
