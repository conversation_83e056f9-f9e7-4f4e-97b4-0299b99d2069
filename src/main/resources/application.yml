# 服务器端口
server:
  port: 8080

# Spring Boot 应用名称, LiteFlow的SQL插件需要此配置来匹配数据库中的规则
spring:
  application:
    name: spring-ai-demo
  # 数据源配置 (使用H2内存数据库)
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password:
    driver-class-name: org.h2.Driver
  # SQL脚本初始化配置
  sql:
    init:
      mode: always # 每次启动都执行脚本
      schema-locations: classpath:db/schema.sql # schema脚本位置
      data-locations: classpath:db/data.sql   # data脚本位置
  # Redis 配置 (用于分布式缓存)
  data:
    redis:
      host: localhost
      port: 6379
      # password: your-redis-password # 如果有密码，请取消注释并配置

  # 启用Spring缓存
  cache:
    type: redis

  # ===================================================================
  # AI Provider Configurations
  # ===================================================================
  ai:
    # Spring AI Foundation Models
    openai:
      # 这里可以复用为指向DeepSeek的兼容API
      base-url: https://api.deepseek.com/v1
      api-key: ${DEEPSEEK_API_KEY:sk-your-deepseek-key-here} # 优先从环境变量读取
      chat:
        options:
          model: deepseek-chat
    # 自定义的Provider配置
    provider:
      deepseek:
        api-key: ${DEEPSEEK_API_KEY:sk-your-deepseek-key-here}
        endpoint: "https://api.deepseek.com/chat/completions"
        model: "deepseek-chat"
      ollama:
        base-url: http://localhost:11434
        model: "llama3"
        enabled: false
      qianwen:
        api-key: ${QIANWEN_API_KEY:sk-your-key-here}
        enabled: false
    milvus:
      vector-store:
        uri: milvus://your-milvus-host:19530
        collection-name: your_collection_name
        embedding-dimension: 768 # 请根据您使用的Embedding模型的维度设置
    ollama:
      base-url: http://localhost:11434
      chat:
        model: deepseek-coder # 您的模型
      embedding:
        model: mxbai-embed-large # 您的Embedding模型
# MyBatis 配置
mybatis:
  # Mapper XML文件的位置
  mapper-locations: classpath:mapper/*.xml
  # 类型别名包，让XML中可以直接使用类名而不是全限定名
  type-aliases-package: com.nybc.ai.domain.entity
  configuration:
    map-underscore-to-camel-case: true

# LiteFlow 配置
liteflow:
  # 规则源类型为sql
  rule-source-ext-data: sql
  # 是否打印LiteFlow Logo
  print-banner: true
  # 开启LiteFlow执行流程的监控
  monitor:
    enable-log: true
  # 并行执行线程池配置
  when-thread-pool-class: "com.yomahub.liteflow.thread.LiteFlowDefaultWhenThreadPool"
  when-max-wait-seconds: 15 # 设置并行执行最长等待时间为15秒
  # 自定义主执行线程池 (可选, 但建议生产环境配置)
  # main-executor-works: 16 # 工作线程数
  # main-executor-queue-size: 512 # 队列大小

# SpringDoc OpenAPI (Swagger) 配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

# ===================================================================
# 应用自定义配置
# ===================================================================
app:
  ai:
    default-provider: deepseek
  conversation:
    max-context-tokens: 4000
    summary-retention-days: 30
    auto-compression-enabled: true
    compression-threshold: 0.8
    topic-shift-sensitivity: 0.7
    domain-entity-extraction-enabled: true
    max-history-messages: 50
  feedback:
    auto-optimization: true
    analysis-retention-days: 180

# ===================================================================
# 欢迎语
# ===================================================================
