# 服务器端口
server:
  port: 8080

# Spring Boot 应用名称, LiteFlow的SQL插件需要此配置来匹配数据库中的规则
spring:
  application:
    name: spring-ai-demo
  # 数据源配置 (使用H2内存数据库)
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password:
    driver-class-name: org.h2.Driver
  # SQL脚本初始化配置
  sql:
    init:
      mode: always # 每次启动都执行脚本
      schema-locations: classpath:db/schema.sql # schema脚本位置
      data-locations: classpath:db/data.sql   # data脚本位置
  # Redis 配置 (用于分布式缓存)
  data:
    redis:
      host: localhost
      port: 6379
      # password: your-redis-password # 如果有密码，请取消注释并配置

  # 启用Spring缓存
  cache:
    type: redis

  # ===================================================================
  # 统一AI配置 - 基于Spring AI框架
  # ===================================================================
  ai:
    # DeepSeek 配置 (OpenAI兼容API) - 默认启用
    deepseek:
      enabled: ${DEEPSEEK_ENABLED:true}
      api-key: ${DEEPSEEK_API_KEY:sk-your-deepseek-key}
      chat:
        model: ${DEEPSEEK_CHAT_MODEL:deepseek-chat}
        temperature: 0.7
        max-tokens: 4096

    # 阿里百炼 配置 (OpenAI兼容API)
    dashscope:
      enabled: ${DASHSCOPE_ENABLED:false}
      api-key: ${DASHSCOPE_API_KEY:sk-your-dashscope-key}
      chat:
        model: ${DASHSCOPE_CHAT_MODEL:qwen-plus}
        temperature: 0.7
        max-tokens: 4096

    # OpenAI 官方配置
    openai:
      enabled: ${OPENAI_ENABLED:false}
      api-key: ${OPENAI_API_KEY:sk-your-openai-key}
      chat:
        options:
          model: ${OPENAI_CHAT_MODEL:gpt-4o}
          temperature: 0.7
          max-tokens: 4096
      embedding:
        options:
          model: ${OPENAI_EMBEDDING_MODEL:text-embedding-3-large}
      image:
        options:
          model: ${OPENAI_IMAGE_MODEL:dall-e-3}
          quality: hd
          size: 1024x1024

    # Ollama 配置 (Spring AI原生支持)
    ollama:
      enabled: ${OLLAMA_ENABLED:false}
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
      chat:
        options:
          model: ${OLLAMA_CHAT_MODEL:llama3.2}
          temperature: 0.7
      embedding:
        options:
          model: ${OLLAMA_EMBEDDING_MODEL:nomic-embed-text}

    # 向量数据库配置
    vectorstore:
      milvus:
        uri: ${MILVUS_URI:milvus://localhost:19530}
        database-name: ${MILVUS_DATABASE:ai_assistant}
        collection-name: ${MILVUS_COLLECTION:documents}
        embedding-dimension: 768
# MyBatis 配置
mybatis:
  # Mapper XML文件的位置
  mapper-locations: classpath:mapper/*.xml
  # 类型别名包，让XML中可以直接使用类名而不是全限定名
  type-aliases-package: com.nybc.ai.domain.entity
  configuration:
    map-underscore-to-camel-case: true

# LiteFlow 配置
liteflow:
  # 规则源类型为sql
  rule-source-ext-data: sql
  # 是否打印LiteFlow Logo
  print-banner: true
  # 开启LiteFlow执行流程的监控
  monitor:
    enable-log: true
  # 并行执行线程池配置
  when-thread-pool-class: "com.yomahub.liteflow.thread.LiteFlowDefaultWhenThreadPool"
  when-max-wait-seconds: 15 # 设置并行执行最长等待时间为15秒
  # 自定义主执行线程池 (可选, 但建议生产环境配置)
  # main-executor-works: 16 # 工作线程数
  # main-executor-queue-size: 512 # 队列大小

# SpringDoc OpenAPI (Swagger) 配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

# ===================================================================
# 应用自定义配置
# ===================================================================
app:
  ai:
    # 统一AI架构配置
    unified-enabled: true
    default-provider: deepseek
    # 旧架构配置（迁移期间保留）
    legacy:
      chat-service:
        enabled: false  # 禁用自定义ChatService
      model-service:
        enabled: false  # 禁用自定义AiModelService
  conversation:
    max-context-tokens: 4000
    summary-retention-days: 30
    auto-compression-enabled: true
    compression-threshold: 0.8
    topic-shift-sensitivity: 0.7
    domain-entity-extraction-enabled: true
    max-history-messages: 50
  feedback:
    auto-optimization: true
    analysis-retention-days: 180

# ===================================================================
# 欢迎语
# ===================================================================
