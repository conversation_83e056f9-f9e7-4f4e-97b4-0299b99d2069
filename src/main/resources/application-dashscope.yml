# =================================================================
# 阿里百炼专用配置文件
# 使用方式：spring.profiles.active=dev,dashscope
# =================================================================

spring:
  ai:
    # 阿里百炼 配置 (OpenAI兼容API) - 默认启用
    dashscope:
      enabled: true
      api-key: sk-de657e2f9231440a8fb5585614e94611
      chat:
        model: deepseek-r1  # 可选：deepseek-r1, deepseek-v3, qwen-plus, qwen-max等
        temperature: 0.7
        max-tokens: 4096

    # 其他提供商 - 禁用
    deepseek:
      enabled: false
    openai:
      enabled: false
    ollama:
      enabled: false

# =================================================================
# 应用配置
# =================================================================
app:
  ai:
    unified-enabled: true
    default-provider: dashscope
    legacy:
      chat-service:
        enabled: false  # 禁用自定义ChatService
      model-service:
        enabled: false  # 禁用自定义AiModelService

# =================================================================
# 日志配置
# =================================================================
logging:
  level:
    com.nybc.ai: DEBUG
    org.springframework.ai: DEBUG
    
# =================================================================
# 环境变量说明
# =================================================================
# 可以通过以下环境变量覆盖配置：
# export DASHSCOPE_API_KEY=sk-de657e2f9231440a8fb5585614e94611
# export DASHSCOPE_CHAT_MODEL=deepseek-r1
# export DASHSCOPE_ENABLED=true
