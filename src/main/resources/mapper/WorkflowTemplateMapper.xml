<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.workflow.mapper.WorkflowTemplateMapper">

    <resultMap type="com.nybc.ai.workflow.entity.WorkflowTemplate" id="WorkflowTemplateResult">
        <id property="id" column="id"/>
        <result property="templateName" column="template_name"/>
        <result property="description" column="description"/>
        <result property="taskType" column="task_type"/>
        <result property="workflowType" column="workflow_type"/>
        <result property="chainName" column="chain_name"/>
        <result property="elExpression" column="el_expression"/>
        <result property="nodes" column="nodes" typeHandler="com.nybc.ai.common.handler.JsonTypeHandler"/>
        <result property="matchingCondition" column="matching_condition" typeHandler="com.nybc.ai.common.handler.JsonTypeHandler"/>
        <result property="version" column="version"/>
        <result property="isDefault" column="is_default"/>
        <result property="enabled" column="enabled"/>
        <result property="priority" column="priority"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, template_name, description, task_type, workflow_type, chain_name, el_expression,
        nodes, matching_condition, version, is_default, enabled, priority, tenant_id,
        create_time, update_time, create_user, update_user
    </sql>

    <insert id="insert" parameterType="com.nybc.ai.workflow.entity.WorkflowTemplate" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO workflow_templates(
            template_name, description, task_type, workflow_type, chain_name, el_expression,
            nodes, matching_condition, version, is_default, enabled, priority, tenant_id,
            create_time, update_time, create_user, update_user
        ) VALUES (
            #{templateName}, #{description}, #{taskType}, #{workflowType}, #{chainName}, #{elExpression},
            #{nodes, typeHandler=com.nybc.ai.common.handler.JsonTypeHandler},
            #{matchingCondition, typeHandler=com.nybc.ai.common.handler.JsonTypeHandler},
            #{version}, #{isDefault}, #{enabled}, #{priority}, #{tenantId},
            #{createTime}, #{updateTime}, #{createUser}, #{updateUser}
        )
    </insert>

    <select id="findById" resultMap="WorkflowTemplateResult">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_templates
        WHERE id = #{id}
    </select>

    <update id="update" parameterType="com.nybc.ai.workflow.entity.WorkflowTemplate">
        UPDATE workflow_templates
        SET template_name = #{templateName},
            description = #{description},
            task_type = #{taskType},
            workflow_type = #{workflowType},
            chain_name = #{chainName},
            el_expression = #{elExpression},
            nodes = #{nodes, typeHandler=com.nybc.ai.common.handler.JsonTypeHandler},
            matching_condition = #{matchingCondition, typeHandler=com.nybc.ai.common.handler.JsonTypeHandler},
            version = #{version},
            is_default = #{isDefault},
            enabled = #{enabled},
            priority = #{priority},
            update_time = #{updateTime},
            update_user = #{updateUser}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM workflow_templates WHERE id = #{id}
    </delete>

    <select id="existsByNameAndTenant" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM workflow_templates
        WHERE template_name = #{templateName}
        <choose>
            <when test="tenantId != null">
                AND tenant_id = #{tenantId}
            </when>
            <otherwise>
                AND tenant_id IS NULL
            </otherwise>
        </choose>
    </select>

    <select id="findByTaskTypeAndTenant" resultMap="WorkflowTemplateResult">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_templates
        WHERE task_type = #{taskType}
        <choose>
            <when test="tenantId != null">
                AND tenant_id = #{tenantId}
            </when>
            <otherwise>
                AND tenant_id IS NULL
            </otherwise>
        </choose>
        AND enabled = true
        ORDER BY priority DESC, create_time DESC
    </select>

    <select id="findByTenant" resultMap="WorkflowTemplateResult">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_templates
        <where>
            <choose>
                <when test="tenantId != null">
                    tenant_id = #{tenantId}
                </when>
                <otherwise>
                    tenant_id IS NULL
                </otherwise>
            </choose>
        </where>
        ORDER BY priority DESC, create_time DESC
    </select>

    <select id="findDefaultByTaskType" resultMap="WorkflowTemplateResult">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_templates
        WHERE task_type = #{taskType}
        AND is_default = true
        AND enabled = true
        ORDER BY priority DESC
        LIMIT 1
    </select>

    <select id="findByChainName" resultMap="WorkflowTemplateResult">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_templates
        WHERE chain_name = #{chainName}
    </select>

    <select id="findEnabledByTenant" resultMap="WorkflowTemplateResult">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_templates
        WHERE enabled = true
        <choose>
            <when test="tenantId != null">
                AND tenant_id = #{tenantId}
            </when>
            <otherwise>
                AND tenant_id IS NULL
            </otherwise>
        </choose>
        ORDER BY priority DESC, create_time DESC
    </select>

    <select id="findByTaskTypeOrderByPriority" resultMap="WorkflowTemplateResult">
        SELECT <include refid="Base_Column_List"/>
        FROM workflow_templates
        WHERE task_type = #{taskType}
        AND enabled = true
        <choose>
            <when test="tenantId != null">
                AND (tenant_id = #{tenantId} OR tenant_id IS NULL)
            </when>
            <otherwise>
                AND tenant_id IS NULL
            </otherwise>
        </choose>
        ORDER BY 
            CASE WHEN tenant_id = #{tenantId} THEN 1 ELSE 2 END,
            priority DESC,
            create_time DESC
    </select>

    <update id="batchUpdateStatus">
        UPDATE workflow_templates
        SET enabled = #{enabled}, update_time = CURRENT_TIMESTAMP
        WHERE id IN
        <foreach collection="templateIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getTemplateStats" resultType="map">
        SELECT 
            COUNT(*) as total_templates,
            COUNT(CASE WHEN enabled = true THEN 1 END) as enabled_templates,
            COUNT(CASE WHEN is_default = true THEN 1 END) as default_templates,
            COUNT(DISTINCT task_type) as task_types
        FROM workflow_templates
        <where>
            <choose>
                <when test="tenantId != null">
                    tenant_id = #{tenantId}
                </when>
                <otherwise>
                    tenant_id IS NULL
                </otherwise>
            </choose>
        </where>
    </select>

</mapper>
