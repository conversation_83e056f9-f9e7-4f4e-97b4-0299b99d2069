<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.feedback.mapper.PromptPerformanceAnalyticsMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.nybc.ai.feedback.entity.PromptPerformanceAnalytics">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="template_id" property="templateId" jdbcType="BIGINT"/>
        <result column="version_id" property="versionId" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="analysis_period" property="analysisPeriod" jdbcType="VARCHAR"/>
        <result column="total_executions" property="totalExecutions" jdbcType="INTEGER"/>
        <result column="success_rate" property="successRate" jdbcType="DECIMAL"/>
        <result column="avg_execution_time" property="avgExecutionTime" jdbcType="DECIMAL"/>
        <result column="avg_feedback_score" property="avgFeedbackScore" jdbcType="DECIMAL"/>
        <result column="positive_feedback_rate" property="positiveFeedbackRate" jdbcType="DECIMAL"/>
        <result column="common_failure_patterns" property="commonFailurePatterns" jdbcType="VARCHAR"/>
        <result column="optimization_suggestions" property="optimizationSuggestions" jdbcType="VARCHAR"/>
        <result column="performance_trend" property="performanceTrend" jdbcType="VARCHAR"/>
        <result column="analysis_time" property="analysisTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, template_id, version_id, tenant_id, analysis_period, total_executions,
        success_rate, avg_execution_time, avg_feedback_score, positive_feedback_rate,
        common_failure_patterns, optimization_suggestions, performance_trend, analysis_time
    </sql>

    <!-- 插入性能分析记录 -->
    <insert id="insert" parameterType="com.nybc.ai.feedback.entity.PromptPerformanceAnalytics" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO prompt_performance_analytics (
            template_id, version_id, tenant_id, analysis_period, total_executions,
            success_rate, avg_execution_time, avg_feedback_score, positive_feedback_rate,
            common_failure_patterns, optimization_suggestions, performance_trend, analysis_time
        ) VALUES (
            #{templateId}, #{versionId}, #{tenantId}, #{analysisPeriod}, #{totalExecutions},
            #{successRate}, #{avgExecutionTime}, #{avgFeedbackScore}, #{positiveFeedbackRate},
            #{commonFailurePatterns}, #{optimizationSuggestions}, #{performanceTrend}, #{analysisTime}
        )
    </insert>

    <!-- 根据模板和周期查询性能分析 -->
    <select id="findByTemplateAndPeriod" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_performance_analytics
        WHERE template_id = #{templateId}
          AND tenant_id = #{tenantId}
          AND analysis_period = #{analysisPeriod}
          <if test="versionId != null">
              AND version_id = #{versionId}
          </if>
        ORDER BY analysis_time DESC
        LIMIT 1
    </select>

    <!-- 获取性能趋势数据 -->
    <select id="findTrendData" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_performance_analytics
        WHERE template_id = #{templateId}
          AND tenant_id = #{tenantId}
        ORDER BY analysis_time DESC
        <if test="periodCount != null">
            LIMIT #{periodCount}
        </if>
    </select>

    <!-- 获取最近的性能分析数据 -->
    <select id="findRecentAnalytics" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_performance_analytics
        WHERE template_id = #{templateId}
          AND tenant_id = #{tenantId}
        ORDER BY analysis_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查找最佳性能版本 -->
    <select id="findBestPerformingVersion" resultType="java.lang.Long">
        SELECT version_id
        FROM prompt_performance_analytics
        WHERE template_id = #{templateId}
          AND tenant_id = #{tenantId}
          AND version_id IS NOT NULL
        ORDER BY success_rate DESC, avg_feedback_score DESC, positive_feedback_rate DESC
        LIMIT 1
    </select>

    <!-- 根据版本查询最新的性能分析 -->
    <select id="findLatestByVersion" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_performance_analytics
        WHERE template_id = #{templateId}
          AND version_id = #{versionId}
          AND tenant_id = #{tenantId}
        ORDER BY analysis_time DESC
        LIMIT 1
    </select>

    <!-- 根据时间范围查询性能分析 -->
    <select id="findByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_performance_analytics
        WHERE template_id = #{templateId}
          AND tenant_id = #{tenantId}
          AND analysis_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY analysis_time DESC
    </select>

    <!-- 更新性能分析记录 -->
    <update id="update" parameterType="com.nybc.ai.feedback.entity.PromptPerformanceAnalytics">
        UPDATE prompt_performance_analytics
        SET template_id = #{templateId},
            version_id = #{versionId},
            tenant_id = #{tenantId},
            analysis_period = #{analysisPeriod},
            total_executions = #{totalExecutions},
            success_rate = #{successRate},
            avg_execution_time = #{avgExecutionTime},
            avg_feedback_score = #{avgFeedbackScore},
            positive_feedback_rate = #{positiveFeedbackRate},
            common_failure_patterns = #{commonFailurePatterns},
            optimization_suggestions = #{optimizationSuggestions},
            performance_trend = #{performanceTrend},
            analysis_time = #{analysisTime}
        WHERE id = #{id}
    </update>

    <!-- 删除过期的性能分析记录 -->
    <delete id="deleteExpired">
        DELETE FROM prompt_performance_analytics
        WHERE analysis_time &lt; #{expireTime}
    </delete>

</mapper>
