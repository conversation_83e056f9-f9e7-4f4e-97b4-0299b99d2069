<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.feedback.mapper.StudentLearningBehaviorMapper">

    <resultMap type="com.nybc.ai.feedback.entity.StudentLearningBehavior" id="StudentLearningBehaviorResult">
        <id property="id" column="id"/>
        <result property="studentId" column="student_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="assignmentId" column="assignment_id"/>
        <result property="behaviorType" column="behavior_type"/>
        <result property="behaviorData" column="behavior_data"/>
        <result property="sessionId" column="session_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, student_id, tenant_id, assignment_id, behavior_type, behavior_data, session_id, create_time
    </sql>

    <insert id="insert" parameterType="com.nybc.ai.feedback.entity.StudentLearningBehavior" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO student_learning_behaviors(student_id, tenant_id, assignment_id, behavior_type, behavior_data, session_id, create_time)
        VALUES (#{studentId}, #{tenantId}, #{assignmentId}, #{behaviorType}, #{behaviorData}, #{sessionId}, #{createTime})
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO student_learning_behaviors(student_id, tenant_id, assignment_id, behavior_type, behavior_data, session_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.studentId}, #{item.tenantId}, #{item.assignmentId}, #{item.behaviorType}, #{item.behaviorData}, #{item.sessionId}, #{item.createTime})
        </foreach>
    </insert>

    <select id="findByStudentAndTenant" resultMap="StudentLearningBehaviorResult">
        SELECT <include refid="Base_Column_List"/>
        FROM student_learning_behaviors
        WHERE student_id = #{studentId} AND tenant_id = #{tenantId}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="findByAssignment" resultMap="StudentLearningBehaviorResult">
        SELECT <include refid="Base_Column_List"/>
        FROM student_learning_behaviors
        WHERE assignment_id = #{assignmentId}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="countByBehaviorType" resultType="int">
        SELECT COUNT(*)
        FROM student_learning_behaviors
        WHERE student_id = #{studentId} AND tenant_id = #{tenantId} AND behavior_type = #{behaviorType}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <select id="findRecentBehaviors" resultMap="StudentLearningBehaviorResult">
        SELECT <include refid="Base_Column_List"/>
        FROM student_learning_behaviors
        WHERE student_id = #{studentId} AND tenant_id = #{tenantId}
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

</mapper>
