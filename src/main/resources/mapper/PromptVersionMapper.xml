<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.prompt.mapper.PromptVersionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.nybc.ai.domain.PromptVersion">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="template_id" property="templateId" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="version_number" property="versionNumber" jdbcType="INTEGER"/>
        <result column="template_content" property="templateContent" jdbcType="VARCHAR"/>
        <result column="raw_idea_text" property="rawIdeaText" jdbcType="VARCHAR"/>
        <result column="changelog" property="changelog" jdbcType="VARCHAR"/>
        <result column="optimization_model" property="optimizationModel" jdbcType="VARCHAR"/>
        <result column="optimization_cot" property="optimizationCot" jdbcType="VARCHAR"/>
        <result column="runtime_provider" property="runtimeProvider" jdbcType="VARCHAR"/>
        <result column="performance_metrics" property="performanceMetrics" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, template_id, tenant_id, version_number, template_content, raw_idea_text,
        changelog, optimization_model, optimization_cot, runtime_provider, performance_metrics,
        create_time, create_user, update_time, update_user, deleted
    </sql>

    <!-- 插入版本记录 -->
    <insert id="insert" parameterType="com.nybc.ai.domain.PromptVersion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO prompt_versions (
            template_id, tenant_id, version_number, template_content, raw_idea_text,
            changelog, optimization_model, optimization_cot, runtime_provider, performance_metrics,
            create_time, create_user, update_time, update_user, deleted
        ) VALUES (
            #{templateId}, #{tenantId}, #{versionNumber}, #{templateContent}, #{rawIdeaText},
            #{changelog}, #{optimizationModel}, #{optimizationCot}, #{runtimeProvider}, #{performanceMetrics},
            #{createTime}, #{createUser}, #{updateTime}, #{updateUser}, #{deleted}
        )
    </insert>

    <!-- 根据ID查询版本 -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_versions
        WHERE id = #{id}
          AND deleted = 0
    </select>

    <!-- 根据模板ID查询所有版本 -->
    <select id="findByTemplateId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_versions
        WHERE template_id = #{templateId}
          AND deleted = 0
        ORDER BY version_number DESC
    </select>

    <!-- 更新版本记录 -->
    <update id="update" parameterType="com.nybc.ai.domain.PromptVersion">
        UPDATE prompt_versions
        SET template_id = #{templateId},
            tenant_id = #{tenantId},
            version_number = #{versionNumber},
            template_content = #{templateContent},
            raw_idea_text = #{rawIdeaText},
            changelog = #{changelog},
            optimization_model = #{optimizationModel},
            optimization_cot = #{optimizationCot},
            runtime_provider = #{runtimeProvider},
            performance_metrics = #{performanceMetrics},
            update_time = #{updateTime},
            update_user = #{updateUser}
        WHERE id = #{id}
          AND deleted = 0
    </update>

    <!-- 逻辑删除指定模板的所有版本 -->
    <update id="logicalDeleteByTemplateId">
        UPDATE prompt_versions
        SET deleted = 1,
            update_time = NOW()
        WHERE template_id = #{templateId}
          AND deleted = 0
    </update>

</mapper>