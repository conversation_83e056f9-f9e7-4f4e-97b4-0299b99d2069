<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.rules.mapper.RuleChainMapper">

    <resultMap type="com.nybc.ai.rules.entity.RuleChain" id="RuleChainResult">
        <id property="id" column="id"/>
        <result property="applicationName" column="application_name"/>
        <result property="chainName" column="chain_name"/>
        <result property="elData" column="el_data"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="assignmentId" column="assignment_id"/>
    </resultMap>

    <!--
        根据租户ID和作业ID查询规则链。
        此查询实现了优先级匹配：
        1. 首先查找完全匹配 tenant_id 和 assignment_id 的规则。
        2. 然后查找匹配 tenant_id 但 assignment_id 为 NULL 的通用规则。
        3. 通过 ORDER BY assignment_id DESC NULLS LAST，确保非空的（特定的）规则排在前面。
    -->
    <select id="findByTenantIdAndAssignmentIdWithPriority" resultMap="RuleChainResult">
        SELECT * FROM rule_chains
        WHERE tenant_id = #{tenantId}
          AND (assignment_id = #{assignmentId} OR assignment_id IS NULL)
        ORDER BY assignment_id DESC NULLS LAST
    </select>

    <select id="findByTenantId" resultMap="RuleChainResult">
        SELECT * FROM rule_chains
        WHERE tenant_id = #{tenantId}
    </select>

    <!--
        使用H2数据库的MERGE语句实现插入或更新。
        - ON(chain_name): MERGE操作的关键列，用于判断记录是否存在。
        - WHEN MATCHED THEN UPDATE: 如果存在同名的chain_name，则更新el_data, tenant_id, assignment_id。
        - WHEN NOT MATCHED THEN INSERT: 如果不存在，则插入一条新记录。
    -->
    <update id="insertOrUpdate" parameterType="com.nybc.ai.rules.entity.RuleChain">
        MERGE INTO rule_chains (application_name, chain_name, el_data, tenant_id, assignment_id)
        KEY(chain_name)
        VALUES (#{applicationName}, #{chainName}, #{elData}, #{tenantId}, #{assignmentId})
    </update>

    <select id="findAll" resultMap="RuleChainResult">
        select application_name, chain_name, el_data, create_time, update_time
        from rule_chain
    </select>

    <select id="findChain" resultType="java.lang.String">
        SELECT el_data
        FROM rule_chain
        WHERE tenant_id = #{tenantId}
          AND assignment_id = #{assignmentId}
        LIMIT 1
    </select>

</mapper> 