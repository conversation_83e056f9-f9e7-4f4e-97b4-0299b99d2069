<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.prompt.mapper.PromptExecutionLogMapper">

    <resultMap id="BaseResultMap" type="com.nybc.ai.domain.PromptExecutionLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="session_id" jdbcType="VARCHAR" property="sessionId"/>
        <result column="template_id" jdbcType="BIGINT" property="templateId"/>
        <result column="version_id" jdbcType="BIGINT" property="versionId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="input_variables" jdbcType="OTHER" property="inputVariables" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="final_prompt" jdbcType="VARCHAR" property="finalPrompt"/>
        <result column="ai_provider" jdbcType="VARCHAR" property="aiProvider"/>
        <result column="raw_ai_response" jdbcType="VARCHAR" property="rawAiResponse"/>
        <result column="parsed_ai_response" jdbcType="VARCHAR" property="parsedAiResponse"/>
        <result column="execution_time_ms" jdbcType="BIGINT" property="executionTimeMs"/>
        <result column="feedback_status" jdbcType="VARCHAR" property="feedbackStatus"/>
        <result column="feedback_score" jdbcType="INTEGER" property="feedbackScore"/>
        <result column="feedback_notes" jdbcType="VARCHAR" property="feedbackNotes"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="BIGINT" property="createUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="BIGINT" property="updateUser"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, session_id, template_id, version_id, tenant_id, input_variables, final_prompt,
        ai_provider, raw_ai_response, parsed_ai_response, execution_time_ms, feedback_status,
        feedback_score, feedback_notes, create_time, create_user, update_time, update_user, deleted
    </sql>

    <select id="findById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from prompt_execution_logs
        where id = #{id,jdbcType=BIGINT} and deleted = 0
    </select>

    <insert id="insert" parameterType="com.nybc.ai.domain.PromptExecutionLog" useGeneratedKeys="true" keyProperty="id">
        insert into prompt_execution_logs(session_id, template_id, version_id, tenant_id, input_variables,
                                          final_prompt, ai_provider, raw_ai_response, parsed_ai_response,
                                          execution_time_ms, create_user, update_user)
        values (#{sessionId}, #{templateId}, #{versionId}, #{tenantId},
                #{inputVariables,jdbcType=OTHER,typeHandler=org.apache.ibatis.type.StringTypeHandler},
                #{finalPrompt}, #{aiProvider}, #{rawAiResponse}, #{parsedAiResponse},
                #{executionTimeMs}, #{createUser}, #{updateUser})
    </insert>

    <update id="update" parameterType="com.nybc.ai.domain.PromptExecutionLog">
        update prompt_execution_logs
        <set>
            <if test="feedbackStatus != null">feedback_status = #{feedbackStatus,jdbcType=VARCHAR},</if>
            <if test="feedbackScore != null">feedback_score = #{feedbackScore,jdbcType=INTEGER},</if>
            <if test="feedbackNotes != null">feedback_notes = #{feedbackNotes,jdbcType=VARCHAR},</if>
            <if test="updateUser != null">update_user = #{updateUser,jdbcType=BIGINT},</if>
            update_time = CURRENT_TIMESTAMP
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="findByVersionAndTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_execution_logs
        WHERE template_id = #{templateId}
        AND tenant_id = #{tenantId}
        AND version_id = #{versionId}
        AND create_time BETWEEN #{startTime} AND #{endTime}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <select id="findByTemplateAndTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_execution_logs
        WHERE template_id = #{templateId}
        AND tenant_id = #{tenantId}
        AND create_time BETWEEN #{startTime} AND #{endTime}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <select id="findByTenantAndTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_execution_logs
        WHERE tenant_id = #{tenantId}
        AND create_time BETWEEN #{startTime} AND #{endTime}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <select id="calculateSuccessRate" resultType="Double">
        SELECT
            CASE
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE CAST(SUM(CASE WHEN feedback_status = 'SUCCESS' THEN 1 ELSE 0 END) AS DECIMAL) / COUNT(*) * 100
            END as success_rate
        FROM prompt_execution_logs
        WHERE template_id = #{templateId}
        AND tenant_id = #{tenantId}
        AND create_time BETWEEN #{startTime} AND #{endTime}
        AND deleted = 0
    </select>

    <select id="calculateAverageExecutionTime" resultType="Double">
        SELECT AVG(execution_time_ms) as avg_execution_time
        FROM prompt_execution_logs
        WHERE template_id = #{templateId}
        AND tenant_id = #{tenantId}
        AND feedback_status = 'SUCCESS'
        AND create_time BETWEEN #{startTime} AND #{endTime}
        AND deleted = 0
    </select>

</mapper>