<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.mapper.AiModelConfigMapper">

    <resultMap type="AiModelConfig" id="AiModelConfigResult">
        <id property="id" column="id"/>
        <result property="provider" column="provider"/>
        <result property="apiKey" column="api_key"/>
        <result property="baseUrl" column="base_url"/>
        <result property="chatModel" column="chat_model"/>
        <result property="embeddingModel" column="embedding_model"/>
        <result property="isEnabled" column="is_enabled"/>
    </resultMap>

    <select id="findByProviderAndIsEnabledTrue" resultMap="AiModelConfigResult">
        SELECT id, provider, api_key, base_url, chat_model, embedding_model, is_enabled
        FROM ai_model_config
        WHERE provider = #{provider} AND is_enabled = true
        LIMIT 1
    </select>

</mapper> 