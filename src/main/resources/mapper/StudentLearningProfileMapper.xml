<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.feedback.mapper.StudentLearningProfileMapper">

    <resultMap type="com.nybc.ai.feedback.entity.StudentLearningProfile" id="StudentLearningProfileResult">
        <id property="id" column="id"/>
        <result property="studentId" column="student_id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="learningStyle" column="learning_style"/>
        <result property="knowledgeLevel" column="knowledge_level"/>
        <result property="weakAreas" column="weak_areas"/>
        <result property="strongAreas" column="strong_areas"/>
        <result property="learningPreferences" column="learning_preferences"/>
        <result property="performanceTrends" column="performance_trends"/>
        <result property="lastAnalysisTime" column="last_analysis_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, student_id, tenant_id, learning_style, knowledge_level, weak_areas, strong_areas, 
        learning_preferences, performance_trends, last_analysis_time, create_time, update_time
    </sql>

    <select id="findByStudentAndTenant" resultMap="StudentLearningProfileResult">
        SELECT <include refid="Base_Column_List"/>
        FROM student_learning_profiles
        WHERE student_id = #{studentId} AND tenant_id = #{tenantId}
    </select>

    <insert id="insert" parameterType="com.nybc.ai.feedback.entity.StudentLearningProfile" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO student_learning_profiles(student_id, tenant_id, learning_style, knowledge_level, weak_areas, 
                                             strong_areas, learning_preferences, performance_trends, last_analysis_time, 
                                             create_time, update_time)
        VALUES (#{studentId}, #{tenantId}, #{learningStyle}, #{knowledgeLevel}, #{weakAreas}, #{strongAreas}, 
                #{learningPreferences}, #{performanceTrends}, #{lastAnalysisTime}, #{createTime}, #{updateTime})
    </insert>

    <update id="update" parameterType="com.nybc.ai.feedback.entity.StudentLearningProfile">
        UPDATE student_learning_profiles
        SET learning_style = #{learningStyle},
            knowledge_level = #{knowledgeLevel},
            weak_areas = #{weakAreas},
            strong_areas = #{strongAreas},
            learning_preferences = #{learningPreferences},
            performance_trends = #{performanceTrends},
            last_analysis_time = #{lastAnalysisTime},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="findByTenant" resultMap="StudentLearningProfileResult">
        SELECT <include refid="Base_Column_List"/>
        FROM student_learning_profiles
        WHERE tenant_id = #{tenantId}
        ORDER BY update_time DESC
    </select>

    <select id="findByLearningStyle" resultMap="StudentLearningProfileResult">
        SELECT <include refid="Base_Column_List"/>
        FROM student_learning_profiles
        WHERE tenant_id = #{tenantId} AND learning_style = #{learningStyle}
        ORDER BY update_time DESC
    </select>

    <select id="findByKnowledgeLevel" resultMap="StudentLearningProfileResult">
        SELECT <include refid="Base_Column_List"/>
        FROM student_learning_profiles
        WHERE tenant_id = #{tenantId} AND knowledge_level = #{knowledgeLevel}
        ORDER BY update_time DESC
    </select>

</mapper>
