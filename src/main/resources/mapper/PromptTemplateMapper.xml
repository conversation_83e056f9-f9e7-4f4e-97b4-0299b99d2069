<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.prompt.mapper.PromptTemplateMapper">

    <resultMap type="com.nybc.ai.domain.PromptTemplate" id="BaseResultMap">
        <id property="id" column="id"/>
        <result property="promptKey" column="prompt_key"/>
        <result property="promptName" column="prompt_name"/>
        <result property="promptContent" column="prompt_content"/>
        <result property="description" column="description"/>
        <result property="category" column="category"/>
        <result property="tags" column="tags"/>
        <result property="version" column="version"/>
        <result property="status" column="status"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, prompt_key, prompt_name, prompt_content, description, category, tags,
        version, status, tenant_id, create_time, update_time, create_user, update_user, deleted
    </sql>

    <insert id="insert" parameterType="com.nybc.ai.domain.PromptTemplate" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO prompt_templates(
            prompt_key, prompt_name, prompt_content, description, category, tags,
            version, status, tenant_id, create_time, update_time, create_user, update_user, deleted
        ) VALUES (
            #{promptKey}, #{promptName}, #{promptContent}, #{description}, #{category}, #{tags},
            #{version}, #{status}, #{tenantId}, #{createTime}, #{updateTime}, #{createUser}, #{updateUser}, #{deleted}
        )
    </insert>

    <select id="findById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_templates
        WHERE id = #{id} AND deleted = 0
    </select>

    <select id="findByKeyAndTenant" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_templates
        WHERE prompt_key = #{promptKey}
        AND tenant_id = #{tenantId}
        AND deleted = 0
        ORDER BY version DESC
        LIMIT 1
    </select>

    <update id="update" parameterType="com.nybc.ai.domain.PromptTemplate">
        UPDATE prompt_templates
        SET prompt_key = #{promptKey},
            prompt_name = #{promptName},
            prompt_content = #{promptContent},
            description = #{description},
            category = #{category},
            tags = #{tags},
            version = #{version},
            status = #{status},
            tenant_id = #{tenantId},
            update_time = #{updateTime},
            update_user = #{updateUser}
        WHERE id = #{id}
    </update>

    <update id="logicalDeleteById">
        UPDATE prompt_templates
        SET deleted = 1, update_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <select id="listByCriteria" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_templates
        <where>
            deleted = 0
            <if test="promptKey != null and promptKey != ''">
                AND prompt_key LIKE CONCAT('%', #{promptKey}, '%')
            </if>
            <if test="promptName != null and promptName != ''">
                AND prompt_name LIKE CONCAT('%', #{promptName}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="tenantId != null">
                AND tenant_id = #{tenantId}
            </if>
        </where>
        ORDER BY update_time DESC
    </select>

    <select id="findActiveTemplates" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_templates
        WHERE status = 'ACTIVE'
        AND deleted = 0
        ORDER BY update_time DESC
    </select>

    <select id="findActiveTemplatesByTenant" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_templates
        WHERE status = 'ACTIVE'
        AND tenant_id = #{tenantId}
        AND deleted = 0
        ORDER BY update_time DESC
    </select>

    <select id="findByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_templates
        WHERE status = #{status}
        AND deleted = 0
        ORDER BY update_time DESC
    </select>

    <select id="findTemplatesNeedingOptimization" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM prompt_templates pt
        WHERE pt.status = 'ACTIVE'
        AND pt.deleted = 0
        AND EXISTS (
            SELECT 1 FROM prompt_execution_logs pel
            WHERE pel.template_id = pt.id
            AND pel.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY pel.template_id
            HAVING COUNT(*) >= 10
            AND (AVG(CASE WHEN pel.feedback_status = 'SUCCESS' THEN 1.0 ELSE 0.0 END) < 0.8
                 OR AVG(pel.execution_time_ms) > 5000)
        )
        ORDER BY pt.update_time DESC
    </select>

</mapper>