<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.feedback.mapper.TeachingEffectivenessReportMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.nybc.ai.feedback.entity.TeachingEffectivenessReport">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="teacher_id" property="teacherId" jdbcType="BIGINT"/>
        <result column="assignment_id" property="assignmentId" jdbcType="BIGINT"/>
        <result column="report_type" property="reportType" jdbcType="VARCHAR"/>
        <result column="report_period" property="reportPeriod" jdbcType="VARCHAR"/>
        <result column="analysis_data" property="analysisData" jdbcType="VARCHAR"/>
        <result column="key_insights" property="keyInsights" jdbcType="VARCHAR"/>
        <result column="improvement_suggestions" property="improvementSuggestions" jdbcType="VARCHAR"/>
        <result column="performance_metrics" property="performanceMetrics" jdbcType="VARCHAR"/>
        <result column="student_feedback_summary" property="studentFeedbackSummary" jdbcType="VARCHAR"/>
        <result column="generate_time" property="generateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, tenant_id, teacher_id, assignment_id, report_type, report_period,
        analysis_data, key_insights, improvement_suggestions, performance_metrics,
        student_feedback_summary, generate_time, create_user
    </sql>

    <!-- 插入教学效果报告 -->
    <insert id="insert" parameterType="com.nybc.ai.feedback.entity.TeachingEffectivenessReport" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO teaching_effectiveness_reports (
            tenant_id, teacher_id, assignment_id, report_type, report_period,
            analysis_data, key_insights, improvement_suggestions, performance_metrics,
            student_feedback_summary, generate_time, create_user
        ) VALUES (
            #{tenantId}, #{teacherId}, #{assignmentId}, #{reportType}, #{reportPeriod},
            #{analysisData}, #{keyInsights}, #{improvementSuggestions}, #{performanceMetrics},
            #{studentFeedbackSummary}, #{generateTime}, #{createUser}
        )
    </insert>

    <!-- 根据ID查询报告 -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM teaching_effectiveness_reports
        WHERE id = #{id}
    </select>

    <!-- 查询历史报告 -->
    <select id="findHistoricalReports" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM teaching_effectiveness_reports
        WHERE tenant_id = #{tenantId}
          <if test="teacherId != null">
              AND teacher_id = #{teacherId}
          </if>
        ORDER BY generate_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据报告类型和周期查询报告 -->
    <select id="findReportsByTypeAndPeriod" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM teaching_effectiveness_reports
        WHERE tenant_id = #{tenantId}
          <if test="teacherId != null">
              AND teacher_id = #{teacherId}
          </if>
          AND report_type = #{reportType}
        ORDER BY generate_time DESC
        <if test="periodCount != null">
            LIMIT #{periodCount}
        </if>
    </select>

    <!-- 根据租户和时间范围查询报告 -->
    <select id="findByTenantAndTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM teaching_effectiveness_reports
        WHERE tenant_id = #{tenantId}
          AND generate_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY generate_time DESC
    </select>

    <!-- 根据作业ID查询报告 -->
    <select id="findByAssignment" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM teaching_effectiveness_reports
        WHERE assignment_id = #{assignmentId}
        ORDER BY generate_time DESC
    </select>

    <!-- 更新报告 -->
    <update id="update" parameterType="com.nybc.ai.feedback.entity.TeachingEffectivenessReport">
        UPDATE teaching_effectiveness_reports
        SET tenant_id = #{tenantId},
            teacher_id = #{teacherId},
            assignment_id = #{assignmentId},
            report_type = #{reportType},
            report_period = #{reportPeriod},
            analysis_data = #{analysisData},
            key_insights = #{keyInsights},
            improvement_suggestions = #{improvementSuggestions},
            performance_metrics = #{performanceMetrics},
            student_feedback_summary = #{studentFeedbackSummary},
            generate_time = #{generateTime},
            create_user = #{createUser}
        WHERE id = #{id}
    </update>

    <!-- 删除报告 -->
    <delete id="deleteById">
        DELETE FROM teaching_effectiveness_reports
        WHERE id = #{id}
    </delete>

</mapper>
