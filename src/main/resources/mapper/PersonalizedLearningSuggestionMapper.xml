<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nybc.ai.feedback.mapper.PersonalizedLearningSuggestionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.nybc.ai.feedback.entity.PersonalizedLearningSuggestion">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="student_id" property="studentId" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="suggestion_type" property="suggestionType" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="priority_level" property="priorityLevel" jdbcType="INTEGER"/>
        <result column="target_knowledge_points" property="targetKnowledgePoints" jdbcType="VARCHAR"/>
        <result column="suggested_resources" property="suggestedResources" jdbcType="VARCHAR"/>
        <result column="is_read" property="isRead" jdbcType="BOOLEAN"/>
        <result column="is_applied" property="isApplied" jdbcType="BOOLEAN"/>
        <result column="effectiveness_score" property="effectivenessScore" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, student_id, tenant_id, suggestion_type, title, content, priority_level,
        target_knowledge_points, suggested_resources, is_read, is_applied,
        effectiveness_score, create_time, expire_time
    </sql>

    <!-- 插入学习建议 -->
    <insert id="insert" parameterType="com.nybc.ai.feedback.entity.PersonalizedLearningSuggestion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO personalized_learning_suggestions (
            student_id, tenant_id, suggestion_type, title, content, priority_level,
            target_knowledge_points, suggested_resources, is_read, is_applied,
            effectiveness_score, create_time, expire_time
        ) VALUES (
            #{studentId}, #{tenantId}, #{suggestionType}, #{title}, #{content}, #{priorityLevel},
            #{targetKnowledgePoints}, #{suggestedResources}, #{isRead}, #{isApplied},
            #{effectivenessScore}, #{createTime}, #{expireTime}
        )
    </insert>

    <!-- 批量插入学习建议 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO personalized_learning_suggestions (
            student_id, tenant_id, suggestion_type, title, content, priority_level,
            target_knowledge_points, suggested_resources, is_read, is_applied,
            effectiveness_score, create_time, expire_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.studentId}, #{item.tenantId}, #{item.suggestionType}, #{item.title}, #{item.content}, #{item.priorityLevel},
             #{item.targetKnowledgePoints}, #{item.suggestedResources}, #{item.isRead}, #{item.isApplied},
             #{item.effectivenessScore}, #{item.createTime}, #{item.expireTime})
        </foreach>
    </insert>

    <!-- 根据学生ID和租户ID查询有效的学习建议 -->
    <select id="findActiveByStudent" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM personalized_learning_suggestions
        WHERE student_id = #{studentId}
          AND tenant_id = #{tenantId}
          AND (expire_time IS NULL OR expire_time > NOW())
        ORDER BY priority_level DESC, create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据建议类型查询学习建议 -->
    <select id="findByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM personalized_learning_suggestions
        WHERE student_id = #{studentId}
          AND tenant_id = #{tenantId}
          AND suggestion_type = #{suggestionType}
          AND (expire_time IS NULL OR expire_time > NOW())
        ORDER BY priority_level DESC, create_time DESC
    </select>

    <!-- 更新建议的阅读状态 -->
    <update id="updateReadStatus">
        UPDATE personalized_learning_suggestions
        SET is_read = #{isRead}
        WHERE id = #{id}
    </update>

    <!-- 更新建议的应用状态 -->
    <update id="updateAppliedStatus">
        UPDATE personalized_learning_suggestions
        SET is_applied = #{isApplied}
        WHERE id = #{id}
    </update>

    <!-- 更新建议的有效性评分 -->
    <update id="updateEffectivenessScore">
        UPDATE personalized_learning_suggestions
        SET effectiveness_score = #{effectivenessScore}
        WHERE id = #{id}
    </update>

    <!-- 删除过期的建议 -->
    <delete id="deleteExpired">
        DELETE FROM personalized_learning_suggestions
        WHERE expire_time IS NOT NULL
          AND expire_time &lt; #{expireTime}
    </delete>

    <!-- 根据优先级查询建议 -->
    <select id="findByPriority" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM personalized_learning_suggestions
        WHERE student_id = #{studentId}
          AND tenant_id = #{tenantId}
          AND priority_level = #{priorityLevel}
          AND (expire_time IS NULL OR expire_time > NOW())
        ORDER BY create_time DESC
    </select>

</mapper>
