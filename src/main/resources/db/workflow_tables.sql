-- =================================================================
-- 工作流编排相关表结构
-- =================================================================

-- 工作流模板表
CREATE TABLE workflow_templates (
    id BIGSERIAL PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    description TEXT COMMENT '模板描述',
    task_type VARCHAR(50) NOT NULL COMMENT '任务类型：HOMEWORK_EVALUATION, CODE_REVIEW, DOCUMENT_ANALYSIS, PROJECT_ASSESSMENT',
    workflow_type VARCHAR(50) NOT NULL COMMENT '工作流类型：SEQUENTIAL, PARALLEL, CONDITIONAL, HYBRID',
    chain_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'LiteFlow规则链名称',
    el_expression TEXT NOT NULL COMMENT 'LiteFlow EL表达式',
    nodes JSONB COMMENT '工作流节点配置',
    matching_condition JSONB COMMENT '任务匹配条件',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '模板版本',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认模板',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    priority INTEGER DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
    tenant_id BIGINT COMMENT '租户ID（为空表示公共模板）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user BIGINT COMMENT '创建人',
    update_user BIGINT COMMENT '更新人'
);

-- 创建索引
CREATE INDEX idx_workflow_templates_task_type ON workflow_templates(task_type);
CREATE INDEX idx_workflow_templates_tenant_id ON workflow_templates(tenant_id);
CREATE INDEX idx_workflow_templates_enabled ON workflow_templates(enabled);
CREATE INDEX idx_workflow_templates_priority ON workflow_templates(priority DESC);
CREATE UNIQUE INDEX idx_workflow_templates_name_tenant ON workflow_templates(template_name, tenant_id);

-- 任务匹配规则表
CREATE TABLE task_matching_rules (
    id BIGSERIAL PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    description TEXT COMMENT '规则描述',
    matching_condition JSONB NOT NULL COMMENT '匹配条件',
    target_template_id BIGINT NOT NULL COMMENT '目标工作流模板ID',
    priority INTEGER DEFAULT 0 COMMENT '规则优先级',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user BIGINT COMMENT '创建人',
    update_user BIGINT COMMENT '更新人',
    FOREIGN KEY (target_template_id) REFERENCES workflow_templates(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_task_matching_rules_tenant_id ON task_matching_rules(tenant_id);
CREATE INDEX idx_task_matching_rules_enabled ON task_matching_rules(enabled);
CREATE INDEX idx_task_matching_rules_priority ON task_matching_rules(priority DESC);
CREATE INDEX idx_task_matching_rules_template_id ON task_matching_rules(target_template_id);

-- 工作流执行记录表
CREATE TABLE workflow_execution_logs (
    id BIGSERIAL PRIMARY KEY,
    template_id BIGINT NOT NULL COMMENT '工作流模板ID',
    chain_name VARCHAR(100) NOT NULL COMMENT '执行的规则链名称',
    task_type VARCHAR(50) NOT NULL COMMENT '任务类型',
    task_context JSONB COMMENT '任务上下文',
    execution_status VARCHAR(20) NOT NULL COMMENT '执行状态：SUCCESS, FAILED, TIMEOUT',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    execution_time_ms BIGINT COMMENT '执行时间（毫秒）',
    error_message TEXT COMMENT '错误信息',
    result_data JSONB COMMENT '执行结果数据',
    tenant_id BIGINT COMMENT '租户ID',
    assignment_id BIGINT COMMENT '作业ID',
    student_id BIGINT COMMENT '学生ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (template_id) REFERENCES workflow_templates(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX idx_workflow_execution_logs_template_id ON workflow_execution_logs(template_id);
CREATE INDEX idx_workflow_execution_logs_task_type ON workflow_execution_logs(task_type);
CREATE INDEX idx_workflow_execution_logs_status ON workflow_execution_logs(execution_status);
CREATE INDEX idx_workflow_execution_logs_tenant_id ON workflow_execution_logs(tenant_id);
CREATE INDEX idx_workflow_execution_logs_start_time ON workflow_execution_logs(start_time);
CREATE INDEX idx_workflow_execution_logs_assignment_id ON workflow_execution_logs(assignment_id);

-- 工作流节点执行详情表
CREATE TABLE workflow_node_execution_details (
    id BIGSERIAL PRIMARY KEY,
    execution_log_id BIGINT NOT NULL COMMENT '工作流执行记录ID',
    node_id VARCHAR(100) NOT NULL COMMENT '节点ID',
    node_name VARCHAR(100) COMMENT '节点名称',
    component_name VARCHAR(100) COMMENT '组件名称',
    node_status VARCHAR(20) NOT NULL COMMENT '节点状态：SUCCESS, FAILED, SKIPPED',
    start_time TIMESTAMP NOT NULL COMMENT '节点开始时间',
    end_time TIMESTAMP COMMENT '节点结束时间',
    execution_time_ms BIGINT COMMENT '节点执行时间（毫秒）',
    input_data JSONB COMMENT '输入数据',
    output_data JSONB COMMENT '输出数据',
    error_message TEXT COMMENT '错误信息',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (execution_log_id) REFERENCES workflow_execution_logs(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_workflow_node_details_execution_id ON workflow_node_execution_details(execution_log_id);
CREATE INDEX idx_workflow_node_details_node_id ON workflow_node_execution_details(node_id);
CREATE INDEX idx_workflow_node_details_status ON workflow_node_execution_details(node_status);
CREATE INDEX idx_workflow_node_details_component ON workflow_node_execution_details(component_name);

-- 插入默认工作流模板
INSERT INTO workflow_templates (
    template_name, description, task_type, workflow_type, chain_name, el_expression,
    nodes, matching_condition, is_default, priority, tenant_id
) VALUES 
(
    '默认作业评估工作流',
    '适用于一般编程作业的评估工作流',
    'HOMEWORK_EVALUATION',
    'SEQUENTIAL',
    'default_homework_evaluation',
    'THEN(initContextCmp, syntaxCheckCmp, logicAnalysisCmp, finalScoreCmp)',
    '[
        {
            "nodeId": "init",
            "nodeName": "初始化上下文",
            "nodeType": "COMPONENT",
            "componentName": "initContextCmp",
            "required": true,
            "order": 1
        },
        {
            "nodeId": "syntax",
            "nodeName": "语法检查",
            "nodeType": "COMPONENT", 
            "componentName": "syntaxCheckCmp",
            "required": true,
            "order": 2
        },
        {
            "nodeId": "logic",
            "nodeName": "逻辑分析",
            "nodeType": "COMPONENT",
            "componentName": "logicAnalysisCmp", 
            "required": true,
            "order": 3
        },
        {
            "nodeId": "score",
            "nodeName": "最终评分",
            "nodeType": "COMPONENT",
            "componentName": "finalScoreCmp",
            "required": true,
            "order": 4
        }
    ]'::jsonb,
    '{
        "taskTypes": ["HOMEWORK_EVALUATION"],
        "fileTypes": ["java", "py", "js", "cpp"],
        "weight": 1.0
    }'::jsonb,
    true,
    100,
    null
),
(
    '代码审查工作流',
    '专门用于代码质量审查的工作流',
    'CODE_REVIEW',
    'PARALLEL',
    'code_review_workflow',
    'THEN(initContextCmp, WHEN(codeQualityCmp, securityCheckCmp, performanceAnalysisCmp), codeReviewSummaryCmp)',
    '[
        {
            "nodeId": "init",
            "nodeName": "初始化",
            "nodeType": "COMPONENT",
            "componentName": "initContextCmp",
            "required": true,
            "order": 1
        },
        {
            "nodeId": "parallel_analysis",
            "nodeName": "并行分析",
            "nodeType": "PARALLEL_GROUP",
            "required": true,
            "order": 2,
            "children": [
                {
                    "nodeId": "quality",
                    "nodeName": "代码质量检查",
                    "nodeType": "COMPONENT",
                    "componentName": "codeQualityCmp"
                },
                {
                    "nodeId": "security", 
                    "nodeName": "安全检查",
                    "nodeType": "COMPONENT",
                    "componentName": "securityCheckCmp"
                },
                {
                    "nodeId": "performance",
                    "nodeName": "性能分析",
                    "nodeType": "COMPONENT", 
                    "componentName": "performanceAnalysisCmp"
                }
            ]
        },
        {
            "nodeId": "summary",
            "nodeName": "审查总结",
            "nodeType": "COMPONENT",
            "componentName": "codeReviewSummaryCmp",
            "required": true,
            "order": 3
        }
    ]'::jsonb,
    '{
        "taskTypes": ["CODE_REVIEW"],
        "fileTypes": ["java", "py", "js", "cpp", "go"],
        "weight": 1.0
    }'::jsonb,
    true,
    90,
    null
);

-- 添加表注释
COMMENT ON TABLE workflow_templates IS '工作流模板表';
COMMENT ON TABLE task_matching_rules IS '任务匹配规则表';
COMMENT ON TABLE workflow_execution_logs IS '工作流执行记录表';
COMMENT ON TABLE workflow_node_execution_details IS '工作流节点执行详情表';
