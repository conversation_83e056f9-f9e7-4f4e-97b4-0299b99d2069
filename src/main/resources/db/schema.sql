-- 租户表: 用于隔离不同用户群体（如学校、学院）的规则
CREATE TABLE tenants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tenant_name VARCHAR(255) NOT NULL COMMENT '租户名称',
    description VARCHAR(255) COMMENT '描述'
);

-- 脚本组件表: 存储可由租户自定义的业务逻辑片段
CREATE TABLE script_components (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    application_name VARCHAR(255) NOT NULL COMMENT '应用名称, LiteFlow要求',
    script_id VARCHAR(255) NOT NULL UNIQUE COMMENT '脚本组件ID, 全局唯一',
    script_name VARCHAR(255) COMMENT '脚本可读名称',
    script_type VARCHAR(50) COMMENT '脚本语言类型 (e.g., groovy)',
    script_data CLOB COMMENT '脚本内容',
    tenant_id BIGINT COMMENT '所属租户ID, 为空则为公共脚本',
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

-- 规则链表: 定义业务流程的编排规则
CREATE TABLE rule_chains (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    application_name VARCHAR(255) NOT NULL COMMENT '应用名称, LiteFlow要求',
    chain_name VARCHAR(255) NOT NULL UNIQUE COMMENT '规则链ID, 全局唯一',
    el_data CLOB COMMENT '规则链的LiteFlow表达式语言(EL)内容',
    tenant_id BIGINT COMMENT '所属租户ID, 为空则为公共规则链',
    assignment_id BIGINT COMMENT '关联的作业ID, 用于更细粒度的规则'
);

-- 新增：AI模型配置表
CREATE TABLE ai_model_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    provider VARCHAR(50) NOT NULL UNIQUE COMMENT 'AI提供商 (e.g., zhipu, deepseek, qianwen)',
    api_key VARCHAR(255) NOT NULL COMMENT 'API Key',
    base_url VARCHAR(255) COMMENT 'API基础URL',
    chat_model VARCHAR(100) COMMENT '默认聊天模型',
    embedding_model VARCHAR(100) COMMENT '默认嵌入模型',
    is_enabled BOOLEAN DEFAULT true NOT NULL COMMENT '是否启用'
);

-- =================================================================
-- Prompt 工程模块 - 新增表结构
-- =================================================================

-- 存储Prompt的核心元数据
DROP TABLE IF EXISTS prompt_templates;
CREATE TABLE prompt_templates (
    id BIGSERIAL PRIMARY KEY,
    prompt_key VARCHAR(255) NOT NULL,
    tenant_id BIGINT NOT NULL,
    description TEXT,
    task_type VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
    active_version_id BIGINT,
    active_template_content TEXT,
    active_runtime_provider VARCHAR(100),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT NOT NULL,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0,
    UNIQUE (tenant_id, prompt_key)
);

-- 存储每个Prompt的所有历史版本
DROP TABLE IF EXISTS prompt_versions;
CREATE TABLE prompt_versions (
    id BIGSERIAL PRIMARY KEY,
    template_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,
    version_number INT NOT NULL,
    template_content TEXT NOT NULL,
    raw_idea_text TEXT,
    changelog TEXT,
    optimization_model VARCHAR(100),
    optimization_cot TEXT,
    runtime_provider VARCHAR(100),
    performance_metrics JSON,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT NOT NULL,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0,
    UNIQUE (template_id, version_number)
);

-- 记录每次Prompt的执行和反馈，用于评估和持续优化
DROP TABLE IF EXISTS prompt_execution_logs;
CREATE TABLE prompt_execution_logs (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255), -- 用于追踪一次完整的对话或业务流程
    template_id BIGINT NOT NULL,
    version_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,
    input_variables JSON, -- 记录填充模板的具体变量
    final_prompt TEXT, -- 最终发送给AI的完整Prompt
    ai_provider VARCHAR(100),
    raw_ai_response TEXT,
    parsed_ai_response TEXT, -- 如果对响应进行了解析，存储解析后的结果
    execution_time_ms BIGINT,
    feedback_status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, CORRECT, INCORRECT
    feedback_score INT, -- 1-5分，匹配度打分
    feedback_notes TEXT, -- 人工反馈的具体说明
    -- 审计字段
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT NOT NULL,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0
);

-- =================================================================
-- 智能反馈与闭环优化模块 - 新增表结构
-- =================================================================

-- 学生学习行为数据表
DROP TABLE IF EXISTS student_learning_behaviors;
CREATE TABLE student_learning_behaviors (
    id BIGSERIAL PRIMARY KEY,
    student_id BIGINT NOT NULL COMMENT '学生ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    assignment_id BIGINT COMMENT '作业ID',
    behavior_type VARCHAR(50) NOT NULL COMMENT '行为类型：SUBMIT, VIEW_FEEDBACK, ASK_QUESTION, REVISE',
    behavior_data JSON COMMENT '行为详细数据',
    session_id VARCHAR(255) COMMENT '会话ID',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_student_tenant (student_id, tenant_id),
    INDEX idx_assignment (assignment_id),
    INDEX idx_behavior_type (behavior_type),
    INDEX idx_create_time (create_time)
);

-- 学生个性化学习档案表
DROP TABLE IF EXISTS student_learning_profiles;
CREATE TABLE student_learning_profiles (
    id BIGSERIAL PRIMARY KEY,
    student_id BIGINT NOT NULL COMMENT '学生ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    learning_style VARCHAR(50) COMMENT '学习风格：VISUAL, AUDITORY, KINESTHETIC, READING',
    knowledge_level VARCHAR(50) COMMENT '知识水平：BEGINNER, INTERMEDIATE, ADVANCED',
    weak_areas JSON COMMENT '薄弱知识点列表',
    strong_areas JSON COMMENT '擅长知识点列表',
    learning_preferences JSON COMMENT '学习偏好设置',
    performance_trends JSON COMMENT '成绩趋势数据',
    last_analysis_time TIMESTAMP COMMENT '最后分析时间',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_student_tenant (student_id, tenant_id)
);

-- 个性化学习建议表
DROP TABLE IF EXISTS personalized_learning_suggestions;
CREATE TABLE personalized_learning_suggestions (
    id BIGSERIAL PRIMARY KEY,
    student_id BIGINT NOT NULL COMMENT '学生ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    suggestion_type VARCHAR(50) NOT NULL COMMENT '建议类型：KNOWLEDGE_GAP, STUDY_METHOD, RESOURCE_RECOMMEND',
    title VARCHAR(255) NOT NULL COMMENT '建议标题',
    content TEXT NOT NULL COMMENT '建议内容',
    priority_level INT DEFAULT 1 COMMENT '优先级：1-5，5最高',
    target_knowledge_points JSON COMMENT '目标知识点',
    suggested_resources JSON COMMENT '推荐学习资源',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    is_applied BOOLEAN DEFAULT FALSE COMMENT '是否已应用',
    effectiveness_score DOUBLE COMMENT '建议有效性评分',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expire_time TIMESTAMP COMMENT '建议过期时间',
    INDEX idx_student_tenant (student_id, tenant_id),
    INDEX idx_suggestion_type (suggestion_type),
    INDEX idx_priority (priority_level),
    INDEX idx_create_time (create_time)
);

-- 教学效果分析报告表
DROP TABLE IF EXISTS teaching_effectiveness_reports;
CREATE TABLE teaching_effectiveness_reports (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    teacher_id BIGINT COMMENT '教师ID',
    assignment_id BIGINT COMMENT '作业ID',
    report_type VARCHAR(50) NOT NULL COMMENT '报告类型：ASSIGNMENT, COURSE, SEMESTER',
    report_period VARCHAR(50) COMMENT '报告周期：WEEKLY, MONTHLY, SEMESTER',
    analysis_data JSON NOT NULL COMMENT '分析数据',
    key_insights JSON COMMENT '关键洞察',
    improvement_suggestions JSON COMMENT '改进建议',
    performance_metrics JSON COMMENT '性能指标',
    student_feedback_summary JSON COMMENT '学生反馈汇总',
    generate_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT NOT NULL,
    INDEX idx_tenant (tenant_id),
    INDEX idx_teacher (teacher_id),
    INDEX idx_assignment (assignment_id),
    INDEX idx_report_type (report_type),
    INDEX idx_generate_time (generate_time)
);

-- Prompt性能分析表
DROP TABLE IF EXISTS prompt_performance_analytics;
CREATE TABLE prompt_performance_analytics (
    id BIGSERIAL PRIMARY KEY,
    template_id BIGINT NOT NULL COMMENT 'Prompt模板ID',
    version_id BIGINT NOT NULL COMMENT 'Prompt版本ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    analysis_period VARCHAR(50) NOT NULL COMMENT '分析周期：DAILY, WEEKLY, MONTHLY',
    total_executions INT DEFAULT 0 COMMENT '总执行次数',
    success_rate DOUBLE DEFAULT 0.0 COMMENT '成功率',
    avg_execution_time DOUBLE DEFAULT 0.0 COMMENT '平均执行时间(ms)',
    avg_feedback_score DOUBLE COMMENT '平均反馈评分',
    positive_feedback_rate DOUBLE DEFAULT 0.0 COMMENT '正面反馈率',
    common_failure_patterns JSON COMMENT '常见失败模式',
    optimization_suggestions JSON COMMENT '优化建议',
    performance_trend JSON COMMENT '性能趋势数据',
    analysis_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_template_version (template_id, version_id),
    INDEX idx_tenant (tenant_id),
    INDEX idx_analysis_period (analysis_period),
    INDEX idx_analysis_time (analysis_time)
);