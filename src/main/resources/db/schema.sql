-- 租户表: 用于隔离不同用户群体（如学校、学院）的规则
CREATE TABLE tenants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    tenant_name VARCHAR(255) NOT NULL COMMENT '租户名称',
    description VARCHAR(255) COMMENT '描述'
);

-- 脚本组件表: 存储可由租户自定义的业务逻辑片段
CREATE TABLE script_components (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    application_name VARCHAR(255) NOT NULL COMMENT '应用名称, LiteFlow要求',
    script_id VARCHAR(255) NOT NULL UNIQUE COMMENT '脚本组件ID, 全局唯一',
    script_name VARCHAR(255) COMMENT '脚本可读名称',
    script_type VARCHAR(50) COMMENT '脚本语言类型 (e.g., groovy)',
    script_data CLOB COMMENT '脚本内容',
    tenant_id BIGINT COMMENT '所属租户ID, 为空则为公共脚本',
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

-- 规则链表: 定义业务流程的编排规则
CREATE TABLE rule_chains (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    application_name VARCHAR(255) NOT NULL COMMENT '应用名称, LiteFlow要求',
    chain_name VARCHAR(255) NOT NULL UNIQUE COMMENT '规则链ID, 全局唯一',
    el_data CLOB COMMENT '规则链的LiteFlow表达式语言(EL)内容',
    tenant_id BIGINT COMMENT '所属租户ID, 为空则为公共规则链',
    assignment_id BIGINT COMMENT '关联的作业ID, 用于更细粒度的规则'
);

-- 新增：AI模型配置表
CREATE TABLE ai_model_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    provider VARCHAR(50) NOT NULL UNIQUE COMMENT 'AI提供商 (e.g., zhipu, deepseek, qianwen)',
    api_key VARCHAR(255) NOT NULL COMMENT 'API Key',
    base_url VARCHAR(255) COMMENT 'API基础URL',
    chat_model VARCHAR(100) COMMENT '默认聊天模型',
    embedding_model VARCHAR(100) COMMENT '默认嵌入模型',
    is_enabled BOOLEAN DEFAULT true NOT NULL COMMENT '是否启用'
);

-- =================================================================
-- Prompt 工程模块 - 新增表结构
-- =================================================================

-- 存储Prompt的核心元数据
DROP TABLE IF EXISTS prompt_templates;
CREATE TABLE prompt_templates (
    id BIGSERIAL PRIMARY KEY,
    prompt_key VARCHAR(255) NOT NULL,
    tenant_id BIGINT NOT NULL,
    description TEXT,
    task_type VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
    active_version_id BIGINT,
    active_template_content TEXT,
    active_runtime_provider VARCHAR(100),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT NOT NULL,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0,
    UNIQUE (tenant_id, prompt_key)
);

-- 存储每个Prompt的所有历史版本
DROP TABLE IF EXISTS prompt_versions;
CREATE TABLE prompt_versions (
    id BIGSERIAL PRIMARY KEY,
    template_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,
    version_number INT NOT NULL,
    template_content TEXT NOT NULL,
    raw_idea_text TEXT,
    changelog TEXT,
    optimization_model VARCHAR(100),
    optimization_cot TEXT,
    runtime_provider VARCHAR(100),
    performance_metrics JSON,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT NOT NULL,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0,
    UNIQUE (template_id, version_number)
);

-- 记录每次Prompt的执行和反馈，用于评估和持续优化
DROP TABLE IF EXISTS prompt_execution_logs;
CREATE TABLE prompt_execution_logs (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255), -- 用于追踪一次完整的对话或业务流程
    template_id BIGINT NOT NULL,
    version_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,
    input_variables JSON, -- 记录填充模板的具体变量
    final_prompt TEXT, -- 最终发送给AI的完整Prompt
    ai_provider VARCHAR(100),
    raw_ai_response TEXT,
    parsed_ai_response TEXT, -- 如果对响应进行了解析，存储解析后的结果
    execution_time_ms BIGINT,
    feedback_status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, CORRECT, INCORRECT
    feedback_score INT, -- 1-5分，匹配度打分
    feedback_notes TEXT, -- 人工反馈的具体说明
    -- 审计字段
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT NOT NULL,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0
); 