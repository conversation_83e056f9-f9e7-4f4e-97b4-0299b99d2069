-- 插入租户
-- 1: 平台通用租户
-- 2: 一个具体的教师/学院租户，用于演示自定义规则
INSERT INTO tenants (id, tenant_name, description) VALUES (1, '平台默认租户', '平台提供的通用规则集');
INSERT INTO tenants (id, tenant_name, description) VALUES (2, '软件工程学院', '软件工程学院的特定评分规则');

-- 插入一个由"软件工程学院"租户自定义的Groovy脚本组件
-- 这个脚本组件将会在通用规则执行完毕后，执行一些学院特有的扣分逻辑
INSERT INTO script_components (application_name, script_id, script_name, script_type, script_data, tenant_id)
VALUES ('spring-ai-demo', 'customCodeScoreRule', '自定义代码评分规则', 'groovy',
'
// 这是一个Groovy脚本组件, 属于"软件工程学院"
def evalCtx = context.getEvaluationContext();
println("正在执行[软件工程学院]的自定义代码评分规则...");

// 规则：如果圈复杂度大于10，额外扣5分
if (evalCtx.getCyclomaticComplexity() != null && evalCtx.getCyclomaticComplexity() > 10.0) {
    evalCtx.setFinalScore(evalCtx.getFinalScore() - 5);
    evalCtx.getFeedbackMessages().add("[软件工程学院规则] 代码圈复杂度过高，已额外扣除5分。");
    evalCtx.getTriggeredRuleTags().add("tenant_custom_complexity_penalty");
}
', 2);

-- 插入平台通用的代码审核链
-- 这是一个基础流程，包含初始化、语法检查、抄袭检查和逻辑分析
INSERT INTO rule_chains (application_name, chain_name, el_data, tenant_id, assignment_id)
VALUES ('spring-ai-demo', 'commonCodeReviewChain',
'<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="commonCodeReviewChain">
        THEN(
            initContextCmp,
            syntaxCheckCmp,
            plagiarismCheckCmp,
            logicAnalysisCmp
        );
    </chain>
</flow>',
1, null);

-- 插入"软件工程学院"专属的代码审核链
-- 这个链首先执行通用的审核链，然后执行学院自己定义的脚本组件，实现了规则的继承与扩展
INSERT INTO rule_chains (application_name, chain_name, el_data, tenant_id, assignment_id)
VALUES ('spring-ai-demo', 'sweCodeReviewChain',
'<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="sweCodeReviewChain">
        THEN(
            commonCodeReviewChain,      -- 首先，执行平台通用链
            customCodeScoreRule         -- 然后，执行本学院的自定义脚本规则
        );
    </chain>
</flow>',
2, null);

-- 新增：一个用于"全栈项目"的复杂代码审核链
-- 这个链演示了条件路由(IF), 只有当编译成功(compilationSuccessful为true)时, 才执行后续的代码分析组件
INSERT INTO rule_chains (application_name, chain_name, el_data, tenant_id, assignment_id)
VALUES ('spring-ai-demo', 'fullStackProjectChain',
'<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="fullStackProjectChain">
        THEN(
            initContextCmp,
            codeCompileCmp,  -- 首先进行编译检查
            IF(
                context.isCompilationSuccessful(), -- IF表达式，判断上下文中的编译结果
                THEN(  -- 如果为true, 则执行代码分析流程
                    syntaxCheckCmp,
                    plagiarismCheckCmp,
                    logicAnalysisCmp,
                    dbDesignCheckCmp,
                    aiArchAnalysisCmp,
                    reqCompletionCheckCmp
                )
                -- 如果编译失败, 则不执行任何操作, 流程会自动结束, 因为前面编译组件设置了setIsEnd(true)
            )
        );
    </chain>
</flow>',
1, null);

-- 新增：一个专门用于"软件测试报告"作业的审核链
-- 这个链展示了如何为完全不同的业务场景组合一套独立的规则
INSERT INTO rule_chains (application_name, chain_name, el_data, tenant_id, assignment_id)
VALUES ('spring-ai-demo', 'softwareTestReportChain',
'<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="softwareTestReportChain">
        THEN(
            initContextCmp,
            testCaseCheckCmp,
            autoTestScriptCheckCmp
        );
    </chain>
</flow>',
1, null);

-- 新增：AI模型配置的初始数据 (默认禁用)
-- 请在启动后通过API或直接修改数据库来更新为您的真实Key并启用
INSERT INTO ai_model_config (provider, api_key, base_url, chat_model, embedding_model, is_enabled) VALUES
('deepseek', 'your_deepseek_api_key', null, 'deepseek-chat', null, false),
('qianwen', 'your_qianwen_api_key', 'https://dashscope.aliyuncs.com/api/v1', 'qwen-turbo', null, false),
('ollama', 'ollama', 'http://localhost:11434', 'llama3', 'llama3', false),
('openai', 'your_openai_api_key', 'https://api.openai.com', 'gpt-4o', 'text-embedding-3-small', false);

-- 存储Prompt的核心元数据，为运行时提供最高效的查询
CREATE TABLE prompt_templates (
    id BIGSERIAL PRIMARY KEY,
    prompt_key VARCHAR(255) NOT NULL, -- Prompt的业务唯一标识，如 "homework.logic.analysis"
    tenant_id BIGINT NOT NULL, -- 核心优化：租户ID，用于数据隔离
    description TEXT,
    task_type VARCHAR(100), -- 任务类型，如 "code-analysis", "text-summary"
    status VARCHAR(50) NOT NULL DEFAULT 'DRAFT', -- DRAFT, IN_REVIEW, ACTIVE, ARCHIVED
    active_version_id BIGINT, -- 指向 prompt_versions 表的活动版本ID
    -- 核心优化：冗余存储活动版本的核心内容，避免运行时JOIN
    active_template_content TEXT,
    created_by BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE (tenant_id, prompt_key) -- 确保在同一租户下prompt_key是唯一的
);

-- 存储每个Prompt的所有历史版本
CREATE TABLE prompt_versions (
    id BIGSERIAL PRIMARY KEY,
    template_id BIGINT NOT NULL REFERENCES prompt_templates(id),
    tenant_id BIGINT NOT NULL, -- 核心优化：租户ID
    version_number INT NOT NULL,
    template_content TEXT NOT NULL, -- 优化后的结构化Prompt内容
    raw_idea_text TEXT, -- 用户最初的、未经优化的想法
    changelog TEXT, -- AI生成的本次版本变更日志
    model_used VARCHAR(255), -- 创建此版本时使用的AI模型
    -- 建议：可将JSONB改为具体的指标列以方便查询分析
    performance_metrics JSONB, -- 存储性能指标，如 { "avg_latency_ms": 120, "success_rate": 0.99 }
    created_by BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    UNIQUE (template_id, version_number) -- 确保同一模板下的版本号唯一
);

-- 权限表（可选，但建议企业级应用增加）
CREATE TABLE prompt_template_permissions (
    id BIGSERIAL PRIMARY KEY,
    template_id BIGINT NOT NULL REFERENCES prompt_templates(id),
    user_id BIGINT,
    role_id BIGINT,
    -- e.g., 'OWNER', 'EDITOR', 'VIEWER'
    permission_level VARCHAR(50) NOT NULL,
    UNIQUE (template_id, user_id, role_id)
);
-- =================================================================
-- Prompt 工程模块 - 新增表结构
-- =================================================================
-- 存储Prompt的核心元数据，为运行时提供最高效的查询
DROP TABLE IF EXISTS prompt_templates;
CREATE TABLE prompt_templates (
    id BIGSERIAL PRIMARY KEY,
    prompt_key VARCHAR(255) NOT NULL, -- Prompt的业务唯一标识
    tenant_id BIGINT NOT NULL, -- 租户ID
    description TEXT,
    task_type VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'DRAFT', -- DRAFT, IN_REVIEW, ACTIVE, ARCHIVED
    active_version_id BIGINT,
    active_template_content TEXT,
    active_runtime_provider VARCHAR(100),
    -- 审计字段
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT NOT NULL,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0, -- 逻辑删除标记
    UNIQUE (tenant_id, prompt_key)
);

-- 存储每个Prompt的所有历史版本
DROP TABLE IF EXISTS prompt_versions;
CREATE TABLE prompt_versions (
    id BIGSERIAL PRIMARY KEY,
    template_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,
    version_number INT NOT NULL,
    template_content TEXT NOT NULL,
    raw_idea_text TEXT,
    changelog TEXT,
    optimization_model VARCHAR(100),
    optimization_cot TEXT,
    runtime_provider VARCHAR(100),
    performance_metrics JSON,
    -- 审计字段
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT NOT NULL,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0, -- 逻辑删除标记
    UNIQUE (template_id, version_number)
);
-- 记录每次Prompt的执行和反馈，用于评估和持续优化
DROP TABLE IF EXISTS prompt_execution_logs;
CREATE TABLE prompt_execution_logs (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255), -- 用于追踪一次完整的对话或业务流程
    template_id BIGINT NOT NULL,
    version_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,
    input_variables JSON, -- 记录填充模板的具体变量
    final_prompt TEXT, -- 最终发送给AI的完整Prompt
    ai_provider VARCHAR(100),
    raw_ai_response TEXT,
    parsed_ai_response TEXT, -- 如果对响应进行了解析，存储解析后的结果
    execution_time_ms BIGINT,
    feedback_status VARCHAR(50) DEFAULT 'PENDING', -- PENDING, CORRECT, INCORRECT
    feedback_score INT, -- 1-5分，匹配度打分
    feedback_notes TEXT, -- 人工反馈的具体说明
    -- 审计字段
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_user BIGINT NOT NULL,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user BIGINT,
    deleted INTEGER NOT NULL DEFAULT 0
);
-- =================================================================
-- 初始化系统核心Prompt - "Prompt优化器"
-- =================================================================

-- 1. 插入主模板记录
INSERT INTO prompt_templates (id, prompt_key, tenant_id, description, task_type, status, create_user, update_user, deleted)
VALUES (1, 'system.internal.prompt_optimizer', 0, '一个内部元Prompt，用于将用户的粗略想法优化成专业、高效、结构化的Prompt。', 'prompt-optimization', 'ACTIVE', 0, 0, 0);

-- 2. 插入V1版本内容
INSERT INTO prompt_versions (id, template_id, tenant_id, version_number, template_content, raw_idea_text, changelog, optimization_model, create_user, update_user, deleted)
VALUES (
           1,
           1,
           0,
           1,
           '# 角色与目标
       你是一位世界顶级的 AI Prompt 工程专家，你的任务是分析并优化用户提供的初步 Prompt 想法。你需要将一个模糊、低效的想法，转换成一个结构清晰、指令明确、能够引导 AI 模型（特别是像 DeepSeek 这样的代码和逻辑推理强模型）产出高质量结果的专业级 Prompt。

       # 核心指令
       你必须遵循以下步骤，并以指定的JSON格式输出结果：

       ## 第一步：深度分析用户意图
       - **识别目标 (Goal)**: 用户最终想用这个Prompt完成什么任务？（例如：代码生成、文本摘要、数据分析、角色扮演对话）
       - **识别核心需求 (Core Requirements)**: 用户输入的关键信息是什么？他希望输出包含哪些核心元素？
       - **识别上下文和约束 (Context and Constraints)**: 是否有隐含的背景信息、格式要求或限制条件？

       ## 第二步：应用高级Prompt优化策略
       在理解用户意图的基础上，运用你的专业知识，从以下策略中选择合适的组合来重构 Prompt：
       1.  **明确角色 (Assign a Role)**: 为 AI 分配一个具体的专家角色 (e.g., `你是一位资深的软件架构师...`)。
       2.  **提供背景 (Provide Context)**: 给出任务的详细背景信息，帮助 AI 理解场景。
       3.  **任务拆解 (Decomposition)**: 将复杂的任务分解为一步步清晰的指令。
       4.  **指定输出格式 (Specify Output Format)**: 要求 AI 以特定格式（如 JSON, Markdown, XML）输出，并给出明确的结构示例。
       5.  **提供示例 (Few-Shot Learning)**: 给出1-2个高质量的输入/输出示例，为 AI 的回答树立标杆。
       6.  **增加约束 (Add Constraints)**: 明确指出"应该做什么"和"不应该做什么"，减少无关输出。
       7.  **鼓励思考 (Chain-of-Thought Prompting)**: 引导 AI 在回答前先进行一步步的思考分析 (e.g., `请先思考...，然后...`)。

       ## 第三步：生成结构化输出
       你必须严格按照以下JSON格式返回你的分析和优化结果，不要有任何多余的解释性文字。

       ```json
       {
         "optimization_cot": "1. **意图分析**: [这里是你对用户原始想法的详细分析，说明你如何理解他的目标和需求]。\n2. **策略选择**: [这里说明你选择了哪些优化策略，以及为什么这些策略适用于这个场景]。\n3. **重构说明**: [这里简述你是如何将上述策略应用到原始想法中，形成了最终的Prompt模板]。",
         "changelog": "- [变更点1]\n- [变更点2]\n- [变更点3]",
         "template_content": "[这里放入你最终优化好的、可以直接使用的Prompt模板内容]"
       }
       ```

       # 用户输入
       用户的初步想法如下：
       `{{raw_idea_text}}`
       ',
           '系统内置初始版本',
           '初始版本，定义了Prompt优化流程和输出结构。',
           'deepseek-coder',
           0,
           0,
           0
       );


-- 3. 回填主表的活动版本信息
UPDATE prompt_templates
SET
    active_version_id = 1,
    active_template_content = (SELECT template_content FROM prompt_versions WHERE id = 1),
    active_runtime_provider = 'deepseek'
WHERE id = 1;