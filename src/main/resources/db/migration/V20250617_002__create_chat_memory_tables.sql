-- ===================================================================
-- Spring AI ChatMemory 数据库表结构 (PostgreSQL)
-- 用于存储聊天记忆和会话历史
-- ===================================================================

-- 聊天记忆表 - 存储会话ID和消息内容
CREATE TABLE IF NOT EXISTS chat_memory (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(255) NOT NULL,
    message_type VARCHAR(50) NOT NULL, -- 'USER' 或 'ASSISTANT'
    content TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_chat_memory_conversation_id ON chat_memory(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_memory_created_at ON chat_memory(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_memory_message_type ON chat_memory(message_type);

-- 聊天会话表 - 存储会话元信息
CREATE TABLE IF NOT EXISTS chat_sessions (
    id BIGSERIAL PRIMARY KEY,
    conversation_id VARCHAR(255) UNIQUE NOT NULL,
    user_id BIGINT,
    tenant_id BIGINT,
    session_name VARCHAR(255),
    session_description TEXT,
    status VARCHAR(50) DEFAULT 'ACTIVE', -- 'ACTIVE', 'ARCHIVED', 'DELETED'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_chat_sessions_conversation_id ON chat_sessions(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_tenant_id ON chat_sessions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_status ON chat_sessions(status);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_last_activity ON chat_sessions(last_activity_at);

-- 添加注释
COMMENT ON TABLE chat_memory IS '聊天记忆表 - 存储Spring AI ChatMemory的消息内容';
COMMENT ON COLUMN chat_memory.conversation_id IS '会话ID，用于关联同一个对话的所有消息';
COMMENT ON COLUMN chat_memory.message_type IS '消息类型：USER(用户消息) 或 ASSISTANT(AI助手消息)';
COMMENT ON COLUMN chat_memory.content IS '消息内容';
COMMENT ON COLUMN chat_memory.metadata IS '消息元数据，JSON格式存储额外信息';

COMMENT ON TABLE chat_sessions IS '聊天会话表 - 存储会话元信息';
COMMENT ON COLUMN chat_sessions.conversation_id IS '会话ID，与chat_memory表关联';
COMMENT ON COLUMN chat_sessions.user_id IS '用户ID，关联t_user表';
COMMENT ON COLUMN chat_sessions.tenant_id IS '租户ID，用于多租户隔离';
COMMENT ON COLUMN chat_sessions.session_name IS '会话名称';
COMMENT ON COLUMN chat_sessions.status IS '会话状态：ACTIVE(活跃), ARCHIVED(归档), DELETED(已删除)';

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为chat_memory表创建更新时间触发器
DROP TRIGGER IF EXISTS update_chat_memory_updated_at ON chat_memory;
CREATE TRIGGER update_chat_memory_updated_at
    BEFORE UPDATE ON chat_memory
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为chat_sessions表创建更新时间触发器
DROP TRIGGER IF EXISTS update_chat_sessions_updated_at ON chat_sessions;
CREATE TRIGGER update_chat_sessions_updated_at
    BEFORE UPDATE ON chat_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 插入示例数据（可选）
-- INSERT INTO chat_sessions (conversation_id, user_id, tenant_id, session_name, session_description) 
-- VALUES ('demo-session-001', 1, 1, '演示会话', '这是一个演示用的聊天会话');

-- INSERT INTO chat_memory (conversation_id, message_type, content, metadata) VALUES 
-- ('demo-session-001', 'USER', '你好，我想了解Spring AI的功能', '{"timestamp": "2025-06-17T21:00:00Z"}'),
-- ('demo-session-001', 'ASSISTANT', '您好！Spring AI是一个强大的AI集成框架，它提供了统一的API来集成各种AI服务...', '{"model": "deepseek-r1", "tokens": 150}');

-- 查询示例
-- 获取某个会话的所有消息（按时间排序）
-- SELECT conversation_id, message_type, content, created_at 
-- FROM chat_memory 
-- WHERE conversation_id = 'demo-session-001' 
-- ORDER BY created_at ASC;

-- 获取用户的所有活跃会话
-- SELECT cs.conversation_id, cs.session_name, cs.last_activity_at,
--        COUNT(cm.id) as message_count
-- FROM chat_sessions cs
-- LEFT JOIN chat_memory cm ON cs.conversation_id = cm.conversation_id
-- WHERE cs.user_id = 1 AND cs.status = 'ACTIVE'
-- GROUP BY cs.conversation_id, cs.session_name, cs.last_activity_at
-- ORDER BY cs.last_activity_at DESC;
