<?xml version="1.0" encoding="UTF-8"?>
<!-- 该xml配置中,xml元素大小写不敏感 -->
<!-- status="off",log4j2把自身事件记录到控制台的配置，off表示不记录，其余的记录有trace，info，list，warn，error，fatal -->
<!-- monitorInterval表示检测更改配置的时间,单位是秒,最小间隔为5秒,0或负数表示不检测 -->
<configuration monitorInterval="10" name="edu-log" status="info">

    <!-- 常量引用 -->
    <properties>
        <property name="LOG_HOME">${sys:user.home}/logs/nybc/edu</property>
        <property name="FORMAT">%d{HH:mm:ss.SSS} [%t] %-4level %log{15}[%L] - %status%n</property>
    </properties>
    <appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout
                    pattern="[%d{yyyy-MM-dd HH:mm:ss:SSS}][%thread][%-4level][%logger{36}][%L]: %msg%n"/>
        </Console>
        <RollingRandomAccessFile append="true" fileName="${LOG_HOME}/info.log"
                                 filePattern="${LOG_HOME}/info.%d{yyyy-MM-dd}.log.gz" name="info">
            <PatternLayout
                    pattern="[%d{yyyy-MM-dd HH:mm:ss:SSS}][%thread][%-4level][%logger{36}][%L]: %msg%n"/>
            <Filters>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile append="true" fileName="${LOG_HOME}/warn.log"
                                 filePattern="${LOG_HOME}/warn.%d{yyyy-MM-dd}.log.gz" name="warn">
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout
                    pattern="[%d{yyyy-MM-dd HH:mm:ss:SSS}][%thread][%-4level][%logger{36}][%L]: %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile append="true" fileName="${LOG_HOME}/error.log"
                                 filePattern="${LOG_HOME}/error.%d{yyyy-MM-dd}.log.gz" name="error">
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout
                    pattern="[%d{yyyy-MM-dd HH:mm:ss:SSS}][%thread][%-4level][%logger{36}][%L]: %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>
        <!-- synchronize -->
        <RollingRandomAccessFile append="true" fileName="${LOG_HOME}/synchronize.log"
                                 filePattern="${LOG_HOME}/synchronize.%d{yyyy-MM-dd}-%i.log.gz" name="synchronizeLog">
            <PatternLayout
                    pattern="[%d{yyyy-MM-dd HH:mm:ss:SSS}][%thread][%-4level][%logger{36}][%L]: %msg%n"/>
            <Filters>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingRandomAccessFile>
    </appenders>
    <!-- 接收appender -->
    <loggers>

        <Logger level="warn" name="org.gitlab4j.api"/>
        <Logger name="com.nybc.edu.ops" level="info"/>
        <!-- Spring -->
        <Logger level="info" name="org.springframework"/>
        <!-- mybatis loggers -->
        <Logger level="info" name="com.ibatis"/>
        <Logger level="info" name="com.ibatis.common.jdbc.SimpleDataSource"/>
        <Logger level="info" name="com.ibatis.common.jdbc.ScriptRunner"/>
        <Logger level="info" name="com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate"/>
        <!-- General Apache libraries -->
        <logger level="info" name="org.apache"/>
        <Root level="info">
            <appender-ref ref="Console"/>
            <appender-ref ref="info"/>
        </Root>
    </loggers>

</configuration>