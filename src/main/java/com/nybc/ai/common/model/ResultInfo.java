package com.nybc.ai.common.model;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 类描述：统一API响应结果封装
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
public class ResultInfo<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private boolean success;
    private int code;
    private String msg;
    private T data;

    // --- 构造函数 ---
    private ResultInfo() {}

    private ResultInfo(boolean success, int code, String msg, T data) {
        this.success = success;
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    // --- 静态成功响应方法 ---
    public static <T> ResultInfo<T> success(T data) {
        return new ResultInfo<>(true, 200, "操作成功", data);
    }

    public static <T> ResultInfo<T> success() {
        return success(null);
    }

    // --- 静态错误响应方法 ---
    public static <T> ResultInfo<T> error(int code, String msg) {
        return new ResultInfo<>(false, code, msg, null);
    }

    public static <T> ResultInfo<T> error(String msg) {
        return error(500, msg);
    }

    public static <T> ResultInfo<T> error() {
        return error(500, "系统内部错误，请联系管理员");
    }
} 