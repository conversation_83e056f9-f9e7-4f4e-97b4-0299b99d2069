// 文件路径: com/nybc/common/tool/json/JsonConfig.java
package com.nybc.ai.common;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.StreamReadConstraints;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 类描述：全局Jackson ObjectMapper配置
 *
 * <AUTHOR> 庆之
 * @version : 1.2 (重构并修正初始化顺序)
 **/
@Slf4j
public class JsonConfig {

    public static final ObjectMapper OBJECT_MAPPER;
    public static final TypeFactory TYPE_FACTORY;

    // 日期时间格式化器统一定义
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String TIME_FORMAT = "HH:mm:ss";

    static {
        // 1. 创建并配置 Builder
        JsonMapper.Builder builder = JsonMapper.builder()
                // === 基本特性配置 ===
                .enable(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS)
                .configure(JsonParser.Feature.ALLOW_COMMENTS, true)
                .configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true)

                // === 序列化特性 ===
                .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
                .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
                .configure(SerializationFeature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS, false)

                // === 反序列化特性 ===
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true)
                .configure(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS, false);

        // === 包含策略 ===
        // NON_EMPTY 表示只序列化非空（对于对象和集合）和非默认值（对于基本类型包装类）的属性
        builder.serializationInclusion(JsonInclude.Include.NON_EMPTY);

        // === 可见性配置 (核心：影响属性发现) ===
        // 推荐：优先使用标准的 public getter/setter 和可访问的构造函数/工厂方法
        builder.visibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.NONE);          // 首先禁用所有类型的自动检测
        builder.visibility(PropertyAccessor.GETTER, JsonAutoDetect.Visibility.PUBLIC_ONLY);  // 只自动检测 public getter
        builder.visibility(PropertyAccessor.SETTER, JsonAutoDetect.Visibility.PUBLIC_ONLY);  // 只自动检测 public setter
        builder.visibility(PropertyAccessor.CREATOR, JsonAutoDetect.Visibility.ANY);       // 自动检测任何可见性的构造函数和静态工厂方法
        // 如果您确实想通过字段直接访问（即使是 private），并且 LoginRequestPayload 没有 public setter，
        // 可以取消注释下面这行，但这通常不推荐，除非有特定原因。
        // builder.visibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);

        // === 注册标准和自定义模块 ===
        builder.addModule(new Jdk8Module());                   // 支持JDK 8特性，如 Optional, Stream 等
        builder.addModule(configureJavaTimeModule());        // 配置并注册JavaTimeModule，用于处理java.time包下的日期时间类型
        builder.addModule(configureLongToStringModule());    // 配置并注册自定义模块，用于将Long序列化为String，以及可能的自定义Long反序列化

        // 2. 构建 ObjectMapper 实例到一个临时变量
        ObjectMapper objectMapperInstance = builder.build();

        // 3. 在这个临时实例上进行后续的配置修改 (例如，禁用默认类型处理)
        objectMapperInstance.deactivateDefaultTyping(); // ★★★ 核心：禁用全局默认类型处理，防止寻找 @class 属性

        // === 其他高级配置 ===
        // 设置JSON处理字符长度限制 (通常不需要修改默认值，除非遇到超长字符串问题)
        objectMapperInstance.getFactory()
                .setStreamReadConstraints(StreamReadConstraints.builder().maxStringLength(Integer.MAX_VALUE).build());
        // 4. 将完全配置好的实例赋给静态常量
        OBJECT_MAPPER = objectMapperInstance;
        TYPE_FACTORY = objectMapperInstance.getTypeFactory();
    }

    private static JavaTimeModule configureJavaTimeModule() {
        JavaTimeModule javaTimeModule = new JavaTimeModule();

        DateTimeFormatter localDateTimeFormatter = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT);
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(localDateTimeFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(localDateTimeFormatter));

        DateTimeFormatter localDateFormatter = DateTimeFormatter.ofPattern(DATE_FORMAT);
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(localDateFormatter));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(localDateFormatter));

        DateTimeFormatter localTimeFormatter = DateTimeFormatter.ofPattern(TIME_FORMAT);
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(localTimeFormatter));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(localTimeFormatter));

        return javaTimeModule;
    }

    private static SimpleModule configureLongToStringModule() {
        SimpleModule longModule = new SimpleModule();
        // 将 Long 和 long 类型序列化为字符串
        longModule.addSerializer(Long.class, ToStringSerializer.instance);
        longModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        return longModule;
    }

}