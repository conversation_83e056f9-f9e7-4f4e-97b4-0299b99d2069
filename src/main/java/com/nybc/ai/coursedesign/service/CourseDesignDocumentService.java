package com.nybc.ai.coursedesign.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 类描述：课程设计文档专项处理服务
 * 专门处理课程设计中的各类文档：开题报告、设计文档、用户手册、总结报告等
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface CourseDesignDocumentService {

    /**
     * 开题报告智能分析
     * 检查：选题背景、技术路线、计划安排、可行性分析
     *
     * @param proposalDoc 开题报告文档
     * @param projectType 项目类型
     * @param schoolType  院校类型
     * @return 分析结果
     */
    Map<String, Object> analyzeProjectProposal(MultipartFile proposalDoc, String projectType, String schoolType);

    /**
     * 设计文档智能评审
     * 检查：系统架构、模块设计、数据库设计、接口设计
     *
     * @param designDoc   设计文档
     * @param projectType 项目类型
     * @return 评审结果
     */
    Map<String, Object> reviewDesignDocument(MultipartFile designDoc, String projectType);

    /**
     * 用户手册完整性检查
     * 检查：安装指南、使用说明、功能介绍、故障排除
     *
     * @param userManual  用户手册
     * @param projectType 项目类型
     * @return 检查结果
     */
    Map<String, Object> checkUserManual(MultipartFile userManual, String projectType);

    /**
     * 总结报告质量评估
     * 检查：个人贡献、技术总结、收获体会、问题反思
     *
     * @param summaryReport 总结报告
     * @param isGroupProject 是否为小组项目
     * @return 评估结果
     */
    Map<String, Object> evaluateSummaryReport(MultipartFile summaryReport, boolean isGroupProject);

    /**
     * 中期报告进度分析
     * 检查：进度完成情况、遇到的问题、解决方案、后续计划
     *
     * @param progressReport 中期报告
     * @param plannedProgress 计划进度
     * @return 分析结果
     */
    Map<String, Object> analyzeProgressReport(MultipartFile progressReport, Map<String, Object> plannedProgress);

    /**
     * PPT答辩材料评估
     * 检查：内容结构、技术展示、演示效果、时间控制
     *
     * @param presentationFile PPT文件
     * @param presentationTime 预计演示时间（分钟）
     * @return 评估结果
     */
    Map<String, Object> evaluatePresentationMaterial(MultipartFile presentationFile, Integer presentationTime);

    /**
     * 文档格式规范检查
     * 针对不同院校类型的格式要求进行检查
     *
     * @param document   文档文件
     * @param docType    文档类型
     * @param schoolType 院校类型
     * @return 格式检查结果
     */
    Map<String, Object> checkDocumentFormat(MultipartFile document, String docType, String schoolType);

    /**
     * 生成文档改进建议
     *
     * @param analysisResults 各文档的分析结果
     * @param projectStage    项目阶段
     * @return 改进建议
     */
    Map<String, Object> generateImprovementSuggestions(Map<String, Object> analysisResults, String projectStage);
}
