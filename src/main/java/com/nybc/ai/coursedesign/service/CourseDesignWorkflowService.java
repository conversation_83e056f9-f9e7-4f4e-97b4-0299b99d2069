package com.nybc.ai.coursedesign.service;

import com.nybc.ai.coursedesign.dto.CourseDesignStageRequest;
import com.nybc.ai.coursedesign.dto.CourseDesignStageResponse;
import com.nybc.ai.coursedesign.entity.CourseDesignProject;

import java.util.List;
import java.util.Map;

/**
 * 类描述：课程设计工作流服务接口
 * 支持课程设计全流程管理：前期准备、中期实施、后期验收
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface CourseDesignWorkflowService {

    /**
     * 创建课程设计项目
     *
     * @param tenantId    租户ID
     * @param teacherId   教师ID
     * @param projectInfo 项目信息
     * @return 创建的项目
     */
    CourseDesignProject createProject(Long tenantId, Long teacherId, Map<String, Object> projectInfo);

    /**
     * 前期准备阶段处理
     * 包括：选题指导、开题报告审核、技术方案评估
     *
     * @param request 阶段处理请求
     * @return 处理结果
     */
    CourseDesignStageResponse handlePreparationStage(CourseDesignStageRequest request);

    /**
     * 中期实施阶段处理
     * 包括：进度检查、中期报告评审、代码质量评估
     *
     * @param request 阶段处理请求
     * @return 处理结果
     */
    CourseDesignStageResponse handleImplementationStage(CourseDesignStageRequest request);

    /**
     * 后期验收阶段处理
     * 包括：最终成果评估、答辩材料审核、综合评分
     *
     * @param request 阶段处理请求
     * @return 处理结果
     */
    CourseDesignStageResponse handleValidationStage(CourseDesignStageRequest request);

    /**
     * 开题报告智能审核
     *
     * @param projectId      项目ID
     * @param proposalDoc    开题报告文档
     * @param evaluationCriteria 评估标准
     * @return 审核结果
     */
    Map<String, Object> reviewProjectProposal(Long projectId, byte[] proposalDoc, 
                                             Map<String, Object> evaluationCriteria);

    /**
     * 中期报告智能评估
     *
     * @param projectId    项目ID
     * @param progressDoc  中期报告文档
     * @param codeFiles    阶段性代码文件
     * @return 评估结果
     */
    Map<String, Object> evaluateProgressReport(Long projectId, byte[] progressDoc, 
                                              List<byte[]> codeFiles);

    /**
     * 最终成果综合评估
     *
     * @param projectId     项目ID
     * @param finalDocs     最终文档集合
     * @param sourceCode    完整源代码
     * @param presentationMaterials 答辩材料
     * @return 综合评估结果
     */
    Map<String, Object> evaluateFinalDeliverables(Long projectId, List<byte[]> finalDocs, 
                                                  byte[] sourceCode, byte[] presentationMaterials);

    /**
     * 生成阶段性指导建议
     *
     * @param projectId   项目ID
     * @param currentStage 当前阶段
     * @param studentLevel 学生水平
     * @param schoolType   院校类型：VOCATIONAL, UNDERGRADUATE, SOFTWARE_COLLEGE
     * @return 指导建议
     */
    List<String> generateStageGuidance(Long projectId, String currentStage, 
                                      String studentLevel, String schoolType);

    /**
     * 检查项目进度和里程碑
     *
     * @param projectId 项目ID
     * @return 进度检查结果
     */
    Map<String, Object> checkProjectProgress(Long projectId);

    /**
     * 生成项目质量报告
     *
     * @param projectId 项目ID
     * @return 质量报告
     */
    Map<String, Object> generateQualityReport(Long projectId);
}
