package com.nybc.ai.domain.mapper;

import com.nybc.ai.domain.entity.AiModelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 类描述：AI模型配置表数据库访问层
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Mapper
public interface AiModelConfigMapper {

    /**
     * 根据提供商名称查找启用的配置
     *
     * @param provider 提供商名称
     * @return AI模型配置
     */
    AiModelConfig findByProviderAndIsEnabledTrue(@Param("provider") String provider);
} 