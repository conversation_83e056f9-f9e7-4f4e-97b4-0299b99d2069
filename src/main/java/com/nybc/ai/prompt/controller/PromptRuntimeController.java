package com.nybc.ai.prompt.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.prompt.dto.PromptExecuteRequest;
import com.nybc.ai.prompt.dto.PromptExecuteResponse;
import com.nybc.ai.prompt.dto.PromptFeedbackRequest;
import com.nybc.ai.service.PromptRuntimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类描述：Prompt运行时相关的API接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/prompt/runtime")
@Tag(name = "Prompt 运行时服务", description = "提供Prompt的执行、追踪和反馈能力")
public class PromptRuntimeController {

    @Resource
    private PromptRuntimeService promptRuntimeService;

    @PostMapping("/execute")
    @Operation(summary = "执行Prompt")
    public ResultInfo<PromptExecuteResponse> execute(@Valid @RequestBody PromptExecuteRequest request) {
        try {
            PromptExecuteResponse response = promptRuntimeService.execute(request);
            return ResultInfo.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("执行Prompt失败，参数或服务配置错误: {}", e.getMessage());
            return ResultInfo.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("执行Prompt时发生未知错误", e);
            return ResultInfo.error(500, "执行Prompt失败，请联系管理员");
        }
    }

    @PostMapping("/feedback")
    @Operation(summary = "提交人工反馈")
    public ResultInfo<Void> submitFeedback(@Valid @RequestBody PromptFeedbackRequest request) {
        try {
            promptRuntimeService.submitFeedback(request);
            return ResultInfo.success();
        } catch (IllegalArgumentException e) {
            log.warn("提交反馈失败，参数错误: {}", e.getMessage());
            return ResultInfo.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("提交反馈时发生未知错误", e);
            return ResultInfo.error(500, "提交反馈失败，请联系管理员");
        }
    }
} 