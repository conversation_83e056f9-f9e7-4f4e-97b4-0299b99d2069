package com.nybc.ai.prompt.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.prompt.dto.PromptTemplateCreateRequest;
import com.nybc.ai.prompt.dto.PromptTemplateDetailRequest;
import com.nybc.ai.prompt.dto.PromptTemplateDetailResponse;
import com.nybc.ai.prompt.dto.PromptOptimizeRequest;
import com.nybc.ai.prompt.dto.PromptVersionResponse;
import com.nybc.ai.prompt.dto.PromptOptimizeWithFeedbackRequest;
import com.nybc.ai.service.PromptMgmtService;
import com.nybc.ai.service.PromptOptimizationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 类描述：Prompt管理相关的API接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/prompt/mgmt")
@Tag(name = "Prompt 工程化管理", description = "提供Prompt的创建、版本控制、发布等管理功能")
public class PromptMgmtController {

    @Resource
    private PromptMgmtService promptMgmtService;

    @Resource
    private PromptOptimizationService promptOptimizationService;

    /**
     * 创建一个新的Prompt模板及其初始版本
     *
     * @param request 创建请求数据
     * @return 包含新创建的模板和版本信息的响应
     */
    @PostMapping("/create")
    @Operation(summary = "创建Prompt模板")
    public ResultInfo<PromptTemplateDetailResponse> createPromptTemplate(@Valid @RequestBody PromptTemplateCreateRequest request) {
        try {
            PromptTemplateDetailResponse response = promptMgmtService.createPromptTemplate(request);
            return ResultInfo.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("创建Prompt模板失败，参数校验错误: {}", e.getMessage());
            return ResultInfo.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("创建Prompt模板时发生未知错误", e);
            return ResultInfo.error(500, "创建Prompt模板失败，请联系管理员");
        }
    }

    /**
     * 获取Prompt模板的详细信息，包括其所有版本
     *
     * @param request 包含模板ID的请求
     * @return 模板详情响应
     */
    @PostMapping("/detail")
    @Operation(summary = "获取Prompt模板详情")
    public ResultInfo<PromptTemplateDetailResponse> getPromptTemplateDetail(@Valid @RequestBody PromptTemplateDetailRequest request) {
        try {
            PromptTemplateDetailResponse response = promptMgmtService.getPromptTemplateDetail(request.getTemplateId());
            if (response == null) {
                return ResultInfo.error(404, "找不到指定的Prompt模板");
            }
            return ResultInfo.success(response);
        } catch (Exception e) {
            log.error("获取Prompt模板详情时发生未知错误", e);
            return ResultInfo.error(500, "获取模板详情失败，请联系管理员");
        }
    }

    /**
     * 调用AI优化一个Prompt并创建新版本
     *
     * @param request 优化请求
     * @return 新创建的版本信息
     */
    @PostMapping("/optimize-version")
    @Operation(summary = "AI优化并创建新版本")
    public ResultInfo<PromptVersionResponse> optimizeVersion(@Valid @RequestBody PromptOptimizeRequest request) {
        try {
            PromptVersionResponse response = promptOptimizationService.optimizeAndCreateVersion(
                    request.getTemplateId(),
                    request.getRawIdeaText(),
                    request.getOperatorId(),
                    request.getProviderName()
            );
            return ResultInfo.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("优化Prompt失败，参数或服务配置错误: {}", e.getMessage());
            return ResultInfo.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("优化Prompt时发生未知错误", e);
            return ResultInfo.error(500, "优化Prompt失败，请联系管理员");
        }
    }

    /**
     * 基于人工反馈，调用AI进行再次优化并创建新版本
     *
     * @param request 包含执行日志ID的请求
     * @return 新创建的版本信息
     */
    @PostMapping("/optimize-with-feedback")
    @Operation(summary = "基于反馈进行AI优化")
    public ResultInfo<PromptVersionResponse> optimizeWithFeedback(@Valid @RequestBody PromptOptimizeWithFeedbackRequest request) {
        try {
            PromptVersionResponse response = promptOptimizationService.optimizeWithFeedback(request);
            return ResultInfo.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("基于反馈优化Prompt失败，参数或服务配置错误: {}", e.getMessage());
            return ResultInfo.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("基于反馈优化Prompt时发生未知错误", e);
            return ResultInfo.error(500, "优化Prompt失败，请联系管理员");
        }
    }
} 