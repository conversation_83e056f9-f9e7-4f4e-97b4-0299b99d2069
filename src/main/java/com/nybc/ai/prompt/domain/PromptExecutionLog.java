package com.nybc.ai.prompt.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类描述：对应 prompt_execution_logs 表的领域对象
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
public class PromptExecutionLog implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String sessionId;
    private Long templateId;
    private Long versionId;
    private Long tenantId;
    private String inputVariables; // JSON
    private String finalPrompt;
    private String aiProvider;
    private String rawAiResponse;
    private String parsedAiResponse;
    private Long executionTimeMs;
    private String feedbackStatus;
    private Integer feedbackScore;
    private String feedbackNotes;
    private LocalDateTime createTime;
    private Long createUser;
    private LocalDateTime updateTime;
    private Long updateUser;
    private Integer deleted;
} 