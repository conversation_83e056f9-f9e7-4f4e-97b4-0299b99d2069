package com.nybc.ai.prompt.domain;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Prompt 版本历史表对应的领域对象
 *
 * <AUTHOR>
 */
@Data
public class PromptVersion implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long templateId;
    private Long tenantId;
    private Integer versionNumber;
    private String templateContent;
    private String rawIdeaText;
    private String changelog;
    private String optimizationModel;
    private String optimizationCot;
    private String runtimeProvider;
    private String performanceMetrics; // 作为JSON字符串处理
    private LocalDateTime createTime;
    private Long createUser;
    private LocalDateTime updateTime;
    private Long updateUser;
    private Integer deleted;
} 