package com.nybc.ai.prompt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 类描述：提交人工反馈的请求体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "提交人工反馈的请求体")
public class PromptFeedbackRequest {

    @NotNull(message = "执行日志ID不能为空")
    @Schema(description = "需要反馈的执行日志ID (prompt_execution_logs.id)")
    private Long executionLogId;

    @NotBlank(message = "反馈状态不能为空")
    @Schema(description = "反馈状态，'CORRECT' 或 'INCORRECT'", example = "CORRECT")
    private String feedbackStatus;

    @NotNull(message = "反馈评分不能为空")
    @Min(value = 1, message = "评分最低为1")
    @Max(value = 5, message = "评分最高为5")
    @Schema(description = "匹配度评分 (1-5)", example = "5")
    private Integer feedbackScore;

    @Schema(description = "具体的反馈意见和说明")
    private String feedbackNotes;

    @NotNull(message = "反馈人ID不能为空")
    @Schema(description = "提交反馈的操作人ID")
    private Long userId;
} 