package com.nybc.ai.prompt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 类描述：请求AI优化Prompt并创建新版本的请求体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "请求AI优化Prompt并创建新版本的请求体")
public class PromptOptimizeRequest {

    @NotNull(message = "模板ID不能为空")
    @Schema(description = "要为其创建新版本的模板ID", required = true)
    private Long templateId;

    @NotBlank(message = "原始想法不能为空")
    @Schema(description = "需要被优化的、粗糙的Prompt想法或指令", required = true)
    private String rawIdeaText;
    
    @NotNull(message = "操作人ID不能为空")
    @Schema(description = "操作人ID", required = true)
    private Long operatorId;

    @NotBlank(message = "必须指定一个AI Provider")
    @Schema(description = "用于优化的AI Provider名称，如 'deepseek'", required = true, defaultValue = "deepseek")
    private String providerName = "deepseek";
} 