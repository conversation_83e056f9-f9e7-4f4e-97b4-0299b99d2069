package com.nybc.ai.prompt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 类描述：执行Prompt的响应体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "执行Prompt的响应体")
public class PromptExecuteResponse {

    @Schema(description = "本次执行的日志ID，用于后续提交反馈")
    private Long executionLogId;

    @Schema(description = "AI返回并经过解析后的内容")
    private String responseContent;

    @Schema(description = "本次执行的会话ID")
    private String sessionId;
} 