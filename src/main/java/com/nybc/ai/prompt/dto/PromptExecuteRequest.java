package com.nybc.ai.prompt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;
import java.util.UUID;

/**
 * 类描述：执行Prompt的请求体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "执行Prompt的请求体")
public class PromptExecuteRequest {

    @NotBlank(message = "Prompt业务主键不能为空")
    @Schema(description = "要执行的Prompt业务唯一标识")
    private String promptKey;

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "用于填充模板的变量键值对")
    private Map<String, Object> inputVariables;

    @NotNull(message = "执行人ID不能为空")
    @Schema(description = "执行人ID")
    private Long userId;

    @Schema(description = "会话ID，用于追踪同一轮对话或业务流，不传则自动生成")
    private String sessionId = UUID.randomUUID().toString();
    
    @Schema(description = "指定用于本次执行的AI Provider，如'deepseek'。如果为空，则使用模板中建议的Provider")
    private String providerName;
} 