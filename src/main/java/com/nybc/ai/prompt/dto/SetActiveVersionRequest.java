package com.nybc.ai.prompt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 类描述：设置模板活动版本的请求体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "设置模板活动版本的请求体")
public class SetActiveVersionRequest {

    @NotNull(message = "要更新的模板ID不能为空")
    @Schema(description = "要设置活动版本的模板ID")
    private Long templateId;

    @NotNull(message = "活动版本ID不能为空")
    @Schema(description = "要被激活的 prompt_versions 表记录ID")
    private Long versionId;

    @NotNull(message = "更新人ID不能为空")
    @Schema(description = "更新人ID，后续应从登录态中自动获取")
    private Long updateUser;
} 