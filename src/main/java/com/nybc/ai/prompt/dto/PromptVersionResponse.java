package com.nybc.ai.prompt.dto;

import com.nybc.ai.domain.PromptVersion;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * 类描述：Prompt版本历史的响应对象
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "Prompt版本历史的响应对象")
public class PromptVersionResponse {
    
    private Long id;
    private Long templateId;
    private Long tenantId;
    private Integer versionNumber;
    private String templateContent;
    private String rawIdeaText;
    private String changelog;
    private String optimizationModel;
    private String optimizationCot;
    private String runtimeProvider;
    private String performanceMetrics;
    private LocalDateTime createTime;
    private Long createUser;
    private LocalDateTime updateTime;
    private Long updateUser;

    /**
     * 从领域模型转换为DTO
     * @param domainObject PromptVersion
     * @return PromptVersionResponse
     */
    public static PromptVersionResponse fromDomain(PromptVersion domainObject) {
        if (domainObject == null) {
            return null;
        }
        PromptVersionResponse response = new PromptVersionResponse();
        BeanUtils.copyProperties(domainObject, response);
        return response;
    }
} 