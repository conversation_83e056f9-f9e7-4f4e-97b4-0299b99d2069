package com.nybc.ai.prompt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 类描述：创建Prompt模板的请求体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "创建Prompt模板的请求体")
public class PromptTemplateCreateRequest {

    @NotBlank(message = "Prompt业务主键不能为空")
    @Schema(description = "Prompt业务唯一标识，如 'order.risk.check'")
    private String promptKey;

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "Prompt功能描述")
    private String description;

    @Schema(description = "任务类型，如 'text-summary', 'code-generation'")
    private String taskType;

    @NotBlank(message = "初始模板内容不能为空")
    @Schema(description = "初始版本的模板内容")
    private String templateContent;

    @NotNull(message = "创建人ID不能为空")
    @Schema(description = "创建人ID，后续应从登录态中自动获取")
    private Long createUser;
} 