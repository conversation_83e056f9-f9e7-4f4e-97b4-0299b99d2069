package com.nybc.ai.prompt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 类描述：为Prompt模板创建新版本的请求体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "为Prompt模板创建新版本的请求体")
public class PromptVersionCreateRequest {

    @NotNull(message = "模板ID不能为空")
    @Schema(description = "所属的Prompt模板ID")
    private Long templateId;

    @NotBlank(message = "模板内容不能为空")
    @Schema(description = "新版本的模板内容")
    private String templateContent;

    @Schema(description = "版本变更日志，说明本次优化的内容")
    private String changelog;

    @NotNull(message = "创建人ID不能为空")
    @Schema(description = "创建人ID，后续应从登录态中自动获取")
    private Long createUser;
} 