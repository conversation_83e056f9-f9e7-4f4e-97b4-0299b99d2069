package com.nybc.ai.prompt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 类描述：获取Prompt模板详情的请求体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "获取Prompt模板详情的请求体")
public class PromptTemplateDetailRequest {
    
    @NotNull(message = "模板ID不能为空")
    @Schema(description = "要查询的Prompt模板ID")
    private Long templateId;
} 