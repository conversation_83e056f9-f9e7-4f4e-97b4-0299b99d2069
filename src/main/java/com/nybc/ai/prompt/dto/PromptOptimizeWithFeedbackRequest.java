package com.nybc.ai.prompt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 类描述：基于人工反馈请求AI优化并创建新版本的请求体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "基于人工反馈请求AI优化并创建新版本的请求体")
public class PromptOptimizeWithFeedbackRequest {

    @NotNull(message = "执行日志ID不能为空")
    @Schema(description = "需要进行优化迭代的、已提交反馈的执行日志ID", required = true)
    private Long executionLogId;

    @NotNull(message = "操作人ID不能为空")
    @Schema(description = "操作人ID", required = true)
    private Long operatorId;

    @NotBlank(message = "必须指定一个AI Provider")
    @Schema(description = "用于优化的AI Provider名称，如 'deepseek'", required = true, defaultValue = "deepseek")
    private String providerName = "deepseek";
} 