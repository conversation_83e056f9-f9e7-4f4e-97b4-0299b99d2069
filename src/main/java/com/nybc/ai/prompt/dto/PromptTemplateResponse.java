package com.nybc.ai.prompt.dto;

import com.nybc.ai.domain.PromptTemplate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * 类描述：Prompt模板的响应对象
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "Prompt模板的响应对象")
public class PromptTemplateResponse {

    private Long id;
    private String promptKey;
    private Long tenantId;
    private String description;
    private String taskType;
    private String status;
    private Long activeVersionId;
    private String activeTemplateContent;
    private String activeRuntimeProvider;
    private LocalDateTime createTime;
    private Long createUser;
    private LocalDateTime updateTime;
    private Long updateUser;

    /**
     * 从领域模型转换为DTO
     * @param domainObject PromptTemplate
     * @return PromptTemplateResponse
     */
    public static PromptTemplateResponse fromDomain(PromptTemplate domainObject) {
        if (domainObject == null) {
            return null;
        }
        PromptTemplateResponse response = new PromptTemplateResponse();
        BeanUtils.copyProperties(domainObject, response);
        return response;
    }
} 