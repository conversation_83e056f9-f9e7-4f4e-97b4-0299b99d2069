package com.nybc.ai.prompt.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 类描述：Prompt模板详情响应，包含所有版本历史
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Prompt模板详情响应，包含所有版本历史")
public class PromptTemplateDetailResponse extends PromptTemplateResponse {

    @Schema(description = "该模板下的所有版本历史记录")
    private List<PromptVersionResponse> versions;
} 