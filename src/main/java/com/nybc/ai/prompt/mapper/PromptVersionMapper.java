package com.nybc.ai.prompt.mapper;

import com.nybc.ai.domain.PromptVersion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * PromptVersion 表的MyBatis Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PromptVersionMapper {

    int insert(PromptVersion record);

    PromptVersion findById(Long id);

    List<PromptVersion> findByTemplateId(@Param("templateId") Long templateId);

    int update(PromptVersion record);
    
    int logicalDeleteByTemplateId(Long templateId);
} 