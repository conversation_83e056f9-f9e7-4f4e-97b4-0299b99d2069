package com.nybc.ai.prompt.mapper;

import com.nybc.ai.prompt.domain.PromptTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * PromptTemplate 表的MyBatis Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PromptTemplateMapper {

    int insert(PromptTemplate record);

    PromptTemplate findById(Long id);

    PromptTemplate findByKeyAndTenant(@Param("promptKey") String promptKey, @Param("tenantId") Long tenantId);
    
    int update(PromptTemplate record);

    int logicalDeleteById(Long id);

    List<PromptTemplate> listByCriteria(PromptTemplate criteria);

    /**
     * 查询所有活跃的Prompt模板
     *
     * @return 活跃的Prompt模板列表
     */
    List<PromptTemplate> findActiveTemplates();

    /**
     * 根据租户查询活跃的Prompt模板
     *
     * @param tenantId 租户ID
     * @return 活跃的Prompt模板列表
     */
    List<PromptTemplate> findActiveTemplatesByTenant(@Param("tenantId") Long tenantId);

    /**
     * 根据状态查询Prompt模板
     *
     * @param status 状态
     * @return Prompt模板列表
     */
    List<PromptTemplate> findByStatus(@Param("status") String status);

    /**
     * 查询需要优化的Prompt模板
     *
     * @return 需要优化的Prompt模板列表
     */
    List<PromptTemplate> findTemplatesNeedingOptimization();
}