package com.nybc.ai.prompt.mapper;

import com.nybc.ai.prompt.domain.PromptTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * PromptTemplate 表的MyBatis Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PromptTemplateMapper {

    int insert(PromptTemplate record);

    PromptTemplate findById(Long id);

    PromptTemplate findByKeyAndTenant(@Param("promptKey") String promptKey, @Param("tenantId") Long tenantId);
    
    int update(PromptTemplate record);

    int logicalDeleteById(Long id);

    List<PromptTemplate> listByCriteria(PromptTemplate criteria);
} 