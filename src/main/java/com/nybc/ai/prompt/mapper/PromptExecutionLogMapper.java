package com.nybc.ai.prompt.mapper;

import com.nybc.ai.prompt.domain.PromptExecutionLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PromptExecutionLog 表的MyBatis Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface PromptExecutionLogMapper {

    int insert(PromptExecutionLog record);

    PromptExecutionLog findById(Long id);

    int update(PromptExecutionLog record);

    /**
     * 根据版本和时间范围查询执行日志
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @param versionId  版本ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 执行日志列表
     */
    List<PromptExecutionLog> findByVersionAndTimeRange(@Param("templateId") Long templateId,
                                                       @Param("tenantId") Long tenantId,
                                                       @Param("versionId") Long versionId,
                                                       @Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 根据模板ID和时间范围查询执行日志
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 执行日志列表
     */
    List<PromptExecutionLog> findByTemplateAndTimeRange(@Param("templateId") Long templateId,
                                                        @Param("tenantId") Long tenantId,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 根据租户ID和时间范围查询执行日志
     *
     * @param tenantId  租户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 执行日志列表
     */
    List<PromptExecutionLog> findByTenantAndTimeRange(@Param("tenantId") Long tenantId,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 统计执行成功率
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 成功率统计
     */
    Double calculateSuccessRate(@Param("templateId") Long templateId,
                               @Param("tenantId") Long tenantId,
                               @Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime);

    /**
     * 统计平均执行时间
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 平均执行时间（毫秒）
     */
    Double calculateAverageExecutionTime(@Param("templateId") Long templateId,
                                        @Param("tenantId") Long tenantId,
                                        @Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);
}