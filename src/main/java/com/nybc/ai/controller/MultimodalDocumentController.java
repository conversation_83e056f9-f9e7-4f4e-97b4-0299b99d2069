package com.nybc.ai.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.service.MultimodalDocumentService;
import com.nybc.ai.service.dto.DocumentStructure;
import com.nybc.ai.service.dto.MultimodalDocumentAnalysis;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 类描述：多模态文档处理控制器
 * 支持文字、表格、图片、代码片段等多种内容类型的智能处理
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/ai/multimodal-document")
@Tag(name = "多模态文档处理", description = "支持PDF、Office文档中文字、表格、图片、代码片段的智能分析")
public class MultimodalDocumentController {

    @Resource
    private MultimodalDocumentService multimodalDocumentService;

    /**
     * 全面分析多模态文档内容
     *
     * @param file         上传的文档文件
     * @param analysisType 分析类型
     * @return 多模态文档分析结果
     */
    @PostMapping("/analyze")
    @Operation(summary = "全面分析多模态文档", 
               description = "支持PDF、Word、Excel等格式，智能分析文字、图片、表格、代码等多种内容")
    public ResultInfo<MultimodalDocumentAnalysis> analyzeDocument(
            @Parameter(description = "文档文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "分析类型：COMPREHENSIVE, TEXT_ONLY, VISUAL_FOCUS, STRUCTURE_ANALYSIS") 
            @RequestParam(defaultValue = "COMPREHENSIVE") String analysisType) {
        try {
            if (file.isEmpty()) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "文件不能为空");
            }

            MultimodalDocumentAnalysis analysis = multimodalDocumentService.analyzeDocument(file, analysisType);
            return ResultInfo.success(analysis);
        } catch (Exception e) {
            log.error("多模态文档分析失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "多模态文档分析失败：" + e.getMessage());
        }
    }

    /**
     * 提取文档结构信息
     *
     * @param file 上传的文档文件
     * @return 文档结构信息
     */
    @PostMapping("/structure/extract")
    @Operation(summary = "提取文档结构", 
               description = "分析文档的层次结构、章节信息、页面布局等")
    public ResultInfo<DocumentStructure> extractDocumentStructure(
            @Parameter(description = "文档文件") @RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "文件不能为空");
            }

            DocumentStructure structure = multimodalDocumentService.extractDocumentStructure(file);
            return ResultInfo.success(structure);
        } catch (Exception e) {
            log.error("提取文档结构失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "提取文档结构失败：" + e.getMessage());
        }
    }

    /**
     * 处理文档中的图片内容
     *
     * @param file 上传的文档文件
     * @return 图片分析结果
     */
    @PostMapping("/images/process")
    @Operation(summary = "处理文档图片", 
               description = "提取并分析文档中的图片内容，包括OCR文字识别和图像理解")
    public ResultInfo<List<Map<String, Object>>> processDocumentImages(
            @Parameter(description = "文档文件") @RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "文件不能为空");
            }

            List<Map<String, Object>> imageResults = multimodalDocumentService.processDocumentImages(file);
            return ResultInfo.success(imageResults);
        } catch (Exception e) {
            log.error("处理文档图片失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "处理文档图片失败：" + e.getMessage());
        }
    }

    /**
     * 提取并分析文档中的表格
     *
     * @param file 上传的文档文件
     * @return 表格分析结果
     */
    @PostMapping("/tables/extract")
    @Operation(summary = "提取分析表格", 
               description = "提取文档中的表格数据并进行智能分析，支持数据统计和趋势分析")
    public ResultInfo<List<Map<String, Object>>> extractAndAnalyzeTables(
            @Parameter(description = "文档文件") @RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "文件不能为空");
            }

            List<Map<String, Object>> tableResults = multimodalDocumentService.extractAndAnalyzeTables(file);
            return ResultInfo.success(tableResults);
        } catch (Exception e) {
            log.error("提取分析表格失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "提取分析表格失败：" + e.getMessage());
        }
    }

    /**
     * 识别并分析文档中的代码片段
     *
     * @param file 上传的文档文件
     * @return 代码片段分析结果
     */
    @PostMapping("/code/analyze")
    @Operation(summary = "识别分析代码", 
               description = "智能识别文档中的代码片段，分析编程语言、代码质量和功能")
    public ResultInfo<List<Map<String, Object>>> identifyAndAnalyzeCode(
            @Parameter(description = "文档文件") @RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "文件不能为空");
            }

            List<Map<String, Object>> codeResults = multimodalDocumentService.identifyAndAnalyzeCode(file);
            return ResultInfo.success(codeResults);
        } catch (Exception e) {
            log.error("识别分析代码失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "识别分析代码失败：" + e.getMessage());
        }
    }

    /**
     * 智能文档分类
     *
     * @param file 上传的文档文件
     * @return 文档分类结果
     */
    @PostMapping("/classify")
    @Operation(summary = "智能文档分类", 
               description = "基于内容和结构特征，智能识别文档类型和所属领域")
    public ResultInfo<Map<String, Object>> classifyDocument(
            @Parameter(description = "文档文件") @RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "文件不能为空");
            }

            Map<String, Object> classificationResult = multimodalDocumentService.classifyDocument(file);
            return ResultInfo.success(classificationResult);
        } catch (Exception e) {
            log.error("智能文档分类失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "智能文档分类失败：" + e.getMessage());
        }
    }

    /**
     * 文档质量评估
     *
     * @param file               上传的文档文件
     * @param evaluationCriteria 评估标准
     * @return 质量评估结果
     */
    @PostMapping("/quality/evaluate")
    @Operation(summary = "文档质量评估", 
               description = "基于多维度标准评估文档质量，提供改进建议")
    public ResultInfo<Map<String, Object>> evaluateDocumentQuality(
            @Parameter(description = "文档文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "评估标准") @RequestBody(required = false) Map<String, Object> evaluationCriteria) {
        try {
            if (file.isEmpty()) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "文件不能为空");
            }

            if (evaluationCriteria == null) {
                evaluationCriteria = Map.of();
            }

            Map<String, Object> qualityResult = multimodalDocumentService.evaluateDocumentQuality(
                    file, evaluationCriteria);
            return ResultInfo.success(qualityResult);
        } catch (Exception e) {
            log.error("文档质量评估失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "文档质量评估失败：" + e.getMessage());
        }
    }

    /**
     * 生成文档摘要
     *
     * @param file        上传的文档文件
     * @param summaryType 摘要类型
     * @return 文档摘要
     */
    @PostMapping("/summary/generate")
    @Operation(summary = "生成文档摘要", 
               description = "智能生成文档摘要，支持不同详细程度和专业领域")
    public ResultInfo<String> generateDocumentSummary(
            @Parameter(description = "文档文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "摘要类型：BRIEF, DETAILED, TECHNICAL, ACADEMIC") 
            @RequestParam(defaultValue = "DETAILED") String summaryType) {
        try {
            if (file.isEmpty()) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "文件不能为空");
            }

            String summary = multimodalDocumentService.generateDocumentSummary(file, summaryType);
            return ResultInfo.success(summary);
        } catch (Exception e) {
            log.error("生成文档摘要失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "生成文档摘要失败：" + e.getMessage());
        }
    }

    /**
     * 检查文档格式规范
     *
     * @param file            上传的文档文件
     * @param formatStandards 格式标准
     * @return 格式检查结果
     */
    @PostMapping("/format/check")
    @Operation(summary = "检查文档格式", 
               description = "检查文档是否符合指定的格式标准和规范要求")
    public ResultInfo<Map<String, Object>> checkDocumentFormat(
            @Parameter(description = "文档文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "格式标准") @RequestBody(required = false) Map<String, Object> formatStandards) {
        try {
            if (file.isEmpty()) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "文件不能为空");
            }

            if (formatStandards == null) {
                formatStandards = Map.of();
            }

            Map<String, Object> formatResult = multimodalDocumentService.checkDocumentFormat(
                    file, formatStandards);
            return ResultInfo.success(formatResult);
        } catch (Exception e) {
            log.error("检查文档格式失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "检查文档格式失败：" + e.getMessage());
        }
    }
}
