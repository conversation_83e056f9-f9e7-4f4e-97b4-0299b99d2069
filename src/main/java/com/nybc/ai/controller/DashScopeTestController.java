package com.nybc.ai.controller;

import com.nybc.ai.service.UnifiedAiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.Map;

/**
 * 类描述：阿里百炼测试控制器
 * 专门用于测试阿里百炼平台的AI功能
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/ai/dashscope")
@Tag(name = "阿里百炼测试", description = "测试阿里百炼平台的AI功能")
public class DashScopeTestController {

    @Resource
    private UnifiedAiService unifiedAiService;

    /**
     * 测试百炼DeepSeek-R1模型
     */
    @PostMapping("/test/deepseek-r1")
    @Operation(summary = "测试DeepSeek-R1", description = "测试百炼平台的DeepSeek-R1模型")
    public ResponseEntity<String> testDeepSeekR1(@RequestBody String prompt) {
        try {
            log.info("测试百炼DeepSeek-R1模型，prompt: {}", prompt);
            String response = unifiedAiService.chatWithProvider("dashscope", prompt);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试DeepSeek-R1失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试百炼流式响应
     */
    @PostMapping("/test/stream")
    @Operation(summary = "测试流式响应", description = "测试百炼平台的流式输出功能")
    public Flux<String> testStream(@RequestBody String prompt) {
        try {
            log.info("测试百炼流式响应，prompt: {}", prompt);
            return unifiedAiService.streamChatWithProvider("dashscope", prompt);
        } catch (Exception e) {
            log.error("测试流式响应失败: {}", e.getMessage(), e);
            return Flux.just("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试数学推理能力
     */
    @PostMapping("/test/math")
    @Operation(summary = "测试数学推理", description = "测试DeepSeek-R1的数学推理能力")
    public ResponseEntity<String> testMath() {
        try {
            String mathPrompt = "请解决这个数学问题：如果一个圆的半径是5cm，那么它的面积是多少？请详细说明计算过程。";
            log.info("测试数学推理能力");
            String response = unifiedAiService.chatWithProvider("dashscope", mathPrompt);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试数学推理失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试代码分析能力
     */
    @PostMapping("/test/code")
    @Operation(summary = "测试代码分析", description = "测试DeepSeek-R1的代码分析能力")
    public ResponseEntity<String> testCode() {
        try {
            String codePrompt = """
                请分析以下Java代码的功能和可能的问题：
                
                ```java
                public class Calculator {
                    public int divide(int a, int b) {
                        return a / b;
                    }
                }
                ```
                
                请提供详细的分析和改进建议。
                """;
            log.info("测试代码分析能力");
            String response = unifiedAiService.chatWithProvider("dashscope", codePrompt);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试代码分析失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试逻辑推理能力
     */
    @PostMapping("/test/logic")
    @Operation(summary = "测试逻辑推理", description = "测试DeepSeek-R1的逻辑推理能力")
    public ResponseEntity<String> testLogic() {
        try {
            String logicPrompt = """
                逻辑推理题：
                
                有三个盒子，分别标记为A、B、C。
                - 盒子A里有红球和蓝球
                - 盒子B里有红球和绿球  
                - 盒子C里有蓝球和绿球
                
                现在告诉你：
                1. 如果盒子A里的红球数量是偶数，那么盒子B里的绿球数量是奇数
                2. 盒子C里的蓝球数量是偶数
                3. 盒子B里的红球数量等于盒子A里的蓝球数量
                
                请问：盒子A里的红球数量是奇数还是偶数？请详细说明推理过程。
                """;
            log.info("测试逻辑推理能力");
            String response = unifiedAiService.chatWithProvider("dashscope", logicPrompt);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试逻辑推理失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试中文理解能力
     */
    @PostMapping("/test/chinese")
    @Operation(summary = "测试中文理解", description = "测试对中文的理解和生成能力")
    public ResponseEntity<String> testChinese() {
        try {
            String chinesePrompt = """
                请帮我写一份关于"人工智能在教育领域应用"的简短报告，要求：
                1. 包含引言、主要应用、优势分析、挑战与展望四个部分
                2. 每个部分100-150字
                3. 语言要专业但易懂
                4. 重点关注中国的教育环境
                """;
            log.info("测试中文理解能力");
            String response = unifiedAiService.chatWithProvider("dashscope", chinesePrompt);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试中文理解失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取百炼服务状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取百炼状态", description = "检查百炼服务的可用性")
    public ResponseEntity<Map<String, Object>> getStatus() {
        try {
            boolean available = unifiedAiService.isProviderAvailable("dashscope");
            Map<String, Object> status = Map.of(
                    "provider", "dashscope",
                    "platform", "阿里百炼",
                    "model", "deepseek-r1",
                    "available", available,
                    "endpoint", "https://dashscope.aliyuncs.com/compatible-mode/v1",
                    "timestamp", System.currentTimeMillis()
            );
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("获取百炼状态失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取状态失败: " + e.getMessage()));
        }
    }

    /**
     * 综合功能测试
     */
    @PostMapping("/test/comprehensive")
    @Operation(summary = "综合功能测试", description = "测试百炼平台的综合AI能力")
    public ResponseEntity<Map<String, Object>> comprehensiveTest() {
        try {
            log.info("开始百炼综合功能测试");
            
            Map<String, Object> results = new java.util.HashMap<>();
            
            // 测试基础对话
            try {
                String basicResponse = unifiedAiService.chatWithProvider("dashscope", "你好，请简单介绍一下自己");
                results.put("basic_chat", Map.of("success", true, "response_length", basicResponse.length()));
            } catch (Exception e) {
                results.put("basic_chat", Map.of("success", false, "error", e.getMessage()));
            }
            
            // 测试数学计算
            try {
                String mathResponse = unifiedAiService.chatWithProvider("dashscope", "计算 123 + 456 = ?");
                results.put("math_calculation", Map.of("success", true, "response_length", mathResponse.length()));
            } catch (Exception e) {
                results.put("math_calculation", Map.of("success", false, "error", e.getMessage()));
            }
            
            // 测试代码生成
            try {
                String codeResponse = unifiedAiService.chatWithProvider("dashscope", "用Java写一个简单的Hello World程序");
                results.put("code_generation", Map.of("success", true, "response_length", codeResponse.length()));
            } catch (Exception e) {
                results.put("code_generation", Map.of("success", false, "error", e.getMessage()));
            }
            
            results.put("test_time", System.currentTimeMillis());
            results.put("platform", "阿里百炼");
            results.put("model", "deepseek-r1");
            
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            log.error("综合功能测试失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "综合测试失败: " + e.getMessage()));
        }
    }
}
