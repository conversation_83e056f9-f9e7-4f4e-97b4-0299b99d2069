package com.nybc.ai.controller;

import com.nybc.ai.service.UnifiedAiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * 类描述：统一AI架构测试控制器
 * 用于验证迁移到统一Spring AI架构后的功能
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/ai/unified")
@Tag(name = "统一AI测试", description = "测试统一Spring AI架构的功能")
public class UnifiedAiTestController {

    @Resource
    private UnifiedAiService unifiedAiService;

    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取系统状态", description = "查看统一AI架构的状态信息")
    public ResponseEntity<Map<String, Object>> getStatus() {
        try {
            Map<String, Object> status = unifiedAiService.getSystemStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("获取系统状态失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取系统状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取可用的AI提供商列表
     */
    @GetMapping("/providers")
    @Operation(summary = "获取可用提供商", description = "查看当前可用的AI提供商列表")
    public ResponseEntity<List<String>> getAvailableProviders() {
        try {
            List<String> providers = unifiedAiService.getAvailableProviders();
            return ResponseEntity.ok(providers);
        } catch (Exception e) {
            log.error("获取可用提供商失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 测试默认AI提供商
     */
    @PostMapping("/test/default")
    @Operation(summary = "测试默认提供商", description = "使用默认AI提供商进行测试")
    public ResponseEntity<String> testDefaultProvider(@RequestBody String prompt) {
        try {
            log.info("测试默认AI提供商，prompt: {}", prompt);
            String response = unifiedAiService.chat(prompt);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试默认提供商失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试指定AI提供商
     */
    @PostMapping("/test/{provider}")
    @Operation(summary = "测试指定提供商", description = "使用指定的AI提供商进行测试")
    public ResponseEntity<String> testProvider(
            @Parameter(description = "AI提供商名称") @PathVariable String provider,
            @RequestBody String prompt) {
        try {
            log.info("测试AI提供商[{}]，prompt: {}", provider, prompt);
            String response = unifiedAiService.chatWithProvider(provider, prompt);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试提供商[{}]失败: {}", provider, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试参数化提示
     */
    @PostMapping("/test/template")
    @Operation(summary = "测试参数化提示", description = "测试使用参数模板的AI调用")
    public ResponseEntity<String> testTemplate(@RequestBody Map<String, Object> request) {
        try {
            String template = (String) request.get("template");
            @SuppressWarnings("unchecked")
            Map<String, Object> parameters = (Map<String, Object>) request.get("parameters");
            
            log.info("测试参数化提示，模板: {}", template);
            String response = unifiedAiService.chat(template, parameters);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试参数化提示失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试流式响应
     */
    @PostMapping("/test/stream")
    @Operation(summary = "测试流式响应", description = "测试AI的流式输出功能")
    public Flux<String> testStream(@RequestBody String prompt) {
        try {
            log.info("测试流式响应，prompt: {}", prompt);
            return unifiedAiService.streamChat(prompt);
        } catch (Exception e) {
            log.error("测试流式响应失败: {}", e.getMessage(), e);
            return Flux.just("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试指定提供商的流式响应
     */
    @PostMapping("/test/stream/{provider}")
    @Operation(summary = "测试指定提供商流式响应", description = "测试指定AI提供商的流式输出功能")
    public Flux<String> testStreamWithProvider(
            @Parameter(description = "AI提供商名称") @PathVariable String provider,
            @RequestBody String prompt) {
        try {
            log.info("测试提供商[{}]流式响应，prompt: {}", provider, prompt);
            return unifiedAiService.streamChatWithProvider(provider, prompt);
        } catch (Exception e) {
            log.error("测试提供商[{}]流式响应失败: {}", provider, e.getMessage(), e);
            return Flux.just("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试嵌入向量生成
     */
    @PostMapping("/test/embedding")
    @Operation(summary = "测试嵌入向量", description = "测试文本嵌入向量生成功能")
    public ResponseEntity<Map<String, Object>> testEmbedding(@RequestBody String text) {
        try {
            log.info("测试嵌入向量生成，文本长度: {}", text.length());
            List<Double> embedding = unifiedAiService.generateEmbedding(text);
            
            Map<String, Object> result = Map.of(
                    "text", text,
                    "embedding_dimension", embedding.size(),
                    "embedding_preview", embedding.subList(0, Math.min(10, embedding.size())),
                    "success", true
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("测试嵌入向量生成失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "测试失败: " + e.getMessage()));
        }
    }

    /**
     * 检查提供商可用性
     */
    @GetMapping("/check/{provider}")
    @Operation(summary = "检查提供商可用性", description = "检查指定AI提供商是否可用")
    public ResponseEntity<Map<String, Object>> checkProvider(
            @Parameter(description = "AI提供商名称") @PathVariable String provider) {
        try {
            boolean available = unifiedAiService.isProviderAvailable(provider);
            Map<String, Object> result = Map.of(
                    "provider", provider,
                    "available", available,
                    "timestamp", System.currentTimeMillis()
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查提供商[{}]可用性失败: {}", provider, e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "检查失败: " + e.getMessage()));
        }
    }

    /**
     * 批量测试所有提供商
     */
    @PostMapping("/test/all")
    @Operation(summary = "批量测试所有提供商", description = "测试所有可用的AI提供商")
    public ResponseEntity<Map<String, Object>> testAllProviders(@RequestBody String prompt) {
        try {
            log.info("批量测试所有提供商，prompt: {}", prompt);
            
            List<String> providers = unifiedAiService.getAvailableProviders();
            Map<String, Object> results = new java.util.HashMap<>();
            
            for (String provider : providers) {
                try {
                    String response = unifiedAiService.chatWithProvider(provider, prompt);
                    results.put(provider, Map.of(
                            "success", true,
                            "response", response.length() > 100 ? response.substring(0, 100) + "..." : response
                    ));
                } catch (Exception e) {
                    results.put(provider, Map.of(
                            "success", false,
                            "error", e.getMessage()
                    ));
                }
            }
            
            return ResponseEntity.ok(Map.of(
                    "prompt", prompt,
                    "tested_providers", providers.size(),
                    "results", results
            ));
        } catch (Exception e) {
            log.error("批量测试所有提供商失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "批量测试失败: " + e.getMessage()));
        }
    }

    /**
     * 性能基准测试
     */
    @PostMapping("/benchmark")
    @Operation(summary = "性能基准测试", description = "对AI提供商进行性能基准测试")
    public ResponseEntity<Map<String, Object>> benchmark(@RequestBody Map<String, Object> request) {
        try {
            String prompt = (String) request.getOrDefault("prompt", "请简单介绍一下人工智能");
            String provider = (String) request.getOrDefault("provider", "default");
            int iterations = (Integer) request.getOrDefault("iterations", 3);
            
            log.info("开始性能基准测试，提供商: {}, 迭代次数: {}", provider, iterations);
            
            List<Long> responseTimes = new java.util.ArrayList<>();
            List<String> responses = new java.util.ArrayList<>();
            
            for (int i = 0; i < iterations; i++) {
                long startTime = System.currentTimeMillis();
                
                String response = "default".equals(provider) 
                        ? unifiedAiService.chat(prompt)
                        : unifiedAiService.chatWithProvider(provider, prompt);
                
                long endTime = System.currentTimeMillis();
                long responseTime = endTime - startTime;
                
                responseTimes.add(responseTime);
                responses.add(response.length() > 50 ? response.substring(0, 50) + "..." : response);
                
                log.info("第{}次测试完成，响应时间: {}ms", i + 1, responseTime);
            }
            
            double avgResponseTime = responseTimes.stream().mapToLong(Long::longValue).average().orElse(0.0);
            long minResponseTime = responseTimes.stream().mapToLong(Long::longValue).min().orElse(0L);
            long maxResponseTime = responseTimes.stream().mapToLong(Long::longValue).max().orElse(0L);
            
            Map<String, Object> result = Map.of(
                    "provider", provider,
                    "iterations", iterations,
                    "avg_response_time_ms", avgResponseTime,
                    "min_response_time_ms", minResponseTime,
                    "max_response_time_ms", maxResponseTime,
                    "response_times", responseTimes,
                    "sample_responses", responses
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("性能基准测试失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "基准测试失败: " + e.getMessage()));
        }
    }
}
