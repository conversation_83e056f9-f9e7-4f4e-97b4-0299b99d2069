package com.nybc.ai.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.service.ConversationOptimizationService;
import com.nybc.ai.service.dto.ConversationContext;
import com.nybc.ai.service.dto.ConversationSummary;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 类描述：对话优化控制器
 * 提供多轮对话优化、上下文压缩等功能
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/ai/conversation")
@Tag(name = "对话优化", description = "提供多轮对话优化、上下文压缩、对话摘要等功能")
public class ConversationOptimizationController {

    @Resource
    private ConversationOptimizationService conversationOptimizationService;

    /**
     * 压缩对话上下文
     *
     * @param sessionId 会话ID
     * @param maxTokens 最大token限制
     * @return 压缩后的对话上下文
     */
    @PostMapping("/context/compress")
    @Operation(summary = "压缩对话上下文", 
               description = "当对话历史过长时，智能压缩保留关键信息，适用于长对话场景")
    public ResultInfo<ConversationContext> compressContext(
            @Parameter(description = "会话ID") @RequestParam String sessionId,
            @Parameter(description = "最大token限制，默认4000") @RequestParam(defaultValue = "4000") int maxTokens) {
        try {
            ConversationContext context = conversationOptimizationService.compressContext(sessionId, maxTokens);
            return ResultInfo.success(context);
        } catch (Exception e) {
            log.error("压缩对话上下文失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "压缩对话上下文失败：" + e.getMessage());
        }
    }

    /**
     * 生成对话摘要
     *
     * @param sessionId   会话ID
     * @param summaryType 摘要类型
     * @return 对话摘要
     */
    @PostMapping("/summary/generate")
    @Operation(summary = "生成对话摘要", 
               description = "对长对话进行智能摘要，提取关键信息，支持不同摘要类型")
    public ResultInfo<ConversationSummary> generateSummary(
            @Parameter(description = "会话ID") @RequestParam String sessionId,
            @Parameter(description = "摘要类型：BRIEF, DETAILED, TECHNICAL") @RequestParam(defaultValue = "DETAILED") String summaryType) {
        try {
            ConversationSummary summary = conversationOptimizationService.generateSummary(sessionId, summaryType);
            return ResultInfo.success(summary);
        } catch (Exception e) {
            log.error("生成对话摘要失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "生成对话摘要失败：" + e.getMessage());
        }
    }

    /**
     * 优化多轮对话Prompt
     *
     * @param sessionId     会话ID
     * @param currentPrompt 当前用户输入
     * @param domainContext 领域上下文
     * @return 优化后的Prompt
     */
    @PostMapping("/prompt/optimize")
    @Operation(summary = "优化多轮对话Prompt", 
               description = "基于对话历史和上下文，优化下一轮对话的Prompt，提高对话质量")
    public ResultInfo<String> optimizeMultiTurnPrompt(
            @Parameter(description = "会话ID") @RequestParam String sessionId,
            @Parameter(description = "当前用户输入") @RequestParam String currentPrompt,
            @Parameter(description = "领域上下文信息") @RequestBody(required = false) Map<String, Object> domainContext) {
        try {
            if (domainContext == null) {
                domainContext = Map.of();
            }
            String optimizedPrompt = conversationOptimizationService.optimizeMultiTurnPrompt(
                    sessionId, currentPrompt, domainContext);
            return ResultInfo.success(optimizedPrompt);
        } catch (Exception e) {
            log.error("优化多轮对话Prompt失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "优化多轮对话Prompt失败：" + e.getMessage());
        }
    }

    /**
     * 检测对话主题转换
     *
     * @param sessionId  会话ID
     * @param newMessage 新消息
     * @return 是否发生主题转换
     */
    @PostMapping("/topic/detect-shift")
    @Operation(summary = "检测对话主题转换", 
               description = "识别对话是否转向新主题，决定是否需要重置上下文")
    public ResultInfo<Boolean> detectTopicShift(
            @Parameter(description = "会话ID") @RequestParam String sessionId,
            @Parameter(description = "新消息内容") @RequestParam String newMessage) {
        try {
            boolean hasShift = conversationOptimizationService.detectTopicShift(sessionId, newMessage);
            return ResultInfo.success(hasShift);
        } catch (Exception e) {
            log.error("检测对话主题转换失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "检测对话主题转换失败：" + e.getMessage());
        }
    }

    /**
     * 智能上下文管理
     *
     * @param sessionId        会话ID
     * @param maxContextLength 最大上下文长度
     * @return 管理后的上下文信息
     */
    @PostMapping("/context/manage")
    @Operation(summary = "智能上下文管理", 
               description = "根据对话内容和领域特点，智能管理上下文窗口")
    public ResultInfo<ConversationContext> manageContext(
            @Parameter(description = "会话ID") @RequestParam String sessionId,
            @Parameter(description = "最大上下文长度，默认4000") @RequestParam(defaultValue = "4000") int maxContextLength) {
        try {
            ConversationContext context = conversationOptimizationService.manageContext(sessionId, maxContextLength);
            return ResultInfo.success(context);
        } catch (Exception e) {
            log.error("智能上下文管理失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "智能上下文管理失败：" + e.getMessage());
        }
    }

    /**
     * 提取对话中的关键实体和概念
     *
     * @param sessionId 会话ID
     * @return 提取的关键实体和概念
     */
    @GetMapping("/entities/extract")
    @Operation(summary = "提取领域实体", 
               description = "专门针对计算机、软件学院的专业术语和概念进行提取")
    public ResultInfo<Map<String, Object>> extractDomainEntities(
            @Parameter(description = "会话ID") @RequestParam String sessionId) {
        try {
            Map<String, Object> entities = conversationOptimizationService.extractDomainEntities(sessionId);
            return ResultInfo.success(entities);
        } catch (Exception e) {
            log.error("提取领域实体失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "提取领域实体失败：" + e.getMessage());
        }
    }
}
