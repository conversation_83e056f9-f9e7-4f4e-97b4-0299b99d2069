package com.nybc.ai.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.service.DomainSpecificPromptService;
import com.nybc.ai.service.dto.DomainPromptOptimizationRequest;
import com.nybc.ai.service.dto.DomainPromptOptimizationResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 类描述：领域特定Prompt控制器
 * 专注于计算机、软件学院课程教学的Prompt优化
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/ai/domain-prompt")
@Tag(name = "领域特定Prompt优化", description = "专注于计算机、软件学院课程教学的Prompt优化功能")
public class DomainSpecificPromptController {

    @Resource
    private DomainSpecificPromptService domainSpecificPromptService;

    /**
     * 针对计算机、软件学院领域优化Prompt
     *
     * @param request 领域优化请求
     * @return 优化结果
     */
    @PostMapping("/optimize/computer-science")
    @Operation(summary = "计算机科学领域Prompt优化", 
               description = "针对计算机、软件学院的教学场景，对Prompt进行专业化优化")
    public ResultInfo<DomainPromptOptimizationResponse> optimizeForComputerScience(
            @Valid @RequestBody DomainPromptOptimizationRequest request) {
        try {
            DomainPromptOptimizationResponse response = 
                    domainSpecificPromptService.optimizeForComputerScience(request);
            return ResultInfo.success(response);
        } catch (Exception e) {
            log.error("计算机科学领域Prompt优化失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "计算机科学领域Prompt优化失败：" + e.getMessage());
        }
    }

    /**
     * 为代码评估场景优化Prompt
     *
     * @param originalPrompt      原始Prompt
     * @param codeContext        代码上下文信息
     * @param evaluationCriteria 评估标准
     * @return 优化后的Prompt
     */
    @PostMapping("/optimize/code-evaluation")
    @Operation(summary = "代码评估场景Prompt优化", 
               description = "专门为代码评估和审查场景优化Prompt，提高评估准确性")
    public ResultInfo<String> optimizeForCodeEvaluation(
            @Parameter(description = "原始Prompt") @RequestParam String originalPrompt,
            @Parameter(description = "代码上下文信息") @RequestBody Map<String, Object> codeContext,
            @Parameter(description = "评估标准") @RequestParam(required = false) Map<String, Object> evaluationCriteria) {
        try {
            if (evaluationCriteria == null) {
                evaluationCriteria = Map.of();
            }
            String optimizedPrompt = domainSpecificPromptService.optimizeForCodeEvaluation(
                    originalPrompt, codeContext, evaluationCriteria);
            return ResultInfo.success(optimizedPrompt);
        } catch (Exception e) {
            log.error("代码评估场景Prompt优化失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "代码评估场景Prompt优化失败：" + e.getMessage());
        }
    }

    /**
     * 为作业批改场景优化Prompt
     *
     * @param originalPrompt 原始Prompt
     * @param assignmentType 作业类型
     * @param gradingRubric  评分标准
     * @return 优化后的Prompt
     */
    @PostMapping("/optimize/assignment-grading")
    @Operation(summary = "作业批改场景Prompt优化", 
               description = "为不同类型的作业批改场景优化Prompt，确保评分的公平性和准确性")
    public ResultInfo<String> optimizeForAssignmentGrading(
            @Parameter(description = "原始Prompt") @RequestParam String originalPrompt,
            @Parameter(description = "作业类型：PROGRAMMING, DESIGN, REPORT, PROJECT") @RequestParam String assignmentType,
            @Parameter(description = "评分标准") @RequestBody(required = false) Map<String, Object> gradingRubric) {
        try {
            if (gradingRubric == null) {
                gradingRubric = Map.of();
            }
            String optimizedPrompt = domainSpecificPromptService.optimizeForAssignmentGrading(
                    originalPrompt, assignmentType, gradingRubric);
            return ResultInfo.success(optimizedPrompt);
        } catch (Exception e) {
            log.error("作业批改场景Prompt优化失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "作业批改场景Prompt优化失败：" + e.getMessage());
        }
    }

    /**
     * 为学生答疑场景优化Prompt
     *
     * @param originalPrompt 原始Prompt
     * @param studentLevel   学生水平
     * @param courseContext  课程上下文
     * @return 优化后的Prompt
     */
    @PostMapping("/optimize/student-tutoring")
    @Operation(summary = "学生答疑场景Prompt优化", 
               description = "根据学生水平和课程背景，优化答疑场景的Prompt，提供个性化指导")
    public ResultInfo<String> optimizeForStudentTutoring(
            @Parameter(description = "原始Prompt") @RequestParam String originalPrompt,
            @Parameter(description = "学生水平：BEGINNER, INTERMEDIATE, ADVANCED") @RequestParam String studentLevel,
            @Parameter(description = "课程上下文") @RequestBody(required = false) Map<String, Object> courseContext) {
        try {
            if (courseContext == null) {
                courseContext = Map.of();
            }
            String optimizedPrompt = domainSpecificPromptService.optimizeForStudentTutoring(
                    originalPrompt, studentLevel, courseContext);
            return ResultInfo.success(optimizedPrompt);
        } catch (Exception e) {
            log.error("学生答疑场景Prompt优化失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "学生答疑场景Prompt优化失败：" + e.getMessage());
        }
    }

    /**
     * 为技术文档分析优化Prompt
     *
     * @param originalPrompt 原始Prompt
     * @param documentType   文档类型
     * @param analysisGoal   分析目标
     * @return 优化后的Prompt
     */
    @PostMapping("/optimize/document-analysis")
    @Operation(summary = "技术文档分析Prompt优化", 
               description = "为不同类型的技术文档分析场景优化Prompt，提高分析的专业性")
    public ResultInfo<String> optimizeForDocumentAnalysis(
            @Parameter(description = "原始Prompt") @RequestParam String originalPrompt,
            @Parameter(description = "文档类型：API_DOC, DESIGN_DOC, USER_MANUAL, TECHNICAL_SPEC") @RequestParam String documentType,
            @Parameter(description = "分析目标") @RequestParam String analysisGoal) {
        try {
            String optimizedPrompt = domainSpecificPromptService.optimizeForDocumentAnalysis(
                    originalPrompt, documentType, analysisGoal);
            return ResultInfo.success(optimizedPrompt);
        } catch (Exception e) {
            log.error("技术文档分析Prompt优化失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "技术文档分析Prompt优化失败：" + e.getMessage());
        }
    }

    /**
     * 自动检测并应用最佳Prompt模式
     *
     * @param originalPrompt 原始Prompt
     * @param context        上下文信息
     * @return 应用最佳模式后的Prompt
     */
    @PostMapping("/auto-apply-pattern")
    @Operation(summary = "自动应用最佳Prompt模式", 
               description = "智能检测Prompt类型并自动应用最适合的优化模式")
    public ResultInfo<String> autoApplyBestPattern(
            @Parameter(description = "原始Prompt") @RequestParam String originalPrompt,
            @Parameter(description = "上下文信息") @RequestBody(required = false) Map<String, Object> context) {
        try {
            if (context == null) {
                context = Map.of();
            }
            String optimizedPrompt = domainSpecificPromptService.autoApplyBestPattern(originalPrompt, context);
            return ResultInfo.success(optimizedPrompt);
        } catch (Exception e) {
            log.error("自动应用最佳Prompt模式失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "自动应用最佳Prompt模式失败：" + e.getMessage());
        }
    }

    /**
     * 基于历史数据自动调优Prompt
     *
     * @param promptKey        Prompt模板键
     * @param tenantId         租户ID
     * @param optimizationGoal 优化目标
     * @return 调优结果
     */
    @PostMapping("/auto-tune")
    @Operation(summary = "基于历史数据自动调优Prompt", 
               description = "基于历史执行数据和反馈，自动调优Prompt以达到最佳效果")
    public ResultInfo<DomainPromptOptimizationResponse> autoTunePrompt(
            @Parameter(description = "Prompt模板键") @RequestParam String promptKey,
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Parameter(description = "优化目标：ACCURACY, SPEED, COMPREHENSIVENESS") @RequestParam String optimizationGoal) {
        try {
            DomainPromptOptimizationResponse response = domainSpecificPromptService.autoTunePrompt(
                    promptKey, tenantId, optimizationGoal);
            return ResultInfo.success(response);
        } catch (Exception e) {
            log.error("基于历史数据自动调优Prompt失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "基于历史数据自动调优Prompt失败：" + e.getMessage());
        }
    }

    /**
     * 生成领域特定的Prompt模板
     *
     * @param scenario     应用场景
     * @param requirements 具体需求
     * @return 生成的Prompt模板
     */
    @PostMapping("/generate-template")
    @Operation(summary = "生成领域特定Prompt模板", 
               description = "根据应用场景和需求，生成专业的Prompt模板")
    public ResultInfo<String> generateDomainTemplate(
            @Parameter(description = "应用场景") @RequestParam String scenario,
            @Parameter(description = "具体需求") @RequestBody Map<String, Object> requirements) {
        try {
            String template = domainSpecificPromptService.generateDomainTemplate(scenario, requirements);
            return ResultInfo.success(template);
        } catch (Exception e) {
            log.error("生成领域特定Prompt模板失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), 
                    "生成领域特定Prompt模板失败：" + e.getMessage());
        }
    }
}
