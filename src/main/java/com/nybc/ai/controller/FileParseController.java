package com.nybc.ai.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.rules.dto.DirectoryParseRequest;
import com.nybc.ai.service.FileParserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping("/api/ai")
@Tag(name = "AI 文件解析", description = "提供通用文件解析与问答功能")
public class FileParseController {

    private final FileParserService fileParserService;

    public FileParseController(FileParserService fileParserService) {
        this.fileParserService = fileParserService;
    }

    @PostMapping("/parse-upload")
    @Operation(summary = "解析上传的文件并与AI对话",
            description = "上传一个文件和相关问题，AI将基于文件内容进行回答。支持的文件类型：docx, pptx, xlsx, java, xml, py, html, md, txt等。")
    public ResultInfo<String> parseFileAndAsk(
            @Parameter(description = "要解析的文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "要向AI提出的问题") @RequestParam("prompt") String prompt) {
        if (file.isEmpty()) {
            return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "上传的文件不能为空");
        }
        try {
            String aiResponse = fileParserService.parseFile(file, prompt);
            return ResultInfo.success(aiResponse);
        } catch (Exception e) {
            log.error("文件解析接口失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "处理文件时发生错误：" + e.getMessage());
        }
    }

    @PostMapping("/parse-directory")
    @Operation(summary = "解析服务器本地目录并与AI对话",
            description = "提供一个服务器上的绝对路径和一个问题，AI将分析该目录下所有文件并回答。出于安全考虑，只允许访问预先配置的白名单目录。")
    public ResultInfo<String> parseDirectoryAndAsk(@Valid @RequestBody DirectoryParseRequest request) {
        try {
            String aiResponse = fileParserService.parseDirectory(request.getDirectoryPath(), request.getPrompt());
            return ResultInfo.success(aiResponse);
        } catch (Exception e) {
            log.error("目录解析接口失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "处理目录时发生错误：" + e.getMessage());
        }
    }
} 