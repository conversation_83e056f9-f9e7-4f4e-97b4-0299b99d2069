package com.nybc.ai.controller.dto;

/**
 * 类描述：聊天请求的数据传输对象
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public class ChatRequest {

    /**
     * 会话ID (可选，如果为空，将创建新会话)
     */
    private String sessionId;

    /**
     * 用户的提问
     */
    private String prompt;

    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }
} 