package com.nybc.ai.controller;

import com.nybc.ai.service.DocumentParsingService;
import com.nybc.ai.service.DocumentQueryService;
import com.nybc.ai.service.VectorStoreService;
import jakarta.annotation.Resource;
import org.springframework.ai.document.Document;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.UUID;

/**
 * 类描述：文档处理控制器
 * 提供文件上传、索引和智能问答的API端点。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@RestController
@RequestMapping("/api/document")
public class DocumentController {

    @Resource
    private DocumentParsingService parsingService;
    @Resource
    private VectorStoreService vectorStoreService;
    @Resource
    private DocumentQueryService queryService;

    /**
     * 处理上传的文档，并根据用户查询返回智能回答。
     *
     * @param file  用户上传的文件 (如PDF)
     * @param query 用户的自然语言查询
     * @return 包含AI生成JSON回答的响应实体
     */
    @PostMapping("/query")
    public ResponseEntity<String> queryDocument(
            @RequestParam("file") MultipartFile file,
            @RequestParam("query") String query) {

        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body("{\"error\": \"上传的文件不能为空\"}");
        }

        // 为本次处理生成一个唯一ID，用于关联所有相关的文档块
        String fileId = UUID.randomUUID().toString();

        // 阶段 1: 文档加载与智能解析
        List<Document> chunks = parsingService.parseAndChunk(file.getResource());

        // 阶段 2: 向量化与索引
        vectorStoreService.indexDocuments(chunks, fileId);

        // 阶段 3: 上下文检索
        List<Document> context = vectorStoreService.retrieveContext(query, fileId);

        if (context.isEmpty()) {
            return ResponseEntity.ok("{\"error\": \"无法在文档中找到与您问题相关的信息。\"}");
        }

        // 阶段 4: Prompt构建与AI调用
        String jsonResponse = queryService.queryDocument(query, context);

        // 返回最终的JSON回答
        return ResponseEntity.ok(jsonResponse);

    }

}