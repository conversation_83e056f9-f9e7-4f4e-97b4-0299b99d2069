package com.nybc.ai.controller;

import com.nybc.ai.service.dto.ChatRequest;
import com.nybc.ai.service.factory.ChatServiceFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * 类描述：统一的AI聊天API控制器
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@RestController
@RequestMapping("/api/ai/chat")
@Tag(name = "统一AI聊天API", description = "提供统一的、可切换服务商的流式聊天接口")
public class ChatController {

    @Resource
    private ChatServiceFactory chatServiceFactory;

    /**
     * 流式聊天端点
     *
     * @param provider    AI服务提供商标识
     * @param chatRequest 聊天请求体
     * @return 返回文本事件流 (text/event-stream)
     */
    @PostMapping(value = "/stream/{provider}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流式聊天接口", description = "根据指定的服务商进行流式聊天")
    public Flux<String> streamChat(@PathVariable String provider, @RequestBody ChatRequest chatRequest) {
        return chatServiceFactory.getChatService(provider)
                .map(chatService -> chatService.streamChat(chatRequest))
                .orElseGet(() -> Flux.error(new IllegalArgumentException("不支持或未配置的AI服务提供商: " + provider)));
    }

}