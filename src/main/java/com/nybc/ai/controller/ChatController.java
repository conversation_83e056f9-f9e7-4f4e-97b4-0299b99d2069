package com.nybc.ai.controller;

import com.nybc.ai.service.UnifiedAiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * 类描述：统一的AI聊天API控制器
 * 基于Spring AI框架提供统一的聊天接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/ai/chat")
@Tag(name = "统一AI聊天API", description = "基于Spring AI框架的统一聊天接口")
public class ChatController {

    @Resource
    private UnifiedAiService unifiedAiService;

    /**
     * 指定提供商的流式聊天
     *
     * @param provider AI服务提供商标识
     * @param prompt   用户输入
     * @return 返回文本事件流 (text/event-stream)
     */
    @PostMapping(value = "/stream/{provider}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "指定提供商流式聊天", description = "使用指定AI提供商进行流式聊天")
    public Flux<String> streamChatWithProvider(@PathVariable String provider, @RequestBody String prompt) {
        try {
            log.info("使用提供商[{}]进行流式聊天，prompt长度: {}", provider, prompt.length());
            return unifiedAiService.streamChatWithProvider(provider, prompt);
        } catch (Exception e) {
            log.error("流式聊天失败: {}", e.getMessage(), e);
            return Flux.error(new RuntimeException("流式聊天失败: " + e.getMessage()));
        }
    }

    /**
     * 默认流式聊天端点
     *
     * @param prompt 用户输入
     * @return 返回文本事件流 (text/event-stream)
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "默认流式聊天", description = "使用默认AI提供商进行流式聊天")
    public Flux<String> streamChat(@RequestBody String prompt) {
        try {
            log.info("使用默认提供商进行流式聊天，prompt长度: {}", prompt.length());
            return unifiedAiService.streamChat(prompt);
        } catch (Exception e) {
            log.error("流式聊天失败: {}", e.getMessage(), e);
            return Flux.error(new RuntimeException("流式聊天失败: " + e.getMessage()));
        }
    }

}