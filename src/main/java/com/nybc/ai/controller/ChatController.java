package com.nybc.ai.controller;

import com.nybc.ai.service.dto.ChatRequest;
import com.nybc.ai.service.factory.ChatServiceFactory;
import com.nybc.ai.service.UnifiedAiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * 类描述：统一的AI聊天API控制器
 * 支持统一AI架构和传统ChatService两种模式
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/ai/chat")
@Tag(name = "统一AI聊天API", description = "提供统一的、可切换服务商的流式聊天接口")
public class ChatController {

    @Resource
    private ChatServiceFactory chatServiceFactory;

    // 统一AI服务（优先使用）
    @Resource(required = false)
    private UnifiedAiService unifiedAiService;

    /**
     * 流式聊天端点（传统方式）
     *
     * @param provider    AI服务提供商标识
     * @param chatRequest 聊天请求体
     * @return 返回文本事件流 (text/event-stream)
     */
    @PostMapping(value = "/stream/{provider}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流式聊天接口（传统方式）", description = "根据指定的服务商进行流式聊天")
    public Flux<String> streamChat(@PathVariable String provider, @RequestBody ChatRequest chatRequest) {
        // 优先使用统一AI服务
        if (unifiedAiService != null && unifiedAiService.isProviderAvailable(provider)) {
            log.info("使用统一AI服务进行流式聊天，提供商: {}", provider);
            // 将ChatRequest转换为简单的prompt字符串
            String prompt = extractPromptFromChatRequest(chatRequest);
            return unifiedAiService.streamChatWithProvider(provider, prompt);
        } else {
            log.info("使用传统ChatService进行流式聊天，提供商: {}", provider);
            return chatServiceFactory.getChatService(provider)
                    .map(chatService -> chatService.streamChat(chatRequest))
                    .orElseGet(() -> Flux.error(new IllegalArgumentException("不支持或未配置的AI服务提供商: " + provider)));
        }
    }

    /**
     * 统一流式聊天端点（推荐）
     *
     * @param prompt 用户输入
     * @return 返回文本事件流 (text/event-stream)
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "统一流式聊天接口（推荐）", description = "使用默认AI提供商进行流式聊天")
    public Flux<String> streamChatUnified(@RequestBody String prompt) {
        if (unifiedAiService != null) {
            log.info("使用统一AI服务进行流式聊天，prompt长度: {}", prompt.length());
            return unifiedAiService.streamChat(prompt);
        } else {
            log.error("统一AI服务未配置");
            return Flux.error(new IllegalStateException("统一AI服务未配置"));
        }
    }

    /**
     * 指定提供商的统一流式聊天
     *
     * @param provider 提供商名称
     * @param prompt   用户输入
     * @return 返回文本事件流 (text/event-stream)
     */
    @PostMapping(value = "/unified/stream/{provider}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "指定提供商的统一流式聊天", description = "使用指定AI提供商进行流式聊天")
    public Flux<String> streamChatWithProvider(@PathVariable String provider, @RequestBody String prompt) {
        if (unifiedAiService != null) {
            log.info("使用统一AI服务进行流式聊天，提供商: {}, prompt长度: {}", provider, prompt.length());
            return unifiedAiService.streamChatWithProvider(provider, prompt);
        } else {
            log.error("统一AI服务未配置");
            return Flux.error(new IllegalStateException("统一AI服务未配置"));
        }
    }

    /**
     * 从ChatRequest中提取prompt字符串
     */
    private String extractPromptFromChatRequest(ChatRequest chatRequest) {
        if (chatRequest.getMessages() != null && !chatRequest.getMessages().isEmpty()) {
            // 取最后一条消息作为prompt
            return chatRequest.getMessages().get(chatRequest.getMessages().size() - 1).getContent();
        }
        return chatRequest.getPrompt() != null ? chatRequest.getPrompt() : "";
    }

}