package com.nybc.ai.controller;

import com.nybc.ai.controller.dto.ChatRequest;
import com.nybc.ai.service.AiChatService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.UUID;

/**
 * 类描述：AI聊天控制器
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@RestController
@RequestMapping("/api/ai")
public class AiChatController {

    @Resource
    private AiChatService aiChatService;

    /**
     * 发送聊天请求，支持上下文会话
     *
     * @param request 包含prompt和可选sessionId的请求体
     * @return 经过AI处理和JSON格式化的结果，如果创建了新会话，会包含sessionId
     */
    @PostMapping("/chat")
    public Map<String, Object> chat(@RequestBody ChatRequest request) {
        String sessionId = request.getSessionId();
        boolean newSession = (sessionId == null || sessionId.isBlank());
        if (newSession) {
            sessionId = UUID.randomUUID().toString();
        }

        Map<String, Object> response = aiChatService.chat(sessionId, request.getPrompt());

        if (newSession) {
            response.put("sessionId", sessionId);
        }

        return response;
    }
} 