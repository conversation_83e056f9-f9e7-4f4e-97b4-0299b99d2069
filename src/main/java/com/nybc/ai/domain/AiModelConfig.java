package com.nybc.ai.domain;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 类描述：AI模型配置表 (ai_model_config) 实体类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class AiModelConfig {

    private Long id;

    /**
     * AI提供商 (e.g., dashscope, ollama)
     */
    private String provider;

    /**
     * API Key
     */
    private String apiKey;

    /**
     * API基础URL
     */
    private String baseUrl;

    /**
     * 默认聊天模型
     */
    private String chatModel;

    /**
     * 默认嵌入模型
     */
    private String embeddingModel;

    /**
     * 是否启用
     */
    private Boolean isEnabled;
} 