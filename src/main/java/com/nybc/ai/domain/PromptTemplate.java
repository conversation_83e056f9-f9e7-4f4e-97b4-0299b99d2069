package com.nybc.ai.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Prompt 模板主表对应的领域对象
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PromptTemplate implements Serializable {
    @Serial
    private static final long serialVersionUID = 4940576580159014696L;

    private Long id;
    private String promptKey;
    private Long tenantId;
    private String description;
    private String taskType;
    private String status;
    private Long activeVersionId;
    private String activeTemplateContent;
    private String activeRuntimeProvider;
    private LocalDateTime createTime;
    private Long createUser;
    private LocalDateTime updateTime;
    private Long updateUser;
    private Integer deleted;
} 