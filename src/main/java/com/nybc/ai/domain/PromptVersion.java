package com.nybc.ai.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Prompt 版本历史表对应的领域对象
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PromptVersion implements Serializable {
    @Serial
    private static final long serialVersionUID = 4826745485067317432L;

    private Long id;
    private Long templateId;
    private Long tenantId;
    private Integer versionNumber;
    private String templateContent;
    private String rawIdeaText;
    private String changelog;
    private String optimizationModel;
    private String optimizationCot;
    private String runtimeProvider;
    private String performanceMetrics;
    private LocalDateTime createTime;
    private Long createUser;
    private LocalDateTime updateTime;
    private Long updateUser;
    private Integer deleted;
} 