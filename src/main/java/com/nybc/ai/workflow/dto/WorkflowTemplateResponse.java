package com.nybc.ai.workflow.dto;

import com.nybc.ai.workflow.entity.WorkflowTemplate;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类描述：工作流模板响应DTO
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class WorkflowTemplateResponse {

    /**
     * 模板ID
     */
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 工作流类型
     */
    private String workflowType;

    /**
     * LiteFlow规则链名称
     */
    private String chainName;

    /**
     * LiteFlow EL表达式
     */
    private String elExpression;

    /**
     * 工作流节点配置
     */
    private List<WorkflowTemplate.WorkflowNode> nodes;

    /**
     * 任务匹配条件
     */
    private WorkflowTemplate.TaskMatchingCondition matchingCondition;

    /**
     * 模板版本
     */
    private String version;

    /**
     * 是否为默认模板
     */
    private Boolean isDefault;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 更新人
     */
    private Long updateUser;
}
