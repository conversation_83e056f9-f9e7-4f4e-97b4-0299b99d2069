package com.nybc.ai.workflow.dto;

import com.nybc.ai.workflow.entity.WorkflowTemplate;
import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 类描述：工作流模板请求DTO
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class WorkflowTemplateRequest {

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 任务类型
     */
    @NotBlank(message = "任务类型不能为空")
    private String taskType;

    /**
     * 工作流类型
     */
    @NotBlank(message = "工作流类型不能为空")
    private String workflowType;

    /**
     * LiteFlow EL表达式
     */
    @NotBlank(message = "EL表达式不能为空")
    private String elExpression;

    /**
     * 工作流节点配置
     */
    private List<WorkflowTemplate.WorkflowNode> nodes;

    /**
     * 任务匹配条件
     */
    private WorkflowTemplate.TaskMatchingCondition matchingCondition;

    /**
     * 是否为默认模板
     */
    private Boolean isDefault = false;

    /**
     * 优先级
     */
    @NotNull(message = "优先级不能为空")
    private Integer priority;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 更新人
     */
    private Long updateUser;
}
