package com.nybc.ai.workflow.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 类描述：任务匹配规则DTO
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class TaskMatchingRule {

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 匹配条件
     */
    private MatchingCondition condition;

    /**
     * 目标工作流模板ID
     */
    private Long targetTemplateId;

    /**
     * 规则优先级（数字越大优先级越高）
     */
    private Integer priority;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 租户ID
     */
    private Long tenantId;

    @Data
    @Accessors(chain = true)
    public static class MatchingCondition {
        /**
         * 任务类型条件
         */
        private TaskTypeCondition taskType;

        /**
         * 文件条件
         */
        private FileCondition file;

        /**
         * 学生条件
         */
        private StudentCondition student;

        /**
         * 课程条件
         */
        private CourseCondition course;

        /**
         * 时间条件
         */
        private TimeCondition time;

        /**
         * 自定义条件
         */
        private Map<String, Object> custom;

        /**
         * 逻辑操作符：AND, OR
         */
        private String logicOperator = "AND";
    }

    @Data
    @Accessors(chain = true)
    public static class TaskTypeCondition {
        /**
         * 任务类型列表
         */
        private List<String> types;

        /**
         * 匹配模式：EXACT, CONTAINS, REGEX
         */
        private String matchMode = "EXACT";

        /**
         * 是否排除模式
         */
        private Boolean exclude = false;
    }

    @Data
    @Accessors(chain = true)
    public static class FileCondition {
        /**
         * 文件扩展名
         */
        private List<String> extensions;

        /**
         * 文件大小范围（字节）
         */
        private SizeRange sizeRange;

        /**
         * 文件数量范围
         */
        private CountRange countRange;

        /**
         * 包含的编程语言
         */
        private List<String> programmingLanguages;
    }

    @Data
    @Accessors(chain = true)
    public static class StudentCondition {
        /**
         * 学生水平
         */
        private List<String> levels;

        /**
         * 年级
         */
        private List<String> grades;

        /**
         * 专业
         */
        private List<String> majors;

        /**
         * 院校类型
         */
        private List<String> schoolTypes;
    }

    @Data
    @Accessors(chain = true)
    public static class CourseCondition {
        /**
         * 课程类型
         */
        private List<String> courseTypes;

        /**
         * 课程难度
         */
        private List<String> difficulties;

        /**
         * 学期阶段
         */
        private List<String> semesterStages;

        /**
         * 课程标签
         */
        private List<String> tags;
    }

    @Data
    @Accessors(chain = true)
    public static class TimeCondition {
        /**
         * 一周中的天
         */
        private List<String> daysOfWeek;

        /**
         * 小时范围
         */
        private TimeRange hourRange;

        /**
         * 学期时间
         */
        private List<String> semesterPeriods;
    }

    @Data
    @Accessors(chain = true)
    public static class SizeRange {
        private Long min;
        private Long max;
    }

    @Data
    @Accessors(chain = true)
    public static class CountRange {
        private Integer min;
        private Integer max;
    }

    @Data
    @Accessors(chain = true)
    public static class TimeRange {
        private Integer startHour;
        private Integer endHour;
    }
}
