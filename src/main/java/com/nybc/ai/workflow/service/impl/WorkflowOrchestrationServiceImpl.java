package com.nybc.ai.workflow.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.infra.ai.AiModelService;
import com.nybc.ai.infra.ai.AiModelServiceFactory;
import com.nybc.ai.workflow.dto.TaskMatchingRule;
import com.nybc.ai.workflow.dto.WorkflowTemplateRequest;
import com.nybc.ai.workflow.dto.WorkflowTemplateResponse;
import com.nybc.ai.workflow.entity.WorkflowTemplate;
import com.nybc.ai.workflow.mapper.WorkflowTemplateMapper;
import com.nybc.ai.workflow.service.WorkflowOrchestrationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述：工作流编排服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class WorkflowOrchestrationServiceImpl implements WorkflowOrchestrationService {

    @Resource
    private WorkflowTemplateMapper workflowTemplateMapper;

    @Resource
    private AiModelServiceFactory aiModelServiceFactory;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${app.ai.default-provider:deepseek}")
    private String defaultAiProvider;

    @Override
    public WorkflowTemplateResponse createWorkflowTemplate(WorkflowTemplateRequest request) {
        try {
            log.info("开始创建工作流模板: {}", request.getTemplateName());

            // 1. 验证模板名称唯一性
            if (workflowTemplateMapper.existsByNameAndTenant(request.getTemplateName(), request.getTenantId())) {
                throw new IllegalArgumentException("工作流模板名称已存在");
            }

            // 2. 构建工作流模板
            WorkflowTemplate template = new WorkflowTemplate()
                    .setTemplateName(request.getTemplateName())
                    .setDescription(request.getDescription())
                    .setTaskType(request.getTaskType())
                    .setWorkflowType(request.getWorkflowType())
                    .setChainName(generateChainName(request.getTemplateName()))
                    .setElExpression(request.getElExpression())
                    .setNodes(request.getNodes())
                    .setMatchingCondition(request.getMatchingCondition())
                    .setVersion("1.0")
                    .setIsDefault(request.getIsDefault())
                    .setEnabled(true)
                    .setPriority(request.getPriority())
                    .setTenantId(request.getTenantId())
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now())
                    .setCreateUser(request.getCreateUser());

            // 3. 保存到数据库
            workflowTemplateMapper.insert(template);

            // 4. 同步到LiteFlow规则链
            syncToLiteFlowRuleChain(template);

            log.info("工作流模板创建成功: ID[{}], 名称[{}]", template.getId(), template.getTemplateName());
            return convertToResponse(template);

        } catch (Exception e) {
            log.error("创建工作流模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建工作流模板失败", e);
        }
    }

    @Override
    public WorkflowTemplate matchWorkflowByTask(String taskType, Map<String, Object> taskContext, 
                                               Long tenantId, Long assignmentId) {
        try {
            log.info("开始匹配工作流: 任务类型[{}], 租户ID[{}]", taskType, tenantId);

            // 1. 获取所有可能的工作流模板
            List<WorkflowTemplate> candidates = workflowTemplateMapper.findByTaskTypeAndTenant(taskType, tenantId);

            // 2. 如果没有租户特定的模板，查找公共模板
            if (candidates.isEmpty()) {
                candidates = workflowTemplateMapper.findByTaskTypeAndTenant(taskType, null);
            }

            // 3. 根据匹配条件计算最佳匹配
            WorkflowTemplate bestMatch = findBestMatch(candidates, taskContext);

            if (bestMatch == null) {
                // 4. 如果没有匹配的模板，返回默认模板
                bestMatch = workflowTemplateMapper.findDefaultByTaskType(taskType);
            }

            if (bestMatch == null) {
                throw new IllegalStateException("未找到匹配的工作流模板: " + taskType);
            }

            log.info("匹配到工作流模板: ID[{}], 名称[{}]", bestMatch.getId(), bestMatch.getTemplateName());
            return bestMatch;

        } catch (Exception e) {
            log.error("匹配工作流失败: {}", e.getMessage(), e);
            throw new RuntimeException("匹配工作流失败", e);
        }
    }

    @Override
    public WorkflowTemplateResponse updateWorkflowTemplate(Long templateId, WorkflowTemplateRequest request) {
        try {
            log.info("开始更新工作流模板: ID[{}]", templateId);

            WorkflowTemplate existing = workflowTemplateMapper.findById(templateId);
            if (existing == null) {
                throw new IllegalArgumentException("工作流模板不存在: " + templateId);
            }

            // 更新模板信息
            existing.setTemplateName(request.getTemplateName())
                    .setDescription(request.getDescription())
                    .setTaskType(request.getTaskType())
                    .setWorkflowType(request.getWorkflowType())
                    .setElExpression(request.getElExpression())
                    .setNodes(request.getNodes())
                    .setMatchingCondition(request.getMatchingCondition())
                    .setPriority(request.getPriority())
                    .setUpdateTime(LocalDateTime.now())
                    .setUpdateUser(request.getUpdateUser());

            // 版本号递增
            existing.setVersion(incrementVersion(existing.getVersion()));

            workflowTemplateMapper.update(existing);

            // 同步到LiteFlow
            syncToLiteFlowRuleChain(existing);

            log.info("工作流模板更新成功: ID[{}]", templateId);
            return convertToResponse(existing);

        } catch (Exception e) {
            log.error("更新工作流模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新工作流模板失败", e);
        }
    }

    @Override
    public List<WorkflowTemplate> getWorkflowTemplates(Long tenantId) {
        try {
            return workflowTemplateMapper.findByTenant(tenantId);
        } catch (Exception e) {
            log.error("获取工作流模板列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取工作流模板列表失败", e);
        }
    }

    @Override
    public WorkflowTemplateResponse getWorkflowTemplateDetail(Long templateId) {
        try {
            WorkflowTemplate template = workflowTemplateMapper.findById(templateId);
            if (template == null) {
                throw new IllegalArgumentException("工作流模板不存在: " + templateId);
            }
            return convertToResponse(template);
        } catch (Exception e) {
            log.error("获取工作流模板详情失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取工作流模板详情失败", e);
        }
    }

    @Override
    public void deleteWorkflowTemplate(Long templateId) {
        try {
            log.info("开始删除工作流模板: ID[{}]", templateId);

            WorkflowTemplate template = workflowTemplateMapper.findById(templateId);
            if (template == null) {
                throw new IllegalArgumentException("工作流模板不存在: " + templateId);
            }

            // 检查是否为默认模板
            if (Boolean.TRUE.equals(template.getIsDefault())) {
                throw new IllegalStateException("不能删除默认工作流模板");
            }

            workflowTemplateMapper.deleteById(templateId);

            log.info("工作流模板删除成功: ID[{}]", templateId);

        } catch (Exception e) {
            log.error("删除工作流模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("删除工作流模板失败", e);
        }
    }

    @Override
    public void configureTaskMatchingRules(List<TaskMatchingRule> rules) {
        // 实现任务匹配规则配置逻辑
        log.info("配置任务匹配规则: 规则数量[{}]", rules.size());
        // 这里可以将规则保存到数据库或缓存中
    }

    @Override
    public List<TaskMatchingRule> getTaskMatchingRules(Long tenantId) {
        // 实现获取任务匹配规则逻辑
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> validateWorkflowTemplate(Long templateId) {
        try {
            log.info("开始验证工作流模板: ID[{}]", templateId);

            WorkflowTemplate template = workflowTemplateMapper.findById(templateId);
            if (template == null) {
                throw new IllegalArgumentException("工作流模板不存在: " + templateId);
            }

            Map<String, Object> result = new HashMap<>();
            List<String> errors = new ArrayList<>();
            List<String> warnings = new ArrayList<>();

            // 验证EL表达式语法
            if (template.getElExpression() == null || template.getElExpression().trim().isEmpty()) {
                errors.add("EL表达式不能为空");
            }

            // 验证节点配置
            if (template.getNodes() == null || template.getNodes().isEmpty()) {
                warnings.add("工作流节点为空");
            }

            // 验证组件存在性
            validateComponents(template, errors, warnings);

            result.put("valid", errors.isEmpty());
            result.put("errors", errors);
            result.put("warnings", warnings);
            result.put("validateTime", LocalDateTime.now());

            log.info("工作流模板验证完成: ID[{}], 有效[{}]", templateId, errors.isEmpty());
            return result;

        } catch (Exception e) {
            log.error("验证工作流模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("验证工作流模板失败", e);
        }
    }

    @Override
    public WorkflowTemplateResponse copyWorkflowTemplate(Long sourceTemplateId, String newTemplateName, Long tenantId) {
        try {
            log.info("开始复制工作流模板: 源ID[{}], 新名称[{}]", sourceTemplateId, newTemplateName);

            WorkflowTemplate source = workflowTemplateMapper.findById(sourceTemplateId);
            if (source == null) {
                throw new IllegalArgumentException("源工作流模板不存在: " + sourceTemplateId);
            }

            WorkflowTemplate copy = new WorkflowTemplate();
            BeanUtils.copyProperties(source, copy);
            copy.setId(null)
                    .setTemplateName(newTemplateName)
                    .setChainName(generateChainName(newTemplateName))
                    .setVersion("1.0")
                    .setIsDefault(false)
                    .setTenantId(tenantId)
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now());

            workflowTemplateMapper.insert(copy);
            syncToLiteFlowRuleChain(copy);

            log.info("工作流模板复制成功: 新ID[{}]", copy.getId());
            return convertToResponse(copy);

        } catch (Exception e) {
            log.error("复制工作流模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("复制工作流模板失败", e);
        }
    }

    @Override
    public Map<String, Object> getWorkflowExecutionStats(Long templateId, Integer days) {
        // 实现工作流执行统计逻辑
        Map<String, Object> stats = new HashMap<>();
        stats.put("templateId", templateId);
        stats.put("totalExecutions", 0);
        stats.put("successRate", 0.0);
        stats.put("avgExecutionTime", 0.0);
        stats.put("period", days + " days");
        return stats;
    }

    @Override
    public WorkflowTemplateResponse generateWorkflowSuggestion(String taskType, Map<String, Object> requirements) {
        try {
            log.info("开始生成工作流建议: 任务类型[{}]", taskType);

            // 使用AI生成工作流建议
            String suggestionPrompt = buildWorkflowSuggestionPrompt(taskType, requirements);
            AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
            String aiResponse = aiService.call(suggestionPrompt, Collections.singletonMap("temperature", 0.3));

            // 解析AI响应并构建工作流模板
            WorkflowTemplateResponse suggestion = parseAiWorkflowSuggestion(aiResponse, taskType);

            log.info("工作流建议生成完成: 任务类型[{}]", taskType);
            return suggestion;

        } catch (Exception e) {
            log.error("生成工作流建议失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成工作流建议失败", e);
        }
    }

    @Override
    public String exportWorkflowTemplate(Long templateId) {
        try {
            WorkflowTemplate template = workflowTemplateMapper.findById(templateId);
            if (template == null) {
                throw new IllegalArgumentException("工作流模板不存在: " + templateId);
            }
            return objectMapper.writeValueAsString(template);
        } catch (Exception e) {
            log.error("导出工作流模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出工作流模板失败", e);
        }
    }

    @Override
    public WorkflowTemplateResponse importWorkflowTemplate(String templateJson, Long tenantId) {
        try {
            WorkflowTemplate template = objectMapper.readValue(templateJson, WorkflowTemplate.class);
            template.setId(null)
                    .setTenantId(tenantId)
                    .setCreateTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now());

            workflowTemplateMapper.insert(template);
            syncToLiteFlowRuleChain(template);

            return convertToResponse(template);
        } catch (Exception e) {
            log.error("导入工作流模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("导入工作流模板失败", e);
        }
    }

    /**
     * 查找最佳匹配的工作流模板
     */
    private WorkflowTemplate findBestMatch(List<WorkflowTemplate> candidates, Map<String, Object> taskContext) {
        if (candidates.isEmpty()) {
            return null;
        }

        // 简单的匹配逻辑：按优先级排序，返回第一个启用的模板
        return candidates.stream()
                .filter(template -> Boolean.TRUE.equals(template.getEnabled()))
                .sorted((t1, t2) -> Integer.compare(t2.getPriority(), t1.getPriority()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 生成规则链名称
     */
    private String generateChainName(String templateName) {
        return "workflow_" + templateName.toLowerCase().replaceAll("[^a-z0-9]", "_");
    }

    /**
     * 版本号递增
     */
    private String incrementVersion(String currentVersion) {
        try {
            String[] parts = currentVersion.split("\\.");
            int major = Integer.parseInt(parts[0]);
            int minor = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
            return major + "." + (minor + 1);
        } catch (Exception e) {
            return "1.1";
        }
    }

    /**
     * 同步到LiteFlow规则链
     */
    private void syncToLiteFlowRuleChain(WorkflowTemplate template) {
        // 这里应该调用RuleAdminService来同步规则链
        log.info("同步工作流模板到LiteFlow: 链名[{}]", template.getChainName());
    }

    /**
     * 验证组件存在性
     */
    private void validateComponents(WorkflowTemplate template, List<String> errors, List<String> warnings) {
        if (template.getNodes() != null) {
            for (WorkflowTemplate.WorkflowNode node : template.getNodes()) {
                if (node.getComponentName() != null && !isComponentExists(node.getComponentName())) {
                    warnings.add("组件不存在: " + node.getComponentName());
                }
            }
        }
    }

    /**
     * 检查组件是否存在
     */
    private boolean isComponentExists(String componentName) {
        // 这里应该检查Spring容器中是否存在该组件
        return true; // 简化实现
    }

    /**
     * 构建工作流建议Prompt
     */
    private String buildWorkflowSuggestionPrompt(String taskType, Map<String, Object> requirements) {
        return String.format("""
                请为以下任务类型设计一个LiteFlow工作流模板：
                
                ## 任务类型：%s
                
                ## 需求描述：
                %s
                
                ## 请提供：
                1. 工作流节点设计
                2. LiteFlow EL表达式
                3. 节点执行顺序说明
                4. 适用场景描述
                
                请以JSON格式返回工作流模板配置。
                """, taskType, requirements.toString());
    }

    /**
     * 解析AI工作流建议
     */
    private WorkflowTemplateResponse parseAiWorkflowSuggestion(String aiResponse, String taskType) {
        // 简化实现，实际应该解析AI返回的JSON
        WorkflowTemplateResponse response = new WorkflowTemplateResponse();
        response.setTemplateName("AI建议_" + taskType);
        response.setDescription("基于AI生成的工作流建议");
        response.setTaskType(taskType);
        response.setWorkflowType("SEQUENTIAL");
        response.setElExpression("THEN(initContextCmp, syntaxCheckCmp, logicAnalysisCmp, finalScoreCmp)");
        return response;
    }

    /**
     * 转换为响应对象
     */
    private WorkflowTemplateResponse convertToResponse(WorkflowTemplate template) {
        WorkflowTemplateResponse response = new WorkflowTemplateResponse();
        BeanUtils.copyProperties(template, response);
        return response;
    }
}
