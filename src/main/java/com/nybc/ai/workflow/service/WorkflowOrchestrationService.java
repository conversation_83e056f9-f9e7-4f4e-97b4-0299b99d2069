package com.nybc.ai.workflow.service;

import com.nybc.ai.workflow.dto.WorkflowTemplateRequest;
import com.nybc.ai.workflow.dto.WorkflowTemplateResponse;
import com.nybc.ai.workflow.dto.TaskMatchingRule;
import com.nybc.ai.workflow.entity.WorkflowTemplate;

import java.util.List;
import java.util.Map;

/**
 * 类描述：工作流编排服务接口
 * 支持工作流模板管理、任务匹配规则配置、动态工作流编排
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface WorkflowOrchestrationService {

    /**
     * 创建工作流模板
     *
     * @param request 工作流模板创建请求
     * @return 创建的工作流模板
     */
    WorkflowTemplateResponse createWorkflowTemplate(WorkflowTemplateRequest request);

    /**
     * 更新工作流模板
     *
     * @param templateId 模板ID
     * @param request    更新请求
     * @return 更新后的工作流模板
     */
    WorkflowTemplateResponse updateWorkflowTemplate(Long templateId, WorkflowTemplateRequest request);

    /**
     * 根据任务特征智能匹配工作流
     *
     * @param taskType     任务类型：HOMEWORK_EVALUATION, CODE_REVIEW, DOCUMENT_ANALYSIS, PROJECT_ASSESSMENT
     * @param taskContext  任务上下文信息
     * @param tenantId     租户ID
     * @param assignmentId 作业ID（可选）
     * @return 匹配的工作流模板
     */
    WorkflowTemplate matchWorkflowByTask(String taskType, Map<String, Object> taskContext, 
                                        Long tenantId, Long assignmentId);

    /**
     * 获取所有工作流模板
     *
     * @param tenantId 租户ID，为空则获取公共模板
     * @return 工作流模板列表
     */
    List<WorkflowTemplate> getWorkflowTemplates(Long tenantId);

    /**
     * 根据ID获取工作流模板详情
     *
     * @param templateId 模板ID
     * @return 工作流模板详情
     */
    WorkflowTemplateResponse getWorkflowTemplateDetail(Long templateId);

    /**
     * 删除工作流模板
     *
     * @param templateId 模板ID
     */
    void deleteWorkflowTemplate(Long templateId);

    /**
     * 配置任务匹配规则
     *
     * @param rules 匹配规则列表
     */
    void configureTaskMatchingRules(List<TaskMatchingRule> rules);

    /**
     * 获取任务匹配规则
     *
     * @param tenantId 租户ID
     * @return 匹配规则列表
     */
    List<TaskMatchingRule> getTaskMatchingRules(Long tenantId);

    /**
     * 验证工作流模板的有效性
     *
     * @param templateId 模板ID
     * @return 验证结果
     */
    Map<String, Object> validateWorkflowTemplate(Long templateId);

    /**
     * 复制工作流模板
     *
     * @param sourceTemplateId 源模板ID
     * @param newTemplateName  新模板名称
     * @param tenantId         目标租户ID
     * @return 复制的工作流模板
     */
    WorkflowTemplateResponse copyWorkflowTemplate(Long sourceTemplateId, String newTemplateName, Long tenantId);

    /**
     * 获取工作流执行统计
     *
     * @param templateId 模板ID
     * @param days       统计天数
     * @return 执行统计信息
     */
    Map<String, Object> getWorkflowExecutionStats(Long templateId, Integer days);

    /**
     * 生成工作流模板建议
     *
     * @param taskType    任务类型
     * @param requirements 需求描述
     * @return 建议的工作流模板
     */
    WorkflowTemplateResponse generateWorkflowSuggestion(String taskType, Map<String, Object> requirements);

    /**
     * 导出工作流模板
     *
     * @param templateId 模板ID
     * @return 模板配置JSON
     */
    String exportWorkflowTemplate(Long templateId);

    /**
     * 导入工作流模板
     *
     * @param templateJson 模板配置JSON
     * @param tenantId     目标租户ID
     * @return 导入的工作流模板
     */
    WorkflowTemplateResponse importWorkflowTemplate(String templateJson, Long tenantId);
}
