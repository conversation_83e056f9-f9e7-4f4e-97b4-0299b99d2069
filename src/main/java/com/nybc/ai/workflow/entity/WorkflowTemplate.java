package com.nybc.ai.workflow.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 类描述：工作流模板实体类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class WorkflowTemplate {

    /**
     * 模板ID
     */
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 任务类型：HOMEWORK_EVALUATION, CODE_REVIEW, DOCUMENT_ANALYSIS, PROJECT_ASSESSMENT
     */
    private String taskType;

    /**
     * 工作流类型：SEQUENTIAL, PARALLEL, CONDITIONAL, HYBRID
     */
    private String workflowType;

    /**
     * LiteFlow规则链名称
     */
    private String chainName;

    /**
     * LiteFlow EL表达式
     */
    private String elExpression;

    /**
     * 工作流节点配置
     */
    private List<WorkflowNode> nodes;

    /**
     * 任务匹配条件
     */
    private TaskMatchingCondition matchingCondition;

    /**
     * 模板版本
     */
    private String version;

    /**
     * 是否为默认模板
     */
    private Boolean isDefault;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 优先级（数字越大优先级越高）
     */
    private Integer priority;

    /**
     * 租户ID（为空表示公共模板）
     */
    private Long tenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 更新人
     */
    private Long updateUser;

    @Data
    @Accessors(chain = true)
    public static class WorkflowNode {
        /**
         * 节点ID
         */
        private String nodeId;

        /**
         * 节点名称
         */
        private String nodeName;

        /**
         * 节点类型：COMPONENT, SUB_CHAIN, CONDITION, PARALLEL_GROUP
         */
        private String nodeType;

        /**
         * 组件名称（对应LiteFlow组件）
         */
        private String componentName;

        /**
         * 节点配置参数
         */
        private Map<String, Object> nodeConfig;

        /**
         * 节点描述
         */
        private String description;

        /**
         * 是否必需节点
         */
        private Boolean required;

        /**
         * 执行顺序
         */
        private Integer order;

        /**
         * 条件表达式（用于条件节点）
         */
        private String conditionExpression;

        /**
         * 子节点列表（用于并行组或子链）
         */
        private List<WorkflowNode> children;
    }

    @Data
    @Accessors(chain = true)
    public static class TaskMatchingCondition {
        /**
         * 任务类型匹配
         */
        private List<String> taskTypes;

        /**
         * 文件类型匹配
         */
        private List<String> fileTypes;

        /**
         * 编程语言匹配
         */
        private List<String> programmingLanguages;

        /**
         * 课程类型匹配
         */
        private List<String> courseTypes;

        /**
         * 学生水平匹配
         */
        private List<String> studentLevels;

        /**
         * 院校类型匹配
         */
        private List<String> schoolTypes;

        /**
         * 自定义匹配条件
         */
        private Map<String, Object> customConditions;

        /**
         * 匹配权重
         */
        private Double weight;
    }
}
