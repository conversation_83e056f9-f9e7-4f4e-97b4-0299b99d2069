package com.nybc.ai.workflow.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.workflow.dto.TaskMatchingRule;
import com.nybc.ai.workflow.dto.WorkflowTemplateRequest;
import com.nybc.ai.workflow.dto.WorkflowTemplateResponse;
import com.nybc.ai.workflow.entity.WorkflowTemplate;
import com.nybc.ai.workflow.service.WorkflowOrchestrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 类描述：工作流编排控制器
 * 支持工作流模板管理、任务匹配规则配置、动态工作流编排
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/ai/workflow")
@Tag(name = "工作流编排", description = "支持工作流模板管理、任务匹配、动态编排等功能")
public class WorkflowOrchestrationController {

    @Resource
    private WorkflowOrchestrationService workflowOrchestrationService;

    /**
     * 创建工作流模板
     *
     * @param request 工作流模板创建请求
     * @return 创建的工作流模板
     */
    @PostMapping("/templates")
    @Operation(summary = "创建工作流模板", 
               description = "创建新的工作流模板，支持自定义节点配置和匹配条件")
    public ResultInfo<WorkflowTemplateResponse> createWorkflowTemplate(
            @Valid @RequestBody WorkflowTemplateRequest request) {
        try {
            WorkflowTemplateResponse response = workflowOrchestrationService.createWorkflowTemplate(request);
            return ResultInfo.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("创建工作流模板失败，参数错误: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), e.getMessage());
        } catch (Exception e) {
            log.error("创建工作流模板失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "创建工作流模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新工作流模板
     *
     * @param templateId 模板ID
     * @param request    更新请求
     * @return 更新后的工作流模板
     */
    @PutMapping("/templates/{templateId}")
    @Operation(summary = "更新工作流模板", 
               description = "更新指定的工作流模板配置")
    public ResultInfo<WorkflowTemplateResponse> updateWorkflowTemplate(
            @Parameter(description = "模板ID") @PathVariable Long templateId,
            @Valid @RequestBody WorkflowTemplateRequest request) {
        try {
            WorkflowTemplateResponse response = workflowOrchestrationService.updateWorkflowTemplate(templateId, request);
            return ResultInfo.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("更新工作流模板失败，参数错误: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), e.getMessage());
        } catch (Exception e) {
            log.error("更新工作流模板失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "更新工作流模板失败：" + e.getMessage());
        }
    }

    /**
     * 根据任务特征智能匹配工作流
     *
     * @param taskType     任务类型
     * @param taskContext  任务上下文信息
     * @param tenantId     租户ID
     * @param assignmentId 作业ID
     * @return 匹配的工作流模板
     */
    @PostMapping("/match")
    @Operation(summary = "智能匹配工作流", 
               description = "根据任务特征自动匹配最适合的工作流模板")
    public ResultInfo<WorkflowTemplate> matchWorkflowByTask(
            @Parameter(description = "任务类型") @RequestParam String taskType,
            @Parameter(description = "任务上下文") @RequestBody Map<String, Object> taskContext,
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Parameter(description = "作业ID") @RequestParam(required = false) Long assignmentId) {
        try {
            WorkflowTemplate template = workflowOrchestrationService.matchWorkflowByTask(
                    taskType, taskContext, tenantId, assignmentId);
            return ResultInfo.success(template);
        } catch (IllegalStateException e) {
            log.warn("匹配工作流失败: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.NOT_FOUND.value(), e.getMessage());
        } catch (Exception e) {
            log.error("匹配工作流失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "匹配工作流失败：" + e.getMessage());
        }
    }

    /**
     * 获取工作流模板列表
     *
     * @param tenantId 租户ID
     * @return 工作流模板列表
     */
    @GetMapping("/templates")
    @Operation(summary = "获取工作流模板列表", 
               description = "获取指定租户的所有工作流模板")
    public ResultInfo<List<WorkflowTemplate>> getWorkflowTemplates(
            @Parameter(description = "租户ID，为空则获取公共模板") @RequestParam(required = false) Long tenantId) {
        try {
            List<WorkflowTemplate> templates = workflowOrchestrationService.getWorkflowTemplates(tenantId);
            return ResultInfo.success(templates);
        } catch (Exception e) {
            log.error("获取工作流模板列表失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取工作流模板列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取工作流模板详情
     *
     * @param templateId 模板ID
     * @return 工作流模板详情
     */
    @GetMapping("/templates/{templateId}")
    @Operation(summary = "获取工作流模板详情", 
               description = "获取指定工作流模板的详细配置信息")
    public ResultInfo<WorkflowTemplateResponse> getWorkflowTemplateDetail(
            @Parameter(description = "模板ID") @PathVariable Long templateId) {
        try {
            WorkflowTemplateResponse response = workflowOrchestrationService.getWorkflowTemplateDetail(templateId);
            return ResultInfo.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("获取工作流模板详情失败: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.NOT_FOUND.value(), e.getMessage());
        } catch (Exception e) {
            log.error("获取工作流模板详情失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取工作流模板详情失败：" + e.getMessage());
        }
    }

    /**
     * 删除工作流模板
     *
     * @param templateId 模板ID
     * @return 操作结果
     */
    @DeleteMapping("/templates/{templateId}")
    @Operation(summary = "删除工作流模板", 
               description = "删除指定的工作流模板")
    public ResultInfo<Void> deleteWorkflowTemplate(
            @Parameter(description = "模板ID") @PathVariable Long templateId) {
        try {
            workflowOrchestrationService.deleteWorkflowTemplate(templateId);
            return ResultInfo.success();
        } catch (IllegalArgumentException | IllegalStateException e) {
            log.warn("删除工作流模板失败: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), e.getMessage());
        } catch (Exception e) {
            log.error("删除工作流模板失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "删除工作流模板失败：" + e.getMessage());
        }
    }

    /**
     * 配置任务匹配规则
     *
     * @param rules 匹配规则列表
     * @return 操作结果
     */
    @PostMapping("/matching-rules")
    @Operation(summary = "配置任务匹配规则", 
               description = "配置任务与工作流的匹配规则")
    public ResultInfo<Void> configureTaskMatchingRules(
            @Valid @RequestBody List<TaskMatchingRule> rules) {
        try {
            workflowOrchestrationService.configureTaskMatchingRules(rules);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("配置任务匹配规则失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "配置任务匹配规则失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务匹配规则
     *
     * @param tenantId 租户ID
     * @return 匹配规则列表
     */
    @GetMapping("/matching-rules")
    @Operation(summary = "获取任务匹配规则", 
               description = "获取指定租户的任务匹配规则")
    public ResultInfo<List<TaskMatchingRule>> getTaskMatchingRules(
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {
        try {
            List<TaskMatchingRule> rules = workflowOrchestrationService.getTaskMatchingRules(tenantId);
            return ResultInfo.success(rules);
        } catch (Exception e) {
            log.error("获取任务匹配规则失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取任务匹配规则失败：" + e.getMessage());
        }
    }

    /**
     * 验证工作流模板
     *
     * @param templateId 模板ID
     * @return 验证结果
     */
    @PostMapping("/templates/{templateId}/validate")
    @Operation(summary = "验证工作流模板", 
               description = "验证工作流模板的配置是否正确")
    public ResultInfo<Map<String, Object>> validateWorkflowTemplate(
            @Parameter(description = "模板ID") @PathVariable Long templateId) {
        try {
            Map<String, Object> result = workflowOrchestrationService.validateWorkflowTemplate(templateId);
            return ResultInfo.success(result);
        } catch (IllegalArgumentException e) {
            log.warn("验证工作流模板失败: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.NOT_FOUND.value(), e.getMessage());
        } catch (Exception e) {
            log.error("验证工作流模板失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "验证工作流模板失败：" + e.getMessage());
        }
    }

    /**
     * 复制工作流模板
     *
     * @param sourceTemplateId 源模板ID
     * @param newTemplateName  新模板名称
     * @param tenantId         目标租户ID
     * @return 复制的工作流模板
     */
    @PostMapping("/templates/{sourceTemplateId}/copy")
    @Operation(summary = "复制工作流模板", 
               description = "复制现有的工作流模板")
    public ResultInfo<WorkflowTemplateResponse> copyWorkflowTemplate(
            @Parameter(description = "源模板ID") @PathVariable Long sourceTemplateId,
            @Parameter(description = "新模板名称") @RequestParam String newTemplateName,
            @Parameter(description = "目标租户ID") @RequestParam Long tenantId) {
        try {
            WorkflowTemplateResponse response = workflowOrchestrationService.copyWorkflowTemplate(
                    sourceTemplateId, newTemplateName, tenantId);
            return ResultInfo.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("复制工作流模板失败: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), e.getMessage());
        } catch (Exception e) {
            log.error("复制工作流模板失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "复制工作流模板失败：" + e.getMessage());
        }
    }

    /**
     * 获取工作流执行统计
     *
     * @param templateId 模板ID
     * @param days       统计天数
     * @return 执行统计信息
     */
    @GetMapping("/templates/{templateId}/stats")
    @Operation(summary = "获取工作流执行统计", 
               description = "获取工作流模板的执行统计信息")
    public ResultInfo<Map<String, Object>> getWorkflowExecutionStats(
            @Parameter(description = "模板ID") @PathVariable Long templateId,
            @Parameter(description = "统计天数，默认30天") @RequestParam(defaultValue = "30") Integer days) {
        try {
            Map<String, Object> stats = workflowOrchestrationService.getWorkflowExecutionStats(templateId, days);
            return ResultInfo.success(stats);
        } catch (Exception e) {
            log.error("获取工作流执行统计失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取工作流执行统计失败：" + e.getMessage());
        }
    }

    /**
     * 生成工作流模板建议
     *
     * @param taskType     任务类型
     * @param requirements 需求描述
     * @return 建议的工作流模板
     */
    @PostMapping("/templates/suggest")
    @Operation(summary = "生成工作流建议", 
               description = "基于AI生成工作流模板建议")
    public ResultInfo<WorkflowTemplateResponse> generateWorkflowSuggestion(
            @Parameter(description = "任务类型") @RequestParam String taskType,
            @Parameter(description = "需求描述") @RequestBody Map<String, Object> requirements) {
        try {
            WorkflowTemplateResponse suggestion = workflowOrchestrationService.generateWorkflowSuggestion(
                    taskType, requirements);
            return ResultInfo.success(suggestion);
        } catch (Exception e) {
            log.error("生成工作流建议失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "生成工作流建议失败：" + e.getMessage());
        }
    }

    /**
     * 导出工作流模板
     *
     * @param templateId 模板ID
     * @return 模板配置JSON
     */
    @GetMapping("/templates/{templateId}/export")
    @Operation(summary = "导出工作流模板", 
               description = "导出工作流模板配置为JSON格式")
    public ResultInfo<String> exportWorkflowTemplate(
            @Parameter(description = "模板ID") @PathVariable Long templateId) {
        try {
            String templateJson = workflowOrchestrationService.exportWorkflowTemplate(templateId);
            return ResultInfo.success(templateJson);
        } catch (IllegalArgumentException e) {
            log.warn("导出工作流模板失败: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.NOT_FOUND.value(), e.getMessage());
        } catch (Exception e) {
            log.error("导出工作流模板失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "导出工作流模板失败：" + e.getMessage());
        }
    }

    /**
     * 导入工作流模板
     *
     * @param templateJson 模板配置JSON
     * @param tenantId     目标租户ID
     * @return 导入的工作流模板
     */
    @PostMapping("/templates/import")
    @Operation(summary = "导入工作流模板", 
               description = "从JSON配置导入工作流模板")
    public ResultInfo<WorkflowTemplateResponse> importWorkflowTemplate(
            @Parameter(description = "模板配置JSON") @RequestBody String templateJson,
            @Parameter(description = "目标租户ID") @RequestParam Long tenantId) {
        try {
            WorkflowTemplateResponse response = workflowOrchestrationService.importWorkflowTemplate(
                    templateJson, tenantId);
            return ResultInfo.success(response);
        } catch (Exception e) {
            log.error("导入工作流模板失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "导入工作流模板失败：" + e.getMessage());
        }
    }
}
