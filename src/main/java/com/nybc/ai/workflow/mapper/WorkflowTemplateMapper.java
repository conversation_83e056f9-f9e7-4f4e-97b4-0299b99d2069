package com.nybc.ai.workflow.mapper;

import com.nybc.ai.workflow.entity.WorkflowTemplate;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 类描述：工作流模板数据访问层
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Repository
public interface WorkflowTemplateMapper {

    /**
     * 插入工作流模板
     *
     * @param template 工作流模板
     * @return 影响行数
     */
    int insert(WorkflowTemplate template);

    /**
     * 根据ID查询工作流模板
     *
     * @param id 模板ID
     * @return 工作流模板
     */
    WorkflowTemplate findById(@Param("id") Long id);

    /**
     * 更新工作流模板
     *
     * @param template 工作流模板
     * @return 影响行数
     */
    int update(WorkflowTemplate template);

    /**
     * 根据ID删除工作流模板
     *
     * @param id 模板ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 检查模板名称是否存在
     *
     * @param templateName 模板名称
     * @param tenantId     租户ID
     * @return 是否存在
     */
    boolean existsByNameAndTenant(@Param("templateName") String templateName, @Param("tenantId") Long tenantId);

    /**
     * 根据任务类型和租户查询工作流模板
     *
     * @param taskType 任务类型
     * @param tenantId 租户ID
     * @return 工作流模板列表
     */
    List<WorkflowTemplate> findByTaskTypeAndTenant(@Param("taskType") String taskType, @Param("tenantId") Long tenantId);

    /**
     * 根据租户查询工作流模板
     *
     * @param tenantId 租户ID
     * @return 工作流模板列表
     */
    List<WorkflowTemplate> findByTenant(@Param("tenantId") Long tenantId);

    /**
     * 查询默认工作流模板
     *
     * @param taskType 任务类型
     * @return 默认工作流模板
     */
    WorkflowTemplate findDefaultByTaskType(@Param("taskType") String taskType);

    /**
     * 根据链名称查询工作流模板
     *
     * @param chainName 链名称
     * @return 工作流模板
     */
    WorkflowTemplate findByChainName(@Param("chainName") String chainName);

    /**
     * 查询启用的工作流模板
     *
     * @param tenantId 租户ID
     * @return 启用的工作流模板列表
     */
    List<WorkflowTemplate> findEnabledByTenant(@Param("tenantId") Long tenantId);

    /**
     * 根据优先级查询工作流模板
     *
     * @param taskType 任务类型
     * @param tenantId 租户ID
     * @return 按优先级排序的工作流模板列表
     */
    List<WorkflowTemplate> findByTaskTypeOrderByPriority(@Param("taskType") String taskType, @Param("tenantId") Long tenantId);

    /**
     * 批量更新模板状态
     *
     * @param templateIds 模板ID列表
     * @param enabled     是否启用
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("templateIds") List<Long> templateIds, @Param("enabled") Boolean enabled);

    /**
     * 查询模板统计信息
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    java.util.Map<String, Object> getTemplateStats(@Param("tenantId") Long tenantId);
}
