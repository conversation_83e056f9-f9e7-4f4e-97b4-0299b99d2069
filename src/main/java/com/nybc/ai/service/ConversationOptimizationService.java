package com.nybc.ai.service;

import com.nybc.ai.service.dto.ConversationContext;
import com.nybc.ai.service.dto.ConversationSummary;

import java.util.List;
import java.util.Map;

/**
 * 类描述：对话优化服务接口
 * 提供多轮对话优化、上下文压缩、对话摘要等能力
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface ConversationOptimizationService {

    /**
     * 压缩对话上下文
     * 当对话历史过长时，智能压缩保留关键信息
     *
     * @param sessionId 会话ID
     * @param maxTokens 最大token限制
     * @return 压缩后的对话上下文
     */
    ConversationContext compressContext(String sessionId, int maxTokens);

    /**
     * 生成对话摘要
     * 对长对话进行智能摘要，提取关键信息
     *
     * @param sessionId 会话ID
     * @param summaryType 摘要类型：BRIEF, DETAILED, TECHNICAL
     * @return 对话摘要
     */
    ConversationSummary generateSummary(String sessionId, String summaryType);

    /**
     * 优化多轮对话流程
     * 基于对话历史和上下文，优化下一轮对话的Prompt
     *
     * @param sessionId 会话ID
     * @param currentPrompt 当前用户输入
     * @param domainContext 领域上下文（计算机、软件学院相关）
     * @return 优化后的Prompt
     */
    String optimizeMultiTurnPrompt(String sessionId, String currentPrompt, Map<String, Object> domainContext);

    /**
     * 检测对话主题转换
     * 识别对话是否转向新主题，决定是否需要重置上下文
     *
     * @param sessionId 会话ID
     * @param newMessage 新消息
     * @return 是否发生主题转换
     */
    boolean detectTopicShift(String sessionId, String newMessage);

    /**
     * 智能上下文管理
     * 根据对话内容和领域特点，智能管理上下文窗口
     *
     * @param sessionId 会话ID
     * @param maxContextLength 最大上下文长度
     * @return 管理后的上下文信息
     */
    ConversationContext manageContext(String sessionId, int maxContextLength);

    /**
     * 提取对话中的关键实体和概念
     * 专门针对计算机、软件学院的专业术语和概念
     *
     * @param sessionId 会话ID
     * @return 提取的关键实体和概念
     */
    Map<String, Object> extractDomainEntities(String sessionId);
}
