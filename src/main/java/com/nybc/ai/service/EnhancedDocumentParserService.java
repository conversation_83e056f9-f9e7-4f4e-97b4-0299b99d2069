package com.nybc.ai.service;

import com.nybc.ai.service.dto.DocumentParseResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 类描述：增强的文档解析服务接口
 * 专门处理PDF和Office文档的多模态内容提取
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface EnhancedDocumentParserService {

    /**
     * 解析PDF文档的多模态内容
     *
     * @param file PDF文件
     * @return 解析结果
     */
    DocumentParseResult parsePdfDocument(MultipartFile file);

    /**
     * 解析Word文档的多模态内容
     *
     * @param file Word文件
     * @return 解析结果
     */
    DocumentParseResult parseWordDocument(MultipartFile file);

    /**
     * 解析Excel文档的多模态内容
     *
     * @param file Excel文件
     * @return 解析结果
     */
    DocumentParseResult parseExcelDocument(MultipartFile file);

    /**
     * 解析PowerPoint文档的多模态内容
     *
     * @param file PowerPoint文件
     * @return 解析结果
     */
    DocumentParseResult parsePowerPointDocument(MultipartFile file);

    /**
     * 提取文档中的所有图片
     *
     * @param file 文档文件
     * @return 图片列表及其元数据
     */
    List<Map<String, Object>> extractAllImages(MultipartFile file);

    /**
     * 提取文档中的所有表格
     *
     * @param file 文档文件
     * @return 表格数据及其结构信息
     */
    List<Map<String, Object>> extractAllTables(MultipartFile file);

    /**
     * 提取文档的文本内容并保留格式信息
     *
     * @param file 文档文件
     * @return 带格式的文本内容
     */
    Map<String, Object> extractFormattedText(MultipartFile file);

    /**
     * 检测文档中的代码块
     *
     * @param file 文档文件
     * @return 代码块信息
     */
    List<Map<String, Object>> detectCodeBlocks(MultipartFile file);

    /**
     * 提取文档的元数据信息
     *
     * @param file 文档文件
     * @return 元数据信息
     */
    Map<String, Object> extractMetadata(MultipartFile file);

    /**
     * 分析文档的页面布局
     *
     * @param file 文档文件
     * @return 页面布局信息
     */
    List<Map<String, Object>> analyzePageLayout(MultipartFile file);
}
