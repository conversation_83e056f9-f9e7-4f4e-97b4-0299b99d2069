package com.nybc.ai.service;

import com.nybc.ai.service.dto.MultimodalDocumentAnalysis;
import com.nybc.ai.service.dto.DocumentStructure;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 类描述：多模态文档处理服务接口
 * 支持文字、表格、图片、代码片段等多种内容类型的智能处理
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface MultimodalDocumentService {

    /**
     * 全面分析多模态文档内容
     *
     * @param file 上传的文档文件
     * @param analysisType 分析类型：COMPREHENSIVE, TEXT_ONLY, VISUAL_FOCUS, STRUCTURE_ANALYSIS
     * @return 多模态文档分析结果
     */
    MultimodalDocumentAnalysis analyzeDocument(MultipartFile file, String analysisType);

    /**
     * 提取文档结构信息
     *
     * @param file 上传的文档文件
     * @return 文档结构信息
     */
    DocumentStructure extractDocumentStructure(MultipartFile file);

    /**
     * 处理文档中的图片内容
     *
     * @param file 上传的文档文件
     * @return 图片分析结果
     */
    List<Map<String, Object>> processDocumentImages(MultipartFile file);

    /**
     * 提取并分析文档中的表格
     *
     * @param file 上传的文档文件
     * @return 表格分析结果
     */
    List<Map<String, Object>> extractAndAnalyzeTables(MultipartFile file);

    /**
     * 识别并分析文档中的代码片段
     *
     * @param file 上传的文档文件
     * @return 代码片段分析结果
     */
    List<Map<String, Object>> identifyAndAnalyzeCode(MultipartFile file);

    /**
     * 智能文档分类
     *
     * @param file 上传的文档文件
     * @return 文档分类结果
     */
    Map<String, Object> classifyDocument(MultipartFile file);

    /**
     * 文档质量评估
     *
     * @param file 上传的文档文件
     * @param evaluationCriteria 评估标准
     * @return 质量评估结果
     */
    Map<String, Object> evaluateDocumentQuality(MultipartFile file, Map<String, Object> evaluationCriteria);

    /**
     * 生成文档摘要
     *
     * @param file 上传的文档文件
     * @param summaryType 摘要类型：BRIEF, DETAILED, TECHNICAL, ACADEMIC
     * @return 文档摘要
     */
    String generateDocumentSummary(MultipartFile file, String summaryType);

    /**
     * 检查文档格式规范
     *
     * @param file 上传的文档文件
     * @param formatStandards 格式标准
     * @return 格式检查结果
     */
    Map<String, Object> checkDocumentFormat(MultipartFile file, Map<String, Object> formatStandards);
}
