package com.nybc.ai.service.impl;

import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.aigc.generation.models.QwenParam;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.nybc.ai.domain.entity.AiModelConfig;
import com.nybc.ai.domain.mapper.AiModelConfigMapper;
import com.nybc.ai.service.ChatService;
import com.nybc.ai.service.dto.ChatRequest;
import io.reactivex.Flowable;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 类描述：使用DashScope SDK实现的聊天服务（已废弃）
 *
 * @deprecated 请使用UnifiedAiService替代
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
@ConditionalOnProperty(name = "app.ai.legacy.chat-service.enabled", havingValue = "true", matchIfMissing = false)
@Deprecated
public class DashScopeChatServiceImpl implements ChatService {

    public static final String PROVIDER_NAME = "dashscope";
    @Resource
    private AiModelConfigMapper aiModelConfigMapper;
    private AiModelConfig config;

    /**
     * 在服务初始化时加载配置
     */
    @PostConstruct
    public void init() {
        this.config = aiModelConfigMapper.findByProviderAndIsEnabledTrue(PROVIDER_NAME);
        if (this.config == null) {
            log.warn("未在数据库中找到启用的'{}'提供商配置，该服务将不可用。", PROVIDER_NAME);
        } else {
            log.info("成功加载'{}'提供商配置。", PROVIDER_NAME);
        }
    }

    @Override
    public Flux<String> streamChat(ChatRequest request) {
        if (config == null) {
            return Flux.error(new IllegalStateException("DashScope服务未正确配置或未启用。"));
        }

        try {
            Generation gen = new Generation();

            // 将我们的DTO转换为DashScope的Message对象
            List<Message> messages = request.getMessages().stream()
                    .map(m -> Message.builder()
                            .role(m.getRole().getValue())
                            .content(m.getContent())
                            .build())
                    .collect(Collectors.toList());

            // 使用数据库中或请求中指定的模型
            String model = request.getModel() != null ? request.getModel() : config.getChatModel();
            model = "deepseek-r1-0528";
            QwenParam param = QwenParam.builder()
                    .apiKey(config.getApiKey())
                    .model(model)
                    .messages(messages)
                    .resultFormat(QwenParam.ResultFormat.MESSAGE)
                    .build();

            Flowable<GenerationResult> resultFlowable = gen.streamCall(param);

            // 将DashScope的Flowable转换为Spring WebFlux的Flux
            return Flux.from(resultFlowable)
                    .map(generationResult -> generationResult.getOutput().getChoices().get(0).getMessage().getContent())
                    .filter(StringUtils::hasText); // 过滤掉空字符串的响应

        } catch (NoApiKeyException | InputRequiredException e) {
            log.error("DashScope SDK调用失败", e);
            return Flux.error(e);
        }
    }

    @Override
    public String getProvider() {
        return PROVIDER_NAME;
    }

}