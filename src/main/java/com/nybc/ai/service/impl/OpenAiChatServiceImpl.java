package com.nybc.ai.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.domain.entity.AiModelConfig;
import com.nybc.ai.domain.mapper.AiModelConfigMapper;
import com.nybc.ai.service.ChatService;
import com.nybc.ai.service.dto.ChatMessage;
import com.nybc.ai.service.dto.ChatRequest;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * 类描述：使用WebClient与OpenAI API实现的聊天服务
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class OpenAiChatServiceImpl implements ChatService {

    @Resource
    private AiModelConfigMapper aiModelConfigMapper;
    @Resource
    private ObjectMapper objectMapper;
    private WebClient webClient;
    private AiModelConfig config;
    public static final String PROVIDER_NAME = "openai";

    /**
     * 在服务初始化时加载配置并构建WebClient
     */
    @PostConstruct
    public void init() {
        this.config = aiModelConfigMapper.findByProviderAndIsEnabledTrue(PROVIDER_NAME);
        if (this.config == null) {
            log.warn("未在数据库中找到启用的'{}'提供商配置，该服务将不可用。", PROVIDER_NAME);
        } else {
            this.webClient = WebClient.builder()
                    .baseUrl(config.getBaseUrl())
                    .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + config.getApiKey())
                    .build();
            log.info("成功加载'{}'提供商配置。", PROVIDER_NAME);
        }
    }

    @Override
    public Flux<String> streamChat(ChatRequest request) {
        if (config == null || webClient == null) {
            return Flux.error(new IllegalStateException("OpenAI服务未正确配置或未启用。"));
        }

        String model = request.getModel() != null ? request.getModel() : config.getChatModel();
        OpenAiApiRequest openAiRequest = new OpenAiApiRequest(model, request.getMessages(), true);

        return webClient.post()
                .uri("/v1/chat/completions")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(openAiRequest)
                .retrieve()
                .bodyToFlux(String.class)
                .map(this::parseOpenAiResponse)
                .filter(StringUtils::hasText);
    }

    /**
     * 解析OpenAI SSE流返回的单行字符串
     * @param sseLine SSE事件行, e.g., "data: {...}"
     * @return 提取出的消息内容，如果解析失败或内容为空则返回null
     */
    private String parseOpenAiResponse(String sseLine) {
        if (sseLine.startsWith("data:")) {
            String jsonData = sseLine.substring(5).trim();
            if ("[DONE]".equalsIgnoreCase(jsonData)) {
                return null; // 流结束标志
            }
            try {
                JsonNode root = objectMapper.readTree(jsonData);
                JsonNode choices = root.path("choices");
                if (choices.isArray() && !choices.isEmpty()) {
                    JsonNode delta = choices.get(0).path("delta");
                    if (delta.has("content")) {
                        JsonNode contentNode = delta.get("content");
                        // content字段可能为null
                        return contentNode.isNull() ? null : contentNode.asText();
                    }
                }
            } catch (JsonProcessingException e) {
                log.error("解析OpenAI响应失败: {}", jsonData, e);
            }
        }
        return null;
    }


    @Override
    public String getProvider() {
        return PROVIDER_NAME;
    }

    /**
     * 内部类，用于构建OpenAI API请求体
     */
    @Data
    private static class OpenAiApiRequest {
        private final String model;
        private final List<ChatMessage> messages;
        private final boolean stream;
    }
} 