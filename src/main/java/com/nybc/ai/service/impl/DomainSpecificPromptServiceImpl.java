package com.nybc.ai.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.feedback.service.PromptPerformanceAnalyticsService;
import com.nybc.ai.infra.ai.AiModelService;
import com.nybc.ai.infra.ai.AiModelServiceFactory;
import com.nybc.ai.prompt.mapper.PromptExecutionLogMapper;
import com.nybc.ai.service.DomainSpecificPromptService;
import com.nybc.ai.service.dto.DomainPromptOptimizationRequest;
import com.nybc.ai.service.dto.DomainPromptOptimizationResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 类描述：领域特定Prompt服务实现类
 * 专注于计算机、软件学院课程教学的Prompt优化
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class DomainSpecificPromptServiceImpl implements DomainSpecificPromptService {

    @Resource
    private AiModelServiceFactory aiModelServiceFactory;

    @Resource
    private PromptPerformanceAnalyticsService promptPerformanceAnalyticsService;

    @Resource
    private PromptExecutionLogMapper promptExecutionLogMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${app.ai.default-provider:deepseek}")
    private String defaultAiProvider;

    // 计算机科学领域的核心概念和术语
    private static final Map<String, String[]> DOMAIN_TERMINOLOGY = Map.of(
            "COMPUTER_SCIENCE", new String[]{"算法", "数据结构", "计算复杂度", "设计模式", "软件架构"},
            "SOFTWARE_ENGINEERING", new String[]{"软件工程", "需求分析", "系统设计", "测试驱动开发", "敏捷开发"},
            "DATA_STRUCTURE", new String[]{"数组", "链表", "栈", "队列", "树", "图", "哈希表"},
            "ALGORITHM", new String[]{"排序算法", "搜索算法", "动态规划", "贪心算法", "分治算法"},
            "DATABASE", new String[]{"关系型数据库", "SQL", "事务", "索引", "范式", "ACID"}
    );

    @Override
    public DomainPromptOptimizationResponse optimizeForComputerScience(DomainPromptOptimizationRequest request) {
        try {
            log.info("开始计算机科学领域Prompt优化: 场景[{}], 目标[{}]", 
                    request.getScenario(), request.getOptimizationGoal());

            // 1. 分析原始Prompt
            String analysisResult = analyzeOriginalPrompt(request);

            // 2. 应用领域特定优化
            String optimizedPrompt = applyDomainOptimization(request, analysisResult);

            // 3. 构建响应
            DomainPromptOptimizationResponse response = buildOptimizationResponse(
                    request, optimizedPrompt, analysisResult);

            log.info("计算机科学领域Prompt优化完成: 质量评分[{}]", response.getQualityScore());
            return response;

        } catch (Exception e) {
            log.error("计算机科学领域Prompt优化失败: {}", e.getMessage(), e);
            throw new RuntimeException("领域Prompt优化失败", e);
        }
    }

    @Override
    public String optimizeForCodeEvaluation(String originalPrompt, Map<String, Object> codeContext, 
                                          Map<String, Object> evaluationCriteria) {
        try {
            log.info("开始代码评估场景Prompt优化");

            String optimizationPrompt = buildCodeEvaluationOptimizationPrompt(
                    originalPrompt, codeContext, evaluationCriteria);

            AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
            String optimizedPrompt = aiService.call(optimizationPrompt, 
                    Collections.singletonMap("temperature", 0.3));

            log.info("代码评估场景Prompt优化完成");
            return optimizedPrompt.trim();

        } catch (Exception e) {
            log.error("代码评估场景Prompt优化失败: {}", e.getMessage(), e);
            return originalPrompt;
        }
    }

    @Override
    public String optimizeForAssignmentGrading(String originalPrompt, String assignmentType, 
                                             Map<String, Object> gradingRubric) {
        try {
            log.info("开始作业批改场景Prompt优化: 作业类型[{}]", assignmentType);

            String optimizationPrompt = buildAssignmentGradingOptimizationPrompt(
                    originalPrompt, assignmentType, gradingRubric);

            AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
            String optimizedPrompt = aiService.call(optimizationPrompt, 
                    Collections.singletonMap("temperature", 0.2));

            log.info("作业批改场景Prompt优化完成");
            return optimizedPrompt.trim();

        } catch (Exception e) {
            log.error("作业批改场景Prompt优化失败: {}", e.getMessage(), e);
            return originalPrompt;
        }
    }

    @Override
    public String optimizeForStudentTutoring(String originalPrompt, String studentLevel, 
                                           Map<String, Object> courseContext) {
        try {
            log.info("开始学生答疑场景Prompt优化: 学生水平[{}]", studentLevel);

            String optimizationPrompt = buildStudentTutoringOptimizationPrompt(
                    originalPrompt, studentLevel, courseContext);

            AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
            String optimizedPrompt = aiService.call(optimizationPrompt, 
                    Collections.singletonMap("temperature", 0.4));

            log.info("学生答疑场景Prompt优化完成");
            return optimizedPrompt.trim();

        } catch (Exception e) {
            log.error("学生答疑场景Prompt优化失败: {}", e.getMessage(), e);
            return originalPrompt;
        }
    }

    @Override
    public String optimizeForDocumentAnalysis(String originalPrompt, String documentType, 
                                            String analysisGoal) {
        try {
            log.info("开始文档分析场景Prompt优化: 文档类型[{}], 分析目标[{}]", documentType, analysisGoal);

            String optimizationPrompt = buildDocumentAnalysisOptimizationPrompt(
                    originalPrompt, documentType, analysisGoal);

            AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
            String optimizedPrompt = aiService.call(optimizationPrompt, 
                    Collections.singletonMap("temperature", 0.3));

            log.info("文档分析场景Prompt优化完成");
            return optimizedPrompt.trim();

        } catch (Exception e) {
            log.error("文档分析场景Prompt优化失败: {}", e.getMessage(), e);
            return originalPrompt;
        }
    }

    @Override
    public String autoApplyBestPattern(String originalPrompt, Map<String, Object> context) {
        try {
            log.info("开始自动应用最佳Prompt模式");

            // 1. 检测Prompt类型和场景
            String detectedScenario = detectPromptScenario(originalPrompt, context);

            // 2. 根据场景应用最佳模式
            String optimizedPrompt = applyBestPatternForScenario(originalPrompt, detectedScenario, context);

            log.info("自动应用最佳Prompt模式完成: 检测场景[{}]", detectedScenario);
            return optimizedPrompt;

        } catch (Exception e) {
            log.error("自动应用最佳Prompt模式失败: {}", e.getMessage(), e);
            return originalPrompt;
        }
    }

    @Override
    public DomainPromptOptimizationResponse autoTunePrompt(String promptKey, Long tenantId, 
                                                          String optimizationGoal) {
        try {
            log.info("开始自动调优Prompt: 模板[{}], 目标[{}]", promptKey, optimizationGoal);

            // 1. 获取历史性能数据
            var performanceData = promptPerformanceAnalyticsService.autoOptimizePrompt(
                    null, tenantId, 0L); // 需要根据promptKey查找templateId

            // 2. 分析性能瓶颈
            Map<String, Object> bottlenecks = analyzePerformanceBottlenecks(performanceData);

            // 3. 生成优化建议
            String optimizationSuggestions = generateOptimizationSuggestions(bottlenecks, optimizationGoal);

            // 4. 应用自动调优
            String tunedPrompt = applyAutoTuning(promptKey, optimizationSuggestions);

            // 5. 构建响应
            DomainPromptOptimizationResponse response = new DomainPromptOptimizationResponse()
                    .setOptimizedPrompt(tunedPrompt)
                    .setOptimizationStrategy("基于历史数据的自动调优")
                    .setQualityScore(8.5)
                    .setOptimizationTime(LocalDateTime.now());

            log.info("Prompt自动调优完成: 模板[{}]", promptKey);
            return response;

        } catch (Exception e) {
            log.error("Prompt自动调优失败: {}", e.getMessage(), e);
            throw new RuntimeException("Prompt自动调优失败", e);
        }
    }

    @Override
    public String generateDomainTemplate(String scenario, Map<String, Object> requirements) {
        try {
            log.info("开始生成领域特定Prompt模板: 场景[{}]", scenario);

            String templateGenerationPrompt = buildTemplateGenerationPrompt(scenario, requirements);

            AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
            String generatedTemplate = aiService.call(templateGenerationPrompt, 
                    Collections.singletonMap("temperature", 0.5));

            log.info("领域特定Prompt模板生成完成");
            return generatedTemplate.trim();

        } catch (Exception e) {
            log.error("生成领域特定Prompt模板失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成领域特定Prompt模板失败", e);
        }
    }

    /**
     * 分析原始Prompt
     */
    private String analyzeOriginalPrompt(DomainPromptOptimizationRequest request) {
        String analysisPrompt = String.format("""
                你是一位专业的计算机、软件学院AI教学助手和Prompt工程专家。
                请分析以下Prompt在计算机科学教育领域的优化潜力。
                
                ## 原始Prompt：
                %s
                
                ## 目标领域：%s
                ## 应用场景：%s
                ## 学生水平：%s
                ## 优化目标：%s
                
                ## 分析要求：
                请从以下维度分析并以JSON格式返回：
                1. 领域专业性评估
                2. 教学适用性分析
                3. 技术准确性检查
                4. 结构清晰度评价
                5. 改进建议要点
                
                请确保分析结果专业、准确，符合计算机科学教育的特点。
                """,
                request.getOriginalPrompt(),
                request.getTargetDomain(),
                request.getScenario(),
                request.getStudentLevel(),
                request.getOptimizationGoal());

        AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
        return aiService.call(analysisPrompt, Collections.singletonMap("temperature", 0.3));
    }

    /**
     * 应用领域特定优化
     */
    private String applyDomainOptimization(DomainPromptOptimizationRequest request, String analysisResult) {
        String[] domainTerms = DOMAIN_TERMINOLOGY.getOrDefault(request.getTargetDomain(), new String[]{});
        
        String optimizationPrompt = String.format("""
                你是一位专业的计算机、软件学院AI教学助手。请基于分析结果优化以下Prompt。
                
                ## 原始Prompt：
                %s
                
                ## 分析结果：
                %s
                
                ## 优化要求：
                1. **领域专业性**：融入计算机科学专业术语：%s
                2. **教学适应性**：适配%s水平的学生
                3. **场景针对性**：优化为%s场景
                4. **输出格式**：确保输出为%s格式
                5. **中文表达**：使用专业的中文表达
                6. **实践导向**：强调实际应用和动手实践
                
                ## 技术栈信息：
                %s
                
                ## 课程信息：
                %s
                
                请直接返回优化后的Prompt，确保专业、准确、实用。
                """,
                request.getOriginalPrompt(),
                analysisResult,
                String.join("、", domainTerms),
                request.getStudentLevel(),
                request.getScenario(),
                request.getOutputFormat(),
                request.getTechStackInfo() != null ? request.getTechStackInfo().toString() : "未指定",
                request.getCourseInfo() != null ? request.getCourseInfo().toString() : "未指定");

        AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
        return aiService.call(optimizationPrompt, Collections.singletonMap("temperature", 0.4));
    }

    /**
     * 构建优化响应
     */
    private DomainPromptOptimizationResponse buildOptimizationResponse(
            DomainPromptOptimizationRequest request, String optimizedPrompt, String analysisResult) {
        
        DomainPromptOptimizationResponse response = new DomainPromptOptimizationResponse()
                .setOptimizedPrompt(optimizedPrompt)
                .setOriginalPrompt(request.getOriginalPrompt())
                .setOptimizationStrategy("计算机科学领域特定优化")
                .setQualityScore(calculateQualityScore(request, optimizedPrompt))
                .setOptimizationTime(LocalDateTime.now());

        // 设置应用的优化技术
        List<DomainPromptOptimizationResponse.OptimizationTechnique> techniques = Arrays.asList(
                new DomainPromptOptimizationResponse.OptimizationTechnique()
                        .setTechniqueName("领域术语增强")
                        .setDescription("添加计算机科学专业术语")
                        .setRationale("提高专业性和准确性")
                        .setExpectedEffect("增强领域相关性"),
                new DomainPromptOptimizationResponse.OptimizationTechnique()
                        .setTechniqueName("教学场景适配")
                        .setDescription("根据教学场景调整Prompt结构")
                        .setRationale("提高教学效果")
                        .setExpectedEffect("更好的学习体验")
        );
        response.setAppliedTechniques(techniques);

        // 设置领域增强
        DomainPromptOptimizationResponse.DomainEnhancements enhancements = 
                new DomainPromptOptimizationResponse.DomainEnhancements()
                        .setAddedTerminology(Arrays.asList(DOMAIN_TERMINOLOGY.getOrDefault(request.getTargetDomain(), new String[]{})))
                        .setEnhancedContext(Arrays.asList("计算机科学教育背景", "实践导向学习"))
                        .setProfessionalCriteria(Arrays.asList("技术准确性", "教学适用性", "实践可操作性"));
        response.setDomainEnhancements(enhancements);

        return response;
    }

    /**
     * 计算质量评分
     */
    private Double calculateQualityScore(DomainPromptOptimizationRequest request, String optimizedPrompt) {
        double score = 7.0; // 基础分

        // 根据优化程度调整分数
        if (optimizedPrompt.length() > request.getOriginalPrompt().length() * 1.2) {
            score += 1.0; // 内容丰富度
        }

        // 检查是否包含领域术语
        String[] domainTerms = DOMAIN_TERMINOLOGY.getOrDefault(request.getTargetDomain(), new String[]{});
        long termCount = Arrays.stream(domainTerms)
                .filter(optimizedPrompt::contains)
                .count();
        score += (termCount / (double) domainTerms.length) * 2.0;

        return Math.min(score, 10.0);
    }

    /**
     * 构建代码评估优化Prompt
     */
    private String buildCodeEvaluationOptimizationPrompt(String originalPrompt, 
                                                        Map<String, Object> codeContext, 
                                                        Map<String, Object> evaluationCriteria) {
        return String.format("""
                请优化以下代码评估Prompt，使其更适合计算机、软件学院的代码评估场景。
                
                ## 原始Prompt：
                %s
                
                ## 代码上下文：
                %s
                
                ## 评估标准：
                %s
                
                ## 优化要求：
                1. 明确代码评估的技术标准
                2. 包含代码质量、性能、可维护性等维度
                3. 提供具体的改进建议格式
                4. 确保评估结果的教学价值
                5. 使用中文进行专业表达
                
                请返回优化后的Prompt。
                """,
                originalPrompt,
                codeContext.toString(),
                evaluationCriteria.toString());
    }

    /**
     * 构建作业批改优化Prompt
     */
    private String buildAssignmentGradingOptimizationPrompt(String originalPrompt, 
                                                           String assignmentType, 
                                                           Map<String, Object> gradingRubric) {
        return String.format("""
                请优化以下作业批改Prompt，使其更适合%s类型作业的批改。
                
                ## 原始Prompt：
                %s
                
                ## 评分标准：
                %s
                
                ## 优化要求：
                1. 根据作业类型调整评估重点
                2. 明确评分标准和权重
                3. 提供建设性的反馈建议
                4. 确保评估的公平性和一致性
                5. 突出教学目标的达成度
                
                请返回优化后的Prompt。
                """,
                assignmentType,
                originalPrompt,
                gradingRubric.toString());
    }

    /**
     * 构建学生答疑优化Prompt
     */
    private String buildStudentTutoringOptimizationPrompt(String originalPrompt, 
                                                         String studentLevel, 
                                                         Map<String, Object> courseContext) {
        return String.format("""
                请优化以下学生答疑Prompt，使其更适合%s水平学生的学习需求。
                
                ## 原始Prompt：
                %s
                
                ## 课程上下文：
                %s
                
                ## 优化要求：
                1. 根据学生水平调整解释深度
                2. 使用循序渐进的教学方法
                3. 提供实际的代码示例和练习
                4. 鼓励学生主动思考和探索
                5. 建立知识点之间的联系
                
                请返回优化后的Prompt。
                """,
                studentLevel,
                originalPrompt,
                courseContext.toString());
    }

    /**
     * 构建文档分析优化Prompt
     */
    private String buildDocumentAnalysisOptimizationPrompt(String originalPrompt, 
                                                          String documentType, 
                                                          String analysisGoal) {
        return String.format("""
                请优化以下文档分析Prompt，使其更适合%s类型文档的分析。
                
                ## 原始Prompt：
                %s
                
                ## 分析目标：
                %s
                
                ## 优化要求：
                1. 根据文档类型调整分析重点
                2. 明确分析的技术维度
                3. 提供结构化的分析结果
                4. 突出关键技术要点
                5. 确保分析的专业性和准确性
                
                请返回优化后的Prompt。
                """,
                documentType,
                originalPrompt,
                analysisGoal);
    }

    /**
     * 检测Prompt场景
     */
    private String detectPromptScenario(String originalPrompt, Map<String, Object> context) {
        // 简单的场景检测逻辑
        String prompt = originalPrompt.toLowerCase();
        
        if (prompt.contains("代码") || prompt.contains("编程") || prompt.contains("算法")) {
            return "CODE_EVALUATION";
        } else if (prompt.contains("作业") || prompt.contains("批改") || prompt.contains("评分")) {
            return "ASSIGNMENT_GRADING";
        } else if (prompt.contains("学生") || prompt.contains("答疑") || prompt.contains("解释")) {
            return "STUDENT_TUTORING";
        } else if (prompt.contains("文档") || prompt.contains("分析") || prompt.contains("设计")) {
            return "DOCUMENT_ANALYSIS";
        }
        
        return "GENERAL";
    }

    /**
     * 为场景应用最佳模式
     */
    private String applyBestPatternForScenario(String originalPrompt, String scenario, Map<String, Object> context) {
        switch (scenario) {
            case "CODE_EVALUATION":
                return optimizeForCodeEvaluation(originalPrompt, context, Collections.emptyMap());
            case "ASSIGNMENT_GRADING":
                return optimizeForAssignmentGrading(originalPrompt, "PROGRAMMING", Collections.emptyMap());
            case "STUDENT_TUTORING":
                return optimizeForStudentTutoring(originalPrompt, "INTERMEDIATE", context);
            case "DOCUMENT_ANALYSIS":
                return optimizeForDocumentAnalysis(originalPrompt, "TECHNICAL_SPEC", "质量评估");
            default:
                return originalPrompt;
        }
    }

    /**
     * 分析性能瓶颈
     */
    private Map<String, Object> analyzePerformanceBottlenecks(Map<String, Object> performanceData) {
        Map<String, Object> bottlenecks = new HashMap<>();
        
        // 简单的瓶颈分析逻辑
        bottlenecks.put("responseTime", "正常");
        bottlenecks.put("accuracy", "需要改进");
        bottlenecks.put("userSatisfaction", "良好");
        
        return bottlenecks;
    }

    /**
     * 生成优化建议
     */
    private String generateOptimizationSuggestions(Map<String, Object> bottlenecks, String optimizationGoal) {
        return "基于性能分析，建议优化Prompt的结构和专业术语使用";
    }

    /**
     * 应用自动调优
     */
    private String applyAutoTuning(String promptKey, String optimizationSuggestions) {
        return "经过自动调优的Prompt内容";
    }

    /**
     * 构建模板生成Prompt
     */
    private String buildTemplateGenerationPrompt(String scenario, Map<String, Object> requirements) {
        return String.format("""
                请为计算机、软件学院的%s场景生成一个专业的Prompt模板。
                
                ## 需求：
                %s
                
                ## 模板要求：
                1. 包含变量占位符，如 ${variable_name}
                2. 结构清晰，逻辑合理
                3. 专业术语准确
                4. 适合教学场景
                5. 支持中文输出
                
                请返回完整的Prompt模板。
                """,
                scenario,
                requirements.toString());
    }
}
