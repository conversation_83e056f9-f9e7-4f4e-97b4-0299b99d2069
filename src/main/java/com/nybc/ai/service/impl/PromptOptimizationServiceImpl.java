package com.nybc.ai.service.impl;

import com.alibaba.fastjson2.JSON;
import com.nybc.ai.service.UnifiedAiService;
import com.nybc.ai.domain.PromptTemplate;
import com.nybc.ai.domain.PromptVersion;
import com.nybc.ai.domain.PromptExecutionLog;
import com.nybc.ai.prompt.dto.PromptOptimizeWithFeedbackRequest;
import com.nybc.ai.prompt.dto.PromptVersionResponse;
import com.nybc.ai.prompt.mapper.PromptExecutionLogMapper;
import com.nybc.ai.prompt.mapper.PromptTemplateMapper;
import com.nybc.ai.prompt.mapper.PromptVersionMapper;
import com.nybc.ai.service.PromptOptimizationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.Collections;
import java.util.Map;

/**
 * 类描述：Prompt优化服务的实现
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class PromptOptimizationServiceImpl implements PromptOptimizationService {

    private static final String OPTIMIZER_PROMPT_KEY = "system.internal.prompt_optimizer";
    private static final String USER_INPUT_PLACEHOLDER = "{{user_input_section}}";

    @Resource
    private UnifiedAiService unifiedAiService;
    @Resource
    private PromptTemplateMapper templateMapper;
    @Resource
    private PromptVersionMapper versionMapper;
    @Resource
    private PromptExecutionLogMapper logMapper;


    @Override
    @Transactional
    public PromptVersionResponse optimizeAndCreateVersion(Long templateId, String rawIdeaText, Long operatorId, String providerName) {
        String userInputSection = "## 用户的初步想法\n`" + rawIdeaText + "`";
        return internalOptimize(templateId, userInputSection, rawIdeaText, providerName, operatorId);
    }

    @Override
    @Transactional
    public PromptVersionResponse optimizeWithFeedback(PromptOptimizeWithFeedbackRequest request) {
        // 1. 获取执行日志和对应的旧版本
        PromptExecutionLog execLog = logMapper.findById(request.getExecutionLogId());
        Assert.notNull(execLog, "找不到指定的执行日志: " + request.getExecutionLogId());
        Assert.hasText(execLog.getFeedbackStatus(), "该日志还没有人工反馈，无法进行优化。");

        PromptVersion previousVersion = versionMapper.findById(execLog.getVersionId());
        Assert.notNull(previousVersion, "找不到日志对应的Prompt版本: " + execLog.getVersionId());

        // 2. 构建带反馈的用户输入部分
        String userInputSection = String.format(
                "## 待改进的Prompt及人工反馈\n" +
                        "- **待改进的Prompt内容**:\n```\n%s\n```\n" +
                        "- **人工反馈**:\n" +
                        "  - **匹配度评分**: %d/5\n" +
                        "  - **评价**: %s\n" +
                        "  - **具体说明**: %s",
                previousVersion.getTemplateContent(),
                execLog.getFeedbackScore(),
                execLog.getFeedbackStatus(),
                execLog.getFeedbackNotes()
        );

        // 3. 调用内部通用优化方法
        return internalOptimize(previousVersion.getTemplateId(), userInputSection, previousVersion.getRawIdeaText(), request.getProviderName(), request.getOperatorId());
    }

    /**
     * 内部通用的优化逻辑
     */
    private PromptVersionResponse internalOptimize(Long templateId, String userInputSection, String rawIdeaText, String providerName, Long operatorId) {
        // 1. 获取元Prompt
        PromptTemplate optimizerTemplate = templateMapper.findByKeyAndTenant(OPTIMIZER_PROMPT_KEY, 0L);
        Assert.notNull(optimizerTemplate, "未找到系统内置的Prompt优化器模板。");
        Assert.hasText(optimizerTemplate.getActiveTemplateContent(), "Prompt优化器模板没有活动的版本内容。");

        // 2. 填充元Prompt
        String finalPrompt = optimizerTemplate.getActiveTemplateContent().replace(USER_INPUT_PLACEHOLDER, userInputSection);

        // 3. 调用AI
        log.info("正在使用 [{}] 模型优化Prompt，模板ID: {}", providerName, templateId);
        String aiResponseJson = unifiedAiService.chat(finalPrompt, Collections.singletonMap("temperature", 0.5));
        log.debug("AI模型返回的原始JSON: {}", aiResponseJson);

        // 4. 解析结果
        Map<String, String> optimizationResult;
        try {
            optimizationResult = JSON.parseObject(aiResponseJson, Map.class);
        } catch (Exception e) {
            log.error("解析AI返回的JSON失败", e);
            throw new RuntimeException("AI模型返回的格式无效。", e);
        }

        // 5. 获取最新版本号
        PromptTemplate targetTemplate = templateMapper.findById(templateId);
        Assert.notNull(targetTemplate, "找不到ID为 " + templateId + " 的Prompt模板。");
        int nextVersionNumber = versionMapper.findByTemplateId(templateId).stream()
                .mapToInt(PromptVersion::getVersionNumber).max().orElse(0) + 1;

        // 6. 创建并存储新版本
        PromptVersion newVersion = new PromptVersion();
        newVersion.setTemplateId(templateId);
        newVersion.setTenantId(targetTemplate.getTenantId());
        newVersion.setVersionNumber(nextVersionNumber);
        newVersion.setRawIdeaText(rawIdeaText); // 保留最原始的想法
        newVersion.setTemplateContent(optimizationResult.get("template_content"));
        newVersion.setChangelog(optimizationResult.get("changelog"));
        newVersion.setOptimizationCot(optimizationResult.get("optimization_cot"));
        newVersion.setOptimizationModel(providerName);
        newVersion.setCreateUser(operatorId);
        newVersion.setUpdateUser(operatorId);
        versionMapper.insert(newVersion);

        log.info("成功创建新的Prompt版本 [templateId={}, newVersionId={}, versionNumber={}]",
                templateId, newVersion.getId(), newVersion.getVersionNumber());

        return PromptVersionResponse.fromDomain(newVersion);
    }
} 