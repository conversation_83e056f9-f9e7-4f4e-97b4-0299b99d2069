package com.nybc.ai.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.service.UnifiedAiService;
import com.nybc.ai.service.MultimodalDocumentService;
import com.nybc.ai.service.dto.DocumentStructure;
import com.nybc.ai.service.dto.MultimodalDocumentAnalysis;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 类描述：多模态文档处理服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class MultimodalDocumentServiceImpl implements MultimodalDocumentService {

    @Resource
    private UnifiedAiService unifiedAiService;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${app.ai.default-provider:deepseek}")
    private String defaultAiProvider;

    // 代码语言检测模式
    private static final Map<String, Pattern> CODE_PATTERNS = Map.of(
            "java", Pattern.compile("(public|private|protected)\\s+(class|interface|enum)|import\\s+[\\w.]+;"),
            "python", Pattern.compile("(def\\s+\\w+|import\\s+\\w+|from\\s+\\w+\\s+import)"),
            "javascript", Pattern.compile("(function\\s+\\w+|var\\s+\\w+|let\\s+\\w+|const\\s+\\w+)"),
            "sql", Pattern.compile("(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER)\\s+", Pattern.CASE_INSENSITIVE),
            "xml", Pattern.compile("<[^>]+>.*</[^>]+>"),
            "json", Pattern.compile("\\{[^}]*\"[^\"]+\"\\s*:[^}]*\\}")
    );

    @Override
    public MultimodalDocumentAnalysis analyzeDocument(MultipartFile file, String analysisType) {
        try {
            log.info("开始多模态文档分析: 文件[{}], 分析类型[{}]", file.getOriginalFilename(), analysisType);

            MultimodalDocumentAnalysis analysis = new MultimodalDocumentAnalysis()
                    .setAnalysisType(analysisType)
                    .setAnalysisTime(LocalDateTime.now());

            // 1. 提取文档基本信息
            MultimodalDocumentAnalysis.DocumentInfo docInfo = extractDocumentInfo(file);
            analysis.setDocumentInfo(docInfo);

            // 2. 提取文档结构
            DocumentStructure structure = extractDocumentStructure(file);
            analysis.setDocumentStructure(structure);

            // 3. 文本内容分析
            MultimodalDocumentAnalysis.TextAnalysis textAnalysis = analyzeTextContent(file);
            analysis.setTextAnalysis(textAnalysis);

            // 4. 根据分析类型执行不同的处理
            switch (analysisType.toUpperCase()) {
                case "COMPREHENSIVE":
                    analysis.setImageAnalyses(analyzeImages(file));
                    analysis.setTableAnalyses(analyzeTables(file));
                    analysis.setCodeAnalyses(analyzeCodeSnippets(file));
                    break;
                case "VISUAL_FOCUS":
                    analysis.setImageAnalyses(analyzeImages(file));
                    break;
                case "STRUCTURE_ANALYSIS":
                    // 结构分析已在上面完成
                    break;
                case "TEXT_ONLY":
                default:
                    // 只进行文本分析
                    break;
            }

            // 5. 质量评估
            analysis.setQualityAssessment(assessDocumentQuality(analysis));

            log.info("多模态文档分析完成: 文件[{}]", file.getOriginalFilename());
            return analysis;

        } catch (Exception e) {
            log.error("多模态文档分析失败: {}", e.getMessage(), e);
            throw new RuntimeException("多模态文档分析失败", e);
        }
    }

    @Override
    public DocumentStructure extractDocumentStructure(MultipartFile file) {
        try {
            log.info("开始提取文档结构: 文件[{}]", file.getOriginalFilename());

            String fileExtension = StringUtils.getFilenameExtension(file.getOriginalFilename());
            if (fileExtension == null) {
                throw new IllegalArgumentException("无法确定文件类型");
            }

            DocumentStructure structure = new DocumentStructure();

            switch (fileExtension.toLowerCase()) {
                case "pdf":
                    structure = extractPdfStructure(file);
                    break;
                case "docx":
                    structure = extractWordStructure(file);
                    break;
                case "xlsx":
                    structure = extractExcelStructure(file);
                    break;
                default:
                    structure = extractGenericStructure(file);
                    break;
            }

            log.info("文档结构提取完成: 文件[{}]", file.getOriginalFilename());
            return structure;

        } catch (Exception e) {
            log.error("提取文档结构失败: {}", e.getMessage(), e);
            throw new RuntimeException("提取文档结构失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> processDocumentImages(MultipartFile file) {
        try {
            log.info("开始处理文档图片: 文件[{}]", file.getOriginalFilename());

            List<Map<String, Object>> imageResults = new ArrayList<>();
            String fileExtension = StringUtils.getFilenameExtension(file.getOriginalFilename());

            if ("pdf".equalsIgnoreCase(fileExtension)) {
                imageResults = processPdfImages(file);
            } else if ("docx".equalsIgnoreCase(fileExtension)) {
                imageResults = processWordImages(file);
            }

            // 使用AI分析图片内容
            for (Map<String, Object> imageResult : imageResults) {
                String aiAnalysis = analyzeImageWithAI((byte[]) imageResult.get("imageData"));
                imageResult.put("aiAnalysis", aiAnalysis);
            }

            log.info("文档图片处理完成: 文件[{}], 图片数量[{}]", file.getOriginalFilename(), imageResults.size());
            return imageResults;

        } catch (Exception e) {
            log.error("处理文档图片失败: {}", e.getMessage(), e);
            throw new RuntimeException("处理文档图片失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> extractAndAnalyzeTables(MultipartFile file) {
        try {
            log.info("开始提取和分析表格: 文件[{}]", file.getOriginalFilename());

            List<Map<String, Object>> tableResults = new ArrayList<>();
            String fileExtension = StringUtils.getFilenameExtension(file.getOriginalFilename());

            switch (fileExtension.toLowerCase()) {
                case "xlsx":
                    tableResults = extractExcelTables(file);
                    break;
                case "docx":
                    tableResults = extractWordTables(file);
                    break;
                case "pdf":
                    tableResults = extractPdfTables(file);
                    break;
            }

            // 使用AI分析表格内容
            for (Map<String, Object> tableResult : tableResults) {
                String aiAnalysis = analyzeTableWithAI(tableResult);
                tableResult.put("aiAnalysis", aiAnalysis);
            }

            log.info("表格提取和分析完成: 文件[{}], 表格数量[{}]", file.getOriginalFilename(), tableResults.size());
            return tableResults;

        } catch (Exception e) {
            log.error("提取和分析表格失败: {}", e.getMessage(), e);
            throw new RuntimeException("提取和分析表格失败", e);
        }
    }

    @Override
    public List<Map<String, Object>> identifyAndAnalyzeCode(MultipartFile file) {
        try {
            log.info("开始识别和分析代码片段: 文件[{}]", file.getOriginalFilename());

            // 1. 提取文档文本内容
            String content = extractTextContent(file);

            // 2. 识别代码片段
            List<Map<String, Object>> codeSnippets = identifyCodeSnippets(content);

            // 3. 使用AI分析代码
            for (Map<String, Object> snippet : codeSnippets) {
                String aiAnalysis = analyzeCodeWithAI((String) snippet.get("code"), (String) snippet.get("language"));
                snippet.put("aiAnalysis", aiAnalysis);
            }

            log.info("代码片段识别和分析完成: 文件[{}], 代码片段数量[{}]", 
                    file.getOriginalFilename(), codeSnippets.size());
            return codeSnippets;

        } catch (Exception e) {
            log.error("识别和分析代码片段失败: {}", e.getMessage(), e);
            throw new RuntimeException("识别和分析代码片段失败", e);
        }
    }

    @Override
    public Map<String, Object> classifyDocument(MultipartFile file) {
        try {
            log.info("开始智能文档分类: 文件[{}]", file.getOriginalFilename());

            // 1. 提取文档特征
            String content = extractTextContent(file);
            DocumentStructure structure = extractDocumentStructure(file);

            // 2. 构建分类Prompt
            String classificationPrompt = buildDocumentClassificationPrompt(content, structure, file.getOriginalFilename());

            // 3. 使用AI进行分类
            String classificationResult = callAiService(classificationPrompt, Collections.singletonMap("temperature", 0.3));

            // 4. 解析分类结果
            Map<String, Object> result = parseClassificationResult(classificationResult);

            log.info("智能文档分类完成: 文件[{}], 分类[{}]", 
                    file.getOriginalFilename(), result.get("primaryCategory"));
            return result;

        } catch (Exception e) {
            log.error("智能文档分类失败: {}", e.getMessage(), e);
            throw new RuntimeException("智能文档分类失败", e);
        }
    }

    @Override
    public Map<String, Object> evaluateDocumentQuality(MultipartFile file, Map<String, Object> evaluationCriteria) {
        try {
            log.info("开始文档质量评估: 文件[{}]", file.getOriginalFilename());

            // 1. 全面分析文档
            MultimodalDocumentAnalysis analysis = analyzeDocument(file, "COMPREHENSIVE");

            // 2. 构建质量评估Prompt
            String evaluationPrompt = buildQualityEvaluationPrompt(analysis, evaluationCriteria);

            // 3. 使用AI进行质量评估
            String evaluationResult = callAiService(evaluationPrompt, Collections.singletonMap("temperature", 0.2));

            // 4. 解析评估结果
            Map<String, Object> result = parseEvaluationResult(evaluationResult);

            log.info("文档质量评估完成: 文件[{}], 总分[{}]", 
                    file.getOriginalFilename(), result.get("overallScore"));
            return result;

        } catch (Exception e) {
            log.error("文档质量评估失败: {}", e.getMessage(), e);
            throw new RuntimeException("文档质量评估失败", e);
        }
    }

    @Override
    public String generateDocumentSummary(MultipartFile file, String summaryType) {
        try {
            log.info("开始生成文档摘要: 文件[{}], 摘要类型[{}]", file.getOriginalFilename(), summaryType);

            // 1. 提取文档内容
            String content = extractTextContent(file);

            // 2. 构建摘要生成Prompt
            String summaryPrompt = buildSummaryGenerationPrompt(content, summaryType);

            // 3. 使用AI生成摘要
            String summary = callAiService(summaryPrompt, Collections.singletonMap("temperature", 0.4));

            log.info("文档摘要生成完成: 文件[{}]", file.getOriginalFilename());
            return summary.trim();

        } catch (Exception e) {
            log.error("生成文档摘要失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成文档摘要失败", e);
        }
    }

    @Override
    public Map<String, Object> checkDocumentFormat(MultipartFile file, Map<String, Object> formatStandards) {
        try {
            log.info("开始检查文档格式: 文件[{}]", file.getOriginalFilename());

            // 1. 提取文档结构和格式信息
            DocumentStructure structure = extractDocumentStructure(file);

            // 2. 构建格式检查Prompt
            String formatCheckPrompt = buildFormatCheckPrompt(structure, formatStandards);

            // 3. 使用AI进行格式检查
            String checkResult = callAiService(formatCheckPrompt, Collections.singletonMap("temperature", 0.2));

            // 4. 解析检查结果
            Map<String, Object> result = parseFormatCheckResult(checkResult);

            log.info("文档格式检查完成: 文件[{}], 符合标准[{}]", 
                    file.getOriginalFilename(), result.get("compliant"));
            return result;

        } catch (Exception e) {
            log.error("检查文档格式失败: {}", e.getMessage(), e);
            throw new RuntimeException("检查文档格式失败", e);
        }
    }

    /**
     * 提取文档基本信息
     */
    private MultimodalDocumentAnalysis.DocumentInfo extractDocumentInfo(MultipartFile file) {
        return new MultimodalDocumentAnalysis.DocumentInfo()
                .setFileName(file.getOriginalFilename())
                .setFileType(StringUtils.getFilenameExtension(file.getOriginalFilename()))
                .setFileSize(file.getSize())
                .setEncoding("UTF-8")
                .setLanguage("zh-CN");
    }

    /**
     * 分析文本内容
     */
    private MultimodalDocumentAnalysis.TextAnalysis analyzeTextContent(MultipartFile file) {
        try {
            String content = extractTextContent(file);
            
            return new MultimodalDocumentAnalysis.TextAnalysis()
                    .setFullText(content)
                    .setWordCount(content.split("\\s+").length)
                    .setCharacterCount(content.length())
                    .setReadabilityScore(calculateReadabilityScore(content))
                    .setSentiment("neutral");
        } catch (Exception e) {
            log.warn("文本内容分析失败: {}", e.getMessage());
            return new MultimodalDocumentAnalysis.TextAnalysis();
        }
    }

    /**
     * 分析图片内容
     */
    private List<MultimodalDocumentAnalysis.ImageAnalysis> analyzeImages(MultipartFile file) {
        try {
            List<Map<String, Object>> imageResults = processDocumentImages(file);
            List<MultimodalDocumentAnalysis.ImageAnalysis> analyses = new ArrayList<>();

            for (Map<String, Object> imageResult : imageResults) {
                MultimodalDocumentAnalysis.ImageAnalysis analysis = new MultimodalDocumentAnalysis.ImageAnalysis()
                        .setImageName((String) imageResult.get("imageName"))
                        .setImageType((String) imageResult.get("imageType"))
                        .setDescription((String) imageResult.get("aiAnalysis"))
                        .setRelevanceScore(0.8);
                analyses.add(analysis);
            }

            return analyses;
        } catch (Exception e) {
            log.warn("图片分析失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 分析表格内容
     */
    private List<MultimodalDocumentAnalysis.TableAnalysis> analyzeTables(MultipartFile file) {
        try {
            List<Map<String, Object>> tableResults = extractAndAnalyzeTables(file);
            List<MultimodalDocumentAnalysis.TableAnalysis> analyses = new ArrayList<>();

            for (Map<String, Object> tableResult : tableResults) {
                MultimodalDocumentAnalysis.TableAnalysis analysis = new MultimodalDocumentAnalysis.TableAnalysis()
                        .setTableName((String) tableResult.get("tableName"))
                        .setRowCount((Integer) tableResult.get("rowCount"))
                        .setColumnCount((Integer) tableResult.get("columnCount"))
                        .setSummary((String) tableResult.get("aiAnalysis"));
                analyses.add(analysis);
            }

            return analyses;
        } catch (Exception e) {
            log.warn("表格分析失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 分析代码片段
     */
    private List<MultimodalDocumentAnalysis.CodeAnalysis> analyzeCodeSnippets(MultipartFile file) {
        try {
            List<Map<String, Object>> codeResults = identifyAndAnalyzeCode(file);
            List<MultimodalDocumentAnalysis.CodeAnalysis> analyses = new ArrayList<>();

            for (Map<String, Object> codeResult : codeResults) {
                MultimodalDocumentAnalysis.CodeAnalysis analysis = new MultimodalDocumentAnalysis.CodeAnalysis()
                        .setLanguage((String) codeResult.get("language"))
                        .setCodeSnippet((String) codeResult.get("code"))
                        .setLineCount(((String) codeResult.get("code")).split("\n").length)
                        .setPurpose((String) codeResult.get("aiAnalysis"));
                analyses.add(analysis);
            }

            return analyses;
        } catch (Exception e) {
            log.warn("代码分析失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 评估文档质量
     */
    private MultimodalDocumentAnalysis.QualityAssessment assessDocumentQuality(MultimodalDocumentAnalysis analysis) {
        // 简单的质量评估逻辑
        double score = 7.0; // 基础分

        // 根据内容丰富度调整分数
        if (analysis.getTextAnalysis() != null && analysis.getTextAnalysis().getWordCount() > 1000) {
            score += 1.0;
        }

        // 根据结构完整性调整分数
        if (analysis.getDocumentStructure() != null && 
            analysis.getDocumentStructure().getSections() != null && 
            analysis.getDocumentStructure().getSections().size() > 3) {
            score += 1.0;
        }

        return new MultimodalDocumentAnalysis.QualityAssessment()
                .setOverallScore(Math.min(score, 10.0))
                .setMeetsStandards(score >= 7.0);
    }

    // 以下是辅助方法的简化实现，实际项目中需要更详细的实现

    private String extractTextContent(MultipartFile file) {
        // 复用现有的文件解析逻辑
        try {
            return extractTextFromStream(file.getInputStream(), file.getOriginalFilename());
        } catch (Exception e) {
            log.error("提取文本内容失败: {}", e.getMessage());
            return "";
        }
    }

    private String extractTextFromStream(InputStream inputStream, String filename) throws Exception {
        // 这里可以复用现有的FileParserServiceImpl中的逻辑
        return ""; // 简化实现
    }

    private DocumentStructure extractPdfStructure(MultipartFile file) {
        // PDF结构提取的具体实现
        return new DocumentStructure();
    }

    private DocumentStructure extractWordStructure(MultipartFile file) {
        // Word文档结构提取的具体实现
        return new DocumentStructure();
    }

    private DocumentStructure extractExcelStructure(MultipartFile file) {
        // Excel文档结构提取的具体实现
        return new DocumentStructure();
    }

    private DocumentStructure extractGenericStructure(MultipartFile file) {
        // 通用文档结构提取的具体实现
        return new DocumentStructure();
    }

    private List<Map<String, Object>> processPdfImages(MultipartFile file) {
        // PDF图片提取的具体实现
        return new ArrayList<>();
    }

    private List<Map<String, Object>> processWordImages(MultipartFile file) {
        // Word文档图片提取的具体实现
        return new ArrayList<>();
    }

    private List<Map<String, Object>> extractExcelTables(MultipartFile file) {
        // Excel表格提取的具体实现
        return new ArrayList<>();
    }

    private List<Map<String, Object>> extractWordTables(MultipartFile file) {
        // Word表格提取的具体实现
        return new ArrayList<>();
    }

    private List<Map<String, Object>> extractPdfTables(MultipartFile file) {
        // PDF表格提取的具体实现
        return new ArrayList<>();
    }

    private List<Map<String, Object>> identifyCodeSnippets(String content) {
        List<Map<String, Object>> snippets = new ArrayList<>();
        
        // 简单的代码识别逻辑
        for (Map.Entry<String, Pattern> entry : CODE_PATTERNS.entrySet()) {
            Matcher matcher = entry.getValue().matcher(content);
            while (matcher.find()) {
                Map<String, Object> snippet = new HashMap<>();
                snippet.put("language", entry.getKey());
                snippet.put("code", matcher.group());
                snippet.put("startIndex", matcher.start());
                snippet.put("endIndex", matcher.end());
                snippets.add(snippet);
            }
        }
        
        return snippets;
    }

    private double calculateReadabilityScore(String content) {
        // 简单的可读性评分算法
        if (content == null || content.isEmpty()) return 0.0;
        
        String[] sentences = content.split("[.!?]+");
        String[] words = content.split("\\s+");
        
        if (sentences.length == 0 || words.length == 0) return 0.0;
        
        double avgWordsPerSentence = (double) words.length / sentences.length;
        return Math.max(0, Math.min(10, 10 - (avgWordsPerSentence - 15) * 0.5));
    }

    private String analyzeImageWithAI(byte[] imageData) {
        // 使用AI分析图片内容的具体实现
        return "图片内容分析结果";
    }

    private String analyzeTableWithAI(Map<String, Object> tableData) {
        // 使用AI分析表格内容的具体实现
        return "表格内容分析结果";
    }

    private String analyzeCodeWithAI(String code, String language) {
        // 使用AI分析代码的具体实现
        return "代码分析结果";
    }

    private String buildDocumentClassificationPrompt(String content, DocumentStructure structure, String filename) {
        return String.format("""
                请对以下文档进行智能分类，专注于计算机、软件学院的教学场景。
                
                ## 文档信息：
                文件名: %s
                
                ## 文档内容摘要：
                %s
                
                ## 请从以下维度进行分类：
                1. 文档类型：课程设计报告、实验报告、毕业论文、技术文档、代码文档等
                2. 技术领域：编程语言、数据库、算法、软件工程、系统设计等
                3. 学术水平：本科、专科、研究生
                4. 完成度：草稿、初稿、终稿
                
                请以JSON格式返回分类结果。
                """, filename, content.length() > 1000 ? content.substring(0, 1000) + "..." : content);
    }

    private String buildQualityEvaluationPrompt(MultimodalDocumentAnalysis analysis, Map<String, Object> criteria) {
        return String.format("""
                请对以下文档进行质量评估，重点关注计算机、软件学院的学术标准。
                
                ## 文档分析结果：
                %s
                
                ## 评估标准：
                %s
                
                ## 请从以下维度评估：
                1. 内容完整性 (1-10分)
                2. 技术准确性 (1-10分)
                3. 结构清晰度 (1-10分)
                4. 格式规范性 (1-10分)
                5. 创新性 (1-10分)
                
                请以JSON格式返回评估结果，包含各维度得分、总分、优缺点分析和改进建议。
                """, analysis.toString(), criteria.toString());
    }

    private String buildSummaryGenerationPrompt(String content, String summaryType) {
        return String.format("""
                请为以下文档生成%s摘要，重点突出计算机、软件学院相关的技术要点。
                
                ## 文档内容：
                %s
                
                ## 摘要要求：
                - 突出技术关键点
                - 保留重要的实现细节
                - 使用专业术语
                - 结构清晰，逻辑合理
                
                请生成专业的中文摘要。
                """, summaryType, content.length() > 2000 ? content.substring(0, 2000) + "..." : content);
    }

    private String buildFormatCheckPrompt(DocumentStructure structure, Map<String, Object> standards) {
        return String.format("""
                请检查以下文档格式是否符合指定标准。
                
                ## 文档结构：
                %s
                
                ## 格式标准：
                %s
                
                ## 检查项目：
                1. 标题层次结构
                2. 页面布局
                3. 图表标注
                4. 参考文献格式
                5. 代码格式规范
                
                请以JSON格式返回检查结果，包含是否符合标准、具体问题和修改建议。
                """, structure.toString(), standards.toString());
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> parseClassificationResult(String result) {
        try {
            return objectMapper.readValue(result, Map.class);
        } catch (Exception e) {
            log.warn("解析分类结果失败: {}", e.getMessage());
            return Map.of("primaryCategory", "未知", "confidence", 0.5);
        }
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> parseEvaluationResult(String result) {
        try {
            return objectMapper.readValue(result, Map.class);
        } catch (Exception e) {
            log.warn("解析评估结果失败: {}", e.getMessage());
            return Map.of("overallScore", 7.0, "meetsStandards", true);
        }
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> parseFormatCheckResult(String result) {
        try {
            return objectMapper.readValue(result, Map.class);
        } catch (Exception e) {
            log.warn("解析格式检查结果失败: {}", e.getMessage());
            return Map.of("compliant", true, "issues", new ArrayList<>());
        }
    }

    /**
     * 统一的AI服务调用方法
     *
     * @param prompt     提示词
     * @param parameters 参数
     * @return AI响应
     */
    private String callAiService(String prompt, Map<String, Object> parameters) {
        try {
            return unifiedAiService.chat(prompt, parameters);
        } catch (Exception e) {
            log.error("AI服务调用失败: {}", e.getMessage(), e);
            throw new RuntimeException("AI服务调用失败: " + e.getMessage(), e);
        }
    }
}
