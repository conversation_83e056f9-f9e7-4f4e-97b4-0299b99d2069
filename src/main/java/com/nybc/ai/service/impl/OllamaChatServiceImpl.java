package com.nybc.ai.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.domain.entity.AiModelConfig;
import com.nybc.ai.domain.mapper.AiModelConfigMapper;
import com.nybc.ai.service.ChatService;
import com.nybc.ai.service.dto.ChatMessage;
import com.nybc.ai.service.dto.ChatRequest;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * 类描述：使用WebClient与Ollama API实现的聊天服务
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class OllamaChatServiceImpl implements ChatService {

    @Resource
    private AiModelConfigMapper aiModelConfigMapper;
    @Resource
    private ObjectMapper objectMapper; // Spring Boot自动配置的Jackson ObjectMapper
    private WebClient webClient;
    private AiModelConfig config;
    public static final String PROVIDER_NAME = "ollama";

    /**
     * 在服务初始化时加载配置并构建WebClient
     */
    @PostConstruct
    public void init() {
        this.config = aiModelConfigMapper.findByProviderAndIsEnabledTrue(PROVIDER_NAME);
        if (this.config == null) {
            log.warn("未在数据库中找到启用的'{}'提供商配置，该服务将不可用。", PROVIDER_NAME);
        } else {
            this.webClient = WebClient.builder().baseUrl(config.getBaseUrl()).build();
            log.info("成功加载'{}'提供商配置，API基础URL: {}", PROVIDER_NAME, config.getBaseUrl());
        }
    }

    @Override
    public Flux<String> streamChat(ChatRequest request) {
        if (config == null || webClient == null) {
            return Flux.error(new IllegalStateException("Ollama服务未正确配置或未启用。"));
        }

        // 使用数据库中或请求中指定的模型
        String model = request.getModel() != null ? request.getModel() : config.getChatModel();
        OllamaApiRequest ollamaRequest = new OllamaApiRequest(model, request.getMessages(), true);

        return webClient.post()
                .uri("/api/chat")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(ollamaRequest)
                .retrieve()
                .bodyToFlux(String.class) // 响应体是字符串流，每个字符串是一个JSON对象
                .map(this::parseOllamaResponse)
                .filter(StringUtils::hasText);
    }

    /**
     * 解析Ollama流式API返回的单行JSON字符串
     * @param jsonResponse JSON字符串
     * @return 提取出的消息内容，如果解析失败或内容为空则返回null
     */
    private String parseOllamaResponse(String jsonResponse) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonResponse);
            // Ollama的流式响应中，消息内容位于 message.content
            JsonNode messageNode = rootNode.path("message");
            if (messageNode.isObject()) {
                JsonNode contentNode = messageNode.path("content");
                if (contentNode.isTextual()) {
                    return contentNode.asText();
                }
            }
        } catch (JsonProcessingException e) {
            log.error("解析Ollama响应失败: {}", jsonResponse, e);
        }
        return null;
    }

    @Override
    public String getProvider() {
        return PROVIDER_NAME;
    }

    /**
     * 内部类，用于构建Ollama API请求体
     */
    @Data
    private static class OllamaApiRequest {
        private final String model;
        private final List<ChatMessage> messages;
        private final boolean stream;
    }
} 