package com.nybc.ai.service.impl;

import com.nybc.ai.service.FileParserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFTextShape;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class FileParserServiceImpl implements FileParserService {

    private final ChatClient chatClient;
    // 为了安全起见，限制只能读取指定基础目录下的文件
    private final String ALLOWED_BASE_PATH = "/path/to/your/secure/base/directory";

    public FileParserServiceImpl(ChatClient.Builder chatClientBuilder) {
        this.chatClient = chatClientBuilder.build();
    }

    @Override
    public String parseFile(MultipartFile file, String prompt) {
        try {
            log.info("开始解析上传文件: {}", file.getOriginalFilename());
            String content = extractTextFromFile(file);
            return callAi(content, prompt);
        } catch (Exception e) {
            log.error("文件解析或AI调用失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件解析失败，请检查文件格式或联系管理员。", e);
        }
    }

    @Override
    public String parseDirectory(String directoryPath, String prompt) {
        File directory = new File(directoryPath);

        // 安全校验
        if (!directory.exists() || !directory.isDirectory() || !directory.getAbsolutePath().startsWith(ALLOWED_BASE_PATH)) {
            throw new IllegalArgumentException("提供的路径无效或不允许访问: " + directoryPath);
        }

        StringBuilder aggregatedContent = new StringBuilder();
        List<File> files = listFilesForFolder(directory);

        for (File file : files) {
            try (InputStream inputStream = new FileInputStream(file)) {
                String fileContent = extractTextFromStream(inputStream, file.getName());
                aggregatedContent.append("--- 文件: ").append(file.getName()).append(" ---\n");
                aggregatedContent.append(fileContent).append("\n\n");
            } catch (Exception e) {
                log.warn("读取文件 {} 失败，已跳过: {}", file.getAbsolutePath(), e.getMessage());
            }
        }

        if (aggregatedContent.length() == 0) {
            return "在指定目录中没有找到可解析的文件。";
        }

        return callAi(aggregatedContent.toString(), prompt);
    }

    private List<File> listFilesForFolder(final File folder) {
        List<File> files = new ArrayList<>();
        for (final File fileEntry : folder.listFiles()) {
            if (fileEntry.isDirectory()) {
                files.addAll(listFilesForFolder(fileEntry));
            } else {
                files.add(fileEntry);
            }
        }
        return files;
    }

    private String callAi(String content, String prompt) {
        String finalPrompt = "你是一个专业的文件分析助手。请根据下面提供的[文件内容]，回答用户提出的[问题]。\n" +
                "---[文件内容]---\n" +
                "{fileContent}\n" +
                "---[问题]---\n" +
                "{prompt}";

        return chatClient.prompt()
                .user(userSpec -> userSpec.text(finalPrompt)
                        .param("fileContent", content)
                        .param("prompt", prompt)
                )
                .call()
                .content();
    }

    private String extractTextFromFile(MultipartFile file) throws Exception {
        try (InputStream inputStream = file.getInputStream()) {
            return extractTextFromStream(inputStream, file.getOriginalFilename());
        }
    }

    private String extractTextFromStream(InputStream inputStream, String filename) throws Exception {
        String fileExtension = StringUtils.getFilenameExtension(filename);

        if (fileExtension == null) {
            throw new IllegalArgumentException("无法确定文件类型，因为文件没有扩展名。");
        }

        return switch (fileExtension.toLowerCase()) {
            case "docx" -> {
                XWPFDocument document = new XWPFDocument(inputStream);
                yield new XWPFWordExtractor(document).getText();
            }
            case "pptx" -> {
                XMLSlideShow ppt = new XMLSlideShow(inputStream);
                StringBuilder text = new StringBuilder();
                for (var slide : ppt.getSlides()) {
                    for (var shape : slide.getShapes()) {
                        if (shape instanceof XSLFTextShape textShape) {
                            text.append(textShape.getText()).append("\n");
                        }
                    }
                }
                yield text.toString();
            }
            case "xlsx" -> {
                XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
                StringBuilder text = new StringBuilder();
                for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                    XSSFSheet sheet = workbook.getSheetAt(i);
                    Iterator<Row> rowIterator = sheet.iterator();
                    while (rowIterator.hasNext()) {
                        Row row = rowIterator.next();
                        Iterator<Cell> cellIterator = row.cellIterator();
                        while (cellIterator.hasNext()) {
                            Cell cell = cellIterator.next();
                            text.append(cell.toString()).append("\t");
                        }
                        text.append("\n");
                    }
                }
                yield text.toString();
            }
            case "java", "xml", "py", "html", "js", "css", "md", "txt", "json", "yml" ->
                    new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            default -> throw new IllegalArgumentException("不支持的文件类型: " + fileExtension);
        };
    }
} 