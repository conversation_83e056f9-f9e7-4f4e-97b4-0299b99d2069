package com.nybc.ai.service.impl;

import com.nybc.ai.rules.context.EvaluationContext;
import com.nybc.ai.rules.dto.HomeworkEvaluationRequest;
import com.nybc.ai.rules.mapper.RuleChainMapper;
import com.nybc.ai.service.LiteFlowRuleEngineService;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class LiteFlowRuleEngineServiceImpl implements LiteFlowRuleEngineService {

    @Resource
    private FlowExecutor flowExecutor;

    @Resource
    private RuleChainMapper ruleChainMapper;


    @Override
    public EvaluationContext execute(HomeworkEvaluationRequest request) {
        String chainNameToExecute;
        // 1. 检查请求中是否直接指定了规则链
        if (request.getChainName() != null && !request.getChainName().isEmpty()) {
            chainNameToExecute = request.getChainName();
            log.info("接收到请求，直接指定规则链: [{}]", chainNameToExecute);
        } else {
            // 2. 如果未指定，则自动查找需要执行的规则链
            log.info("未指定规则链，开始根据 租户ID[{}] 和 作业ID[{}] 自动查找...", request.getTenantId(), request.getAssignmentId());
            chainNameToExecute = findChain(request.getTenantId(), request.getAssignmentId())
                    .orElseThrow(() -> new IllegalStateException("未能为租户ID " + request.getTenantId() + " 和作业ID " + request.getAssignmentId() + " 找到匹配的规则链"));
        }

        // 3. 创建用于本次执行的评估上下文
        EvaluationContext context = new EvaluationContext();
        BeanUtils.copyProperties(request, context);
        context.setChainName(chainNameToExecute); // 显式设置规则链名称

        log.info("最终确定执行规则链: [{}], 开始执行... 租户ID: {}", chainNameToExecute, request.getTenantId());
        // 4. 调用LiteFlow执行器
        LiteflowResponse response = flowExecutor.execute2Resp(chainNameToExecute, context);
        if (!response.isSuccess()) {
            log.error("规则链 [{}] 执行失败. TenantId: {}, Cause: {}", chainNameToExecute, request.getTenantId(), response.getCause());
            // 根据需要可以抛出自定义异常
            throw new RuntimeException("规则引擎执行出错", response.getCause());
        }

        log.info("规则链 [{}] 执行完毕. TenantId: {}", chainNameToExecute, request.getTenantId());
        // 5. 返回包含评估结果的上下文
        return response.getContextBean(EvaluationContext.class);
    }

    /**
     * 根据租户ID和作业ID动态查找对应的规则链
     * <p>
     * 查找逻辑：
     * 1. 精确匹配：租户ID + 作业ID
     * 2. 租户级默认：租户ID + (作业ID为null或'*')
     * 3. 平台级默认：(租户ID为null或'*') + (作业ID为null或'*')
     *
     * @param tenantId     租户ID
     * @param assignmentId 作业ID
     * @return 规则链名称
     */
    private Optional<String> findChain(Long tenantId, Long assignmentId) {
        // 1. 尝试根据租户ID和作业ID精确查找
        if (tenantId != null && assignmentId != null) {
            String chain = ruleChainMapper.findChain(tenantId, assignmentId);
            if (chain != null) {
                return Optional.of(chain);
            }
        }
        // 2. 尝试查找租户的通用规则
        if (tenantId != null) {
            String chain = ruleChainMapper.findChain(tenantId, null);
            if (chain != null) {
                return Optional.of(chain);
            }
        }
        // 3. 查找平台的默认规则
        String defaultChain = ruleChainMapper.findChain(null, null);
        return Optional.ofNullable(defaultChain);
    }

}