package com.nybc.ai.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.infra.ai.AiModelService;
import com.nybc.ai.infra.ai.AiModelServiceFactory;
import com.nybc.ai.prompt.domain.PromptExecutionLog;
import com.nybc.ai.prompt.domain.PromptTemplate;
import com.nybc.ai.prompt.dto.PromptExecuteRequest;
import com.nybc.ai.prompt.dto.PromptExecuteResponse;
import com.nybc.ai.prompt.dto.PromptFeedbackRequest;
import com.nybc.ai.prompt.mapper.PromptExecutionLogMapper;
import com.nybc.ai.prompt.mapper.PromptTemplateMapper;
import com.nybc.ai.service.PromptRuntimeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Collections;

/**
 * 类描述：Prompt运行时与反馈服务的实现
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class PromptRuntimeServiceImpl implements PromptRuntimeService {

    @Resource
    private PromptTemplateMapper templateMapper;
    @Resource
    private PromptExecutionLogMapper logMapper;
    @Resource
    private AiModelServiceFactory aiModelServiceFactory;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    @Transactional
    public PromptExecuteResponse execute(PromptExecuteRequest request) {
        long startTime = System.currentTimeMillis();

        // 1. 查找活动的Prompt模板
        PromptTemplate template = templateMapper.findByKeyAndTenant(request.getPromptKey(), request.getTenantId());
        Assert.notNull(template, "找不到指定的Prompt模板 [key=" + request.getPromptKey() + "]");
        Assert.notNull(template.getActiveVersionId(), "该Prompt模板没有设置活动版本，无法执行。");
        Assert.hasText(template.getActiveTemplateContent(), "该Prompt模板的活动版本内容为空。");

        // 2. 替换变量，生成最终的Prompt
        StringSubstitutor sub = new StringSubstitutor(request.getInputVariables());
        String finalPrompt = sub.replace(template.getActiveTemplateContent());

        // 3. 确定并获取AI模型服务
        String providerName = StringUtils.hasText(request.getProviderName())
                ? request.getProviderName()
                : template.getActiveRuntimeProvider();
        Assert.hasText(providerName, "必须为Prompt执行指定一个AI Provider。");
        AiModelService aiService = aiModelServiceFactory.getService(providerName);

        // 4. 调用AI模型
        String rawResponse = aiService.call(finalPrompt, Collections.emptyMap()); // 暂时不传入额外参数
        long executionTimeMs = System.currentTimeMillis() - startTime;

        // 5. 创建并保存执行日志
        PromptExecutionLog execLog = new PromptExecutionLog();
        execLog.setSessionId(request.getSessionId());
        execLog.setTemplateId(template.getId());
        execLog.setVersionId(template.getActiveVersionId());
        execLog.setTenantId(template.getTenantId());
        try {
            execLog.setInputVariables(objectMapper.writeValueAsString(request.getInputVariables()));
        } catch (JsonProcessingException e) {
            log.warn("序列化InputVariables失败", e);
        }
        execLog.setFinalPrompt(finalPrompt);
        execLog.setAiProvider(providerName);
        execLog.setRawAiResponse(rawResponse);
        // TODO: 这里可以增加对rawResponse的解析逻辑，提取核心内容放入parsedAiResponse
        execLog.setParsedAiResponse(rawResponse);
        execLog.setExecutionTimeMs(executionTimeMs);
        execLog.setCreateUser(request.getUserId());
        execLog.setUpdateUser(request.getUserId());
        logMapper.insert(execLog);

        log.info("成功执行Prompt [key={}, logId={}]，耗时: {}ms", request.getPromptKey(), execLog.getId(), executionTimeMs);

        // 6. 返回结果
        return new PromptExecuteResponse(execLog.getId(), execLog.getParsedAiResponse(), request.getSessionId());
    }

    @Override
    @Transactional
    public void submitFeedback(PromptFeedbackRequest request) {
        // 1. 查找执行日志
        PromptExecutionLog execLog = logMapper.findById(request.getExecutionLogId());
        Assert.notNull(execLog, "找不到ID为 " + request.getExecutionLogId() + " 的执行日志。");

        // 2. 更新反馈信息
        execLog.setFeedbackStatus(request.getFeedbackStatus());
        execLog.setFeedbackScore(request.getFeedbackScore());
        execLog.setFeedbackNotes(request.getFeedbackNotes());
        execLog.setUpdateUser(request.getUserId());

        // 3. 保存更新
        logMapper.update(execLog);
        log.info("成功为执行日志 [logId={}] 提交反馈", request.getExecutionLogId());
    }
} 