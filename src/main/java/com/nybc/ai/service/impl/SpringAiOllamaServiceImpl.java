package com.nybc.ai.service.impl;

import com.nybc.ai.infra.ai.AiModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * 类描述：基于Spring AI的Ollama服务实现
 * 使用Spring AI框架提供的原生Ollama支持
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
@ConditionalOnProperty(name = "spring.ai.ollama.enabled", havingValue = "true")
@ConditionalOnBean(OllamaChatModel.class)
public class SpringAiOllamaServiceImpl implements AiModelService {

    public static final String PROVIDER_NAME = "spring-ai-ollama";

    @Resource
    private ChatModel ollamaChatModel;

    @Override
    public String call(String prompt, Map<String, Object> parameters) {
        try {
            log.debug("使用Spring AI Ollama调用AI模型，prompt长度: {}", prompt.length());

            // 如果有参数，使用PromptTemplate进行参数替换
            Prompt finalPrompt;
            if (parameters != null && !parameters.isEmpty()) {
                PromptTemplate promptTemplate = new PromptTemplate(prompt);
                finalPrompt = promptTemplate.create(parameters);
            } else {
                finalPrompt = new Prompt(prompt);
            }

            // 调用Ollama模型
            ChatResponse response = ollamaChatModel.call(finalPrompt);
            String result = response.getResult().getOutput().getContent();

            log.debug("Spring AI Ollama调用成功，响应长度: {}", result.length());
            return result;

        } catch (Exception e) {
            log.error("Spring AI Ollama调用失败: {}", e.getMessage(), e);
            throw new RuntimeException("Spring AI Ollama调用失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }

    @Override
    public boolean isAvailable() {
        try {
            // 简单的可用性检查
            ChatResponse response = ollamaChatModel.call(new Prompt("test"));
            return response != null && response.getResult() != null;
        } catch (Exception e) {
            log.warn("Spring AI Ollama服务不可用: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public Map<String, Object> getModelInfo() {
        return Map.of(
                "provider", PROVIDER_NAME,
                "type", "Spring AI Ollama",
                "description", "基于Spring AI框架的Ollama集成",
                "features", new String[]{"chat", "streaming", "local-deployment"},
                "framework", "Spring AI 1.0.0"
        );
    }
}
