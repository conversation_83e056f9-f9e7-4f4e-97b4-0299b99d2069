package com.nybc.ai.service.impl;

import com.nybc.ai.service.AiChatService;
import com.nybc.ai.service.PromptEngineeringService;
import jakarta.annotation.Resource;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.memory.InMemoryChatMemoryRepository;
import org.springframework.ai.chat.messages.Message;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 类描述：AI聊天服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Service
public class AiChatServiceImpl implements AiChatService {

    @Resource
    private ChatClient.Builder chatClientBuilder;

    @Resource
    private PromptEngineeringService promptEngineeringService;

    /**
     * 适配器类，用于将 ChatMemoryRepository 适配到 ChatMemory 接口。
     */
    private static class RepositoryChatMemoryAdapter implements ChatMemory {

        private final ChatMemoryRepository repository;

        public RepositoryChatMemoryAdapter(ChatMemoryRepository repository) {
            this.repository = repository;
        }

        @Override
        public List<Message> get(String conversationId) {
            return new ArrayList<>(this.repository.findByConversationId(conversationId));
        }

        @Override
        public void add(String conversationId, List<Message> messages) {
            List<Message> history = new ArrayList<>(this.repository.findByConversationId(conversationId));
            history.addAll(messages);
            this.repository.saveAll(conversationId, history);
        }

        @Override
        public void clear(String conversationId) {
            this.repository.deleteByConversationId(conversationId);
        }
    }

    /**
     * 使用内存存储库来管理不同会话的聊天记录
     */
    private final ChatMemoryRepository chatMemoryRepository = new InMemoryChatMemoryRepository();

    /**
     * 根据用户输入和会话ID，执行带有上下文记忆的AI聊天流程
     *
     * @param sessionId 会话的唯一标识符
     * @param prompt    用户的原始输入
     * @return 模型返回的、已解析为Map的JSON数据
     */
    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> chat(String sessionId, String prompt) {
        // 步骤1：检查是否是新对话的第一次请求
        boolean isNewConversation = this.chatMemoryRepository.findByConversationId(sessionId).isEmpty();
        String finalPrompt = prompt;

        // 如果是新对话，使用Prompt优化服务
        if (isNewConversation) {
            finalPrompt = promptEngineeringService.optimizePrompt(prompt);
        }

        // 步骤2：根据仓库创建一个ChatMemory实例
        ChatMemory chatMemory = new RepositoryChatMemoryAdapter(this.chatMemoryRepository);

        // 步骤3：使用Builder模式创建Advisor
        MessageChatMemoryAdvisor advisor = MessageChatMemoryAdvisor.builder(chatMemory)
                .conversationId(sessionId)
                .build();

        // 步骤4：构建带有特定会话记忆的ChatClient
        ChatClient chatClient = chatClientBuilder
                .defaultAdvisors(advisor)
                .build();

        // 步骤5：执行调用
        return chatClient.prompt()
                .user(finalPrompt)
                .call()
                .entity(Map.class);
    }
}