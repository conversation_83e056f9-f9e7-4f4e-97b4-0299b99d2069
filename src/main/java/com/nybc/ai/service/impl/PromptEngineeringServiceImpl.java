package com.nybc.ai.service.impl;

import com.nybc.ai.service.PromptEngineeringService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 类描述：Prompt工程服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Service
public class PromptEngineeringServiceImpl implements PromptEngineeringService {

    @Resource
    private ChatClient.Builder chatClientBuilder;

    private static final String OPTIMIZATION_PROMPT_TEMPLATE = """
            你是一位资深的Prompt优化专家。
            请将以下用户问题转换成一个结构化、清晰、并且能让大语言模型（LLM）返回JSON格式的优化版Prompt。
            
            **优化要求**:
            1.  **意图清晰**：准确理解用户原始意图，消除模糊性。
            2.  **内容丰富**：如果原始问题过于简单，适当补充上下文或限定条件，使其更具可操作性。
            3.  **JSON输出指令**：在Prompt末尾明确指示LLM必须返回JSON对象，如果可能，请定义一个合理的JSON结构。
            4.  **保留核心问题**：优化后的Prompt必须保留用户的核心问题。
            
            **原始问题**:
            "%s"
            
            请输出优化后的Prompt内容，不要包含任何额外的解释或说明，直接给出结果。
            """;

    /**
     * 将用户的原始输入优化成一个结构更清晰、内容更丰富、对模型更友好的Prompt。
     *
     * @param originalPrompt 用户的原始输入
     * @return 优化后的Prompt字符串
     */
    @Override
    public String optimizePrompt(String originalPrompt) {
        ChatClient chatClient = chatClientBuilder.build();
        String optimizationRequest = String.format(OPTIMIZATION_PROMPT_TEMPLATE, originalPrompt);
        
        return chatClient.prompt()
                .user(optimizationRequest)
                .call()
                .content();
    }
} 