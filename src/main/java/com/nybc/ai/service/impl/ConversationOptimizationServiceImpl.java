package com.nybc.ai.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.infra.ai.AiModelService;
import com.nybc.ai.infra.ai.AiModelServiceFactory;
import com.nybc.ai.service.ConversationOptimizationService;
import com.nybc.ai.service.dto.ConversationContext;
import com.nybc.ai.service.dto.ConversationSummary;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述：对话优化服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class ConversationOptimizationServiceImpl implements ConversationOptimizationService {

    @Resource
    private ChatMemoryRepository chatMemoryRepository;

    @Resource
    private AiModelServiceFactory aiModelServiceFactory;

    // 统一AI服务（优先使用）
    @Resource(required = false)
    private UnifiedAiService unifiedAiService;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${app.ai.default-provider:deepseek}")
    private String defaultAiProvider;

    @Value("${app.conversation.max-context-tokens:4000}")
    private int maxContextTokens;

    @Override
    public ConversationContext compressContext(String sessionId, int maxTokens) {
        try {
            log.info("开始压缩对话上下文: 会话ID[{}], 最大Token[{}]", sessionId, maxTokens);

            // 1. 获取对话历史
            List<Message> messages = chatMemoryRepository.findByConversationId(sessionId);
            if (messages.isEmpty()) {
                return new ConversationContext().setSessionId(sessionId).setTokenCount(0);
            }

            // 2. 分析消息重要性
            List<ConversationContext.CompressedMessage> analyzedMessages = analyzeMessageImportance(messages);

            // 3. 智能压缩
            ConversationContext context = performIntelligentCompression(sessionId, analyzedMessages, maxTokens);

            log.info("对话上下文压缩完成: 会话ID[{}], 压缩比例[{}]", sessionId, context.getCompressionRatio());
            return context;

        } catch (Exception e) {
            log.error("压缩对话上下文失败: {}", e.getMessage(), e);
            throw new RuntimeException("压缩对话上下文失败", e);
        }
    }

    @Override
    public ConversationSummary generateSummary(String sessionId, String summaryType) {
        try {
            log.info("开始生成对话摘要: 会话ID[{}], 摘要类型[{}]", sessionId, summaryType);

            // 1. 获取对话历史
            List<Message> messages = chatMemoryRepository.findByConversationId(sessionId);
            if (messages.isEmpty()) {
                return new ConversationSummary().setSessionId(sessionId).setOriginalMessageCount(0);
            }

            // 2. 使用AI生成摘要
            String summaryPrompt = buildSummaryPrompt(messages, summaryType);
            AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
            String aiResponse = aiService.call(summaryPrompt, Collections.singletonMap("temperature", 0.3));

            // 3. 解析AI响应
            ConversationSummary summary = parseSummaryResponse(aiResponse, sessionId, summaryType);
            summary.setOriginalMessageCount(messages.size())
                    .setGenerateTime(LocalDateTime.now());

            log.info("对话摘要生成完成: 会话ID[{}], 消息数[{}]", sessionId, messages.size());
            return summary;

        } catch (Exception e) {
            log.error("生成对话摘要失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成对话摘要失败", e);
        }
    }

    @Override
    public String optimizeMultiTurnPrompt(String sessionId, String currentPrompt, Map<String, Object> domainContext) {
        try {
            log.info("开始优化多轮对话Prompt: 会话ID[{}]", sessionId);

            // 1. 获取压缩后的上下文
            ConversationContext context = compressContext(sessionId, maxContextTokens);

            // 2. 构建优化Prompt
            String optimizationPrompt = buildMultiTurnOptimizationPrompt(context, currentPrompt, domainContext);

            // 3. 使用AI优化
            AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
            String optimizedPrompt = aiService.call(optimizationPrompt, Collections.singletonMap("temperature", 0.4));

            log.info("多轮对话Prompt优化完成: 会话ID[{}]", sessionId);
            return optimizedPrompt.trim();

        } catch (Exception e) {
            log.error("优化多轮对话Prompt失败: {}", e.getMessage(), e);
            return currentPrompt; // 失败时返回原始Prompt
        }
    }

    @Override
    public boolean detectTopicShift(String sessionId, String newMessage) {
        try {
            // 1. 获取最近的对话历史
            List<Message> recentMessages = chatMemoryRepository.findByConversationId(sessionId)
                    .stream()
                    .sorted((m1, m2) -> m2.getMetadata().get("timestamp").toString()
                            .compareTo(m1.getMetadata().get("timestamp").toString()))
                    .limit(5)
                    .collect(Collectors.toList());

            if (recentMessages.size() < 2) {
                return false; // 消息太少，无法判断主题转换
            }

            // 2. 构建主题检测Prompt
            String topicDetectionPrompt = buildTopicDetectionPrompt(recentMessages, newMessage);

            // 3. 使用AI检测
            AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
            String response = aiService.call(topicDetectionPrompt, Collections.singletonMap("temperature", 0.2));

            // 4. 解析结果
            return response.toLowerCase().contains("true") || response.toLowerCase().contains("是");

        } catch (Exception e) {
            log.error("检测主题转换失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public ConversationContext manageContext(String sessionId, int maxContextLength) {
        try {
            // 1. 检测是否需要主题转换
            List<Message> messages = chatMemoryRepository.findByConversationId(sessionId);
            if (messages.isEmpty()) {
                return new ConversationContext().setSessionId(sessionId);
            }

            // 2. 如果上下文过长，进行智能压缩
            if (estimateTokenCountFromMessages(messages) > maxContextLength) {
                return compressContext(sessionId, maxContextLength);
            }

            // 3. 构建当前上下文
            ConversationContext context = new ConversationContext()
                    .setSessionId(sessionId)
                    .setTokenCount(estimateTokenCountFromMessages(messages))
                    .setLastUpdated(LocalDateTime.now());

            // 4. 提取领域相关信息
            context.setDomainContext(extractDomainEntities(sessionId));

            return context;

        } catch (Exception e) {
            log.error("管理对话上下文失败: {}", e.getMessage(), e);
            throw new RuntimeException("管理对话上下文失败", e);
        }
    }

    @Override
    public Map<String, Object> extractDomainEntities(String sessionId) {
        try {
            // 1. 获取对话历史
            List<Message> messages = chatMemoryRepository.findByConversationId(sessionId);
            if (messages.isEmpty()) {
                return new HashMap<>();
            }

            // 2. 构建实体提取Prompt
            String entityExtractionPrompt = buildEntityExtractionPrompt(messages);

            // 3. 使用AI提取实体
            AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
            String response = aiService.call(entityExtractionPrompt, Collections.singletonMap("temperature", 0.3));

            // 4. 解析实体信息
            return parseEntityResponse(response);

        } catch (Exception e) {
            log.error("提取领域实体失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 分析消息重要性
     */
    private List<ConversationContext.CompressedMessage> analyzeMessageImportance(List<Message> messages) {
        return messages.stream().map(message -> {
            ConversationContext.CompressedMessage compressedMessage = new ConversationContext.CompressedMessage()
                    .setRole(message.getMessageType().name().toLowerCase())
                    .setContent(message.getText())
                    .setTimestamp(LocalDateTime.now());

            // 简单的重要性评分逻辑
            double importance = calculateMessageImportance(message.getText());
            compressedMessage.setImportance(importance)
                    .setIsKey(importance > 0.7);

            return compressedMessage;
        }).collect(Collectors.toList());
    }

    /**
     * 计算消息重要性
     */
    private double calculateMessageImportance(String content) {
        double importance = 0.5; // 基础重要性

        // 包含代码的消息更重要
        if (content.contains("```") || content.contains("class ") || content.contains("function ")) {
            importance += 0.3;
        }

        // 包含技术术语的消息更重要
        String[] techTerms = {"算法", "数据结构", "设计模式", "架构", "数据库", "API", "框架"};
        for (String term : techTerms) {
            if (content.contains(term)) {
                importance += 0.1;
                break;
            }
        }

        // 问题和解答更重要
        if (content.contains("?") || content.contains("？") || content.contains("如何") || content.contains("怎么")) {
            importance += 0.2;
        }

        return Math.min(importance, 1.0);
    }

    /**
     * 执行智能压缩
     */
    private ConversationContext performIntelligentCompression(String sessionId, 
                                                             List<ConversationContext.CompressedMessage> messages, 
                                                             int maxTokens) {
        // 1. 保留关键消息
        List<ConversationContext.CompressedMessage> keyMessages = messages.stream()
                .filter(ConversationContext.CompressedMessage::getIsKey)
                .collect(Collectors.toList());

        // 2. 按重要性排序其他消息
        List<ConversationContext.CompressedMessage> otherMessages = messages.stream()
                .filter(msg -> !msg.getIsKey())
                .sorted((m1, m2) -> Double.compare(m2.getImportance(), m1.getImportance()))
                .collect(Collectors.toList());

        // 3. 根据token限制选择消息
        List<ConversationContext.CompressedMessage> finalMessages = new ArrayList<>(keyMessages);
        int currentTokens = estimateTokenCountFromCompressedMessages(keyMessages);

        for (ConversationContext.CompressedMessage msg : otherMessages) {
            int msgTokens = estimateTokenCount(msg.getContent());
            if (currentTokens + msgTokens <= maxTokens) {
                finalMessages.add(msg);
                currentTokens += msgTokens;
            }
        }

        // 4. 构建上下文
        ConversationContext context = new ConversationContext()
                .setSessionId(sessionId)
                .setCompressedMessages(finalMessages)
                .setTokenCount(currentTokens)
                .setCompressionRatio((double) finalMessages.size() / messages.size())
                .setLastUpdated(LocalDateTime.now());

        return context;
    }

    /**
     * 构建摘要生成Prompt
     */
    private String buildSummaryPrompt(List<Message> messages, String summaryType) {
        StringBuilder conversationText = new StringBuilder();
        for (Message message : messages) {
            conversationText.append(message.getMessageType().name())
                    .append(": ")
                    .append(message.getText())
                    .append("\n");
        }

        return String.format("""
                你是一位专业的计算机、软件学院教学助手。请对以下对话进行%s摘要。
                
                ## 对话内容：
                %s
                
                ## 摘要要求：
                请以JSON格式返回摘要，包含以下字段：
                - mainSummary: 主要摘要内容
                - keyPoints: 关键讨论点列表
                - technicalPoints: 技术要点列表
                - learningOutcomes: 学习目标和成果列表
                - problemSolutions: 问题和解决方案对象
                - technologies: 涉及的技术栈和工具列表
                - codeExamples: 代码片段和示例列表
                - nextSteps: 后续建议列表
                
                请确保摘要内容专业、准确，突出计算机和软件工程相关的技术要点。
                """, 
                summaryType.equals("BRIEF") ? "简要" : summaryType.equals("DETAILED") ? "详细" : "技术性",
                conversationText.toString());
    }

    /**
     * 解析摘要响应
     */
    @SuppressWarnings("unchecked")
    private ConversationSummary parseSummaryResponse(String response, String sessionId, String summaryType) {
        try {
            Map<String, Object> summaryData = objectMapper.readValue(response, Map.class);
            
            ConversationSummary summary = new ConversationSummary()
                    .setSessionId(sessionId)
                    .setSummaryType(summaryType)
                    .setMainSummary((String) summaryData.get("mainSummary"))
                    .setKeyPoints((List<String>) summaryData.get("keyPoints"))
                    .setTechnicalPoints((List<String>) summaryData.get("technicalPoints"))
                    .setLearningOutcomes((List<String>) summaryData.get("learningOutcomes"))
                    .setProblemSolutions((Map<String, String>) summaryData.get("problemSolutions"))
                    .setTechnologies((List<String>) summaryData.get("technologies"))
                    .setNextSteps((List<String>) summaryData.get("nextSteps"));

            return summary;
        } catch (Exception e) {
            log.warn("解析摘要响应失败，返回默认摘要: {}", e.getMessage());
            return new ConversationSummary()
                    .setSessionId(sessionId)
                    .setSummaryType(summaryType)
                    .setMainSummary("对话摘要生成失败，请重试");
        }
    }

    /**
     * 构建多轮对话优化Prompt
     */
    private String buildMultiTurnOptimizationPrompt(ConversationContext context, String currentPrompt, 
                                                   Map<String, Object> domainContext) {
        return String.format("""
                你是一位专业的计算机、软件学院AI助教。请基于对话历史和领域上下文，优化当前的用户问题。
                
                ## 对话历史摘要：
                %s
                
                ## 当前用户问题：
                %s
                
                ## 领域上下文：
                %s
                
                ## 优化要求：
                1. 保持用户的核心意图
                2. 结合对话历史，避免重复询问已讨论的内容
                3. 针对计算机、软件学院的专业特点，使问题更加精确
                4. 如果涉及代码或技术概念，请明确指出相关的技术栈
                5. 确保问题具有教学价值和实践意义
                
                请直接返回优化后的问题，不要包含额外的解释。
                """,
                context.getKeySummary() != null ? context.getKeySummary() : "暂无历史摘要",
                currentPrompt,
                domainContext.toString());
    }

    /**
     * 构建主题检测Prompt
     */
    private String buildTopicDetectionPrompt(List<Message> recentMessages, String newMessage) {
        StringBuilder recentText = new StringBuilder();
        for (Message message : recentMessages) {
            recentText.append(message.getText()).append("\n");
        }

        return String.format("""
                请判断新消息是否与最近的对话主题发生了明显转换。
                
                ## 最近的对话：
                %s
                
                ## 新消息：
                %s
                
                ## 判断标准：
                - 如果新消息讨论的是完全不同的技术领域或概念，返回 true
                - 如果新消息是对之前讨论内容的延续或深入，返回 false
                - 如果新消息是相关但不同的子话题，返回 false
                
                请只返回 true 或 false，不要包含其他内容。
                """,
                recentText.toString(),
                newMessage);
    }

    /**
     * 构建实体提取Prompt
     */
    private String buildEntityExtractionPrompt(List<Message> messages) {
        StringBuilder conversationText = new StringBuilder();
        for (Message message : messages) {
            conversationText.append(message.getText()).append("\n");
        }

        return String.format("""
                请从以下对话中提取计算机、软件学院相关的关键实体和概念。
                
                ## 对话内容：
                %s
                
                ## 提取要求：
                请以JSON格式返回，包含以下字段：
                - programmingLanguages: 编程语言列表
                - frameworks: 框架和库列表
                - concepts: 技术概念列表
                - tools: 开发工具列表
                - algorithms: 算法和数据结构列表
                - designPatterns: 设计模式列表
                - databases: 数据库相关列表
                - topics: 主要讨论话题列表
                
                只提取明确提到的技术实体，不要推测。
                """,
                conversationText.toString());
    }

    /**
     * 解析实体响应
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseEntityResponse(String response) {
        try {
            return objectMapper.readValue(response, Map.class);
        } catch (Exception e) {
            log.warn("解析实体响应失败: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 估算token数量（简单实现）
     */
    private int estimateTokenCountFromMessages(List<Message> messages) {
        return messages.stream()
                .mapToInt(msg -> estimateTokenCount(msg.getText()))
                .sum();
    }

    private int estimateTokenCountFromCompressedMessages(List<ConversationContext.CompressedMessage> messages) {
        return messages.stream()
                .mapToInt(msg -> estimateTokenCount(msg.getContent()))
                .sum();
    }

    private int estimateTokenCount(String text) {
        // 简单的token估算：中文按字符数，英文按单词数*1.3
        if (text == null) return 0;
        
        long chineseChars = text.chars().filter(ch -> ch >= 0x4E00 && ch <= 0x9FFF).count();
        long englishWords = text.split("\\s+").length;
        
        return (int) (chineseChars + englishWords * 1.3);
    }
}
