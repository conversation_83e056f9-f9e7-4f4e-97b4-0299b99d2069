package com.nybc.ai.service.impl;

import com.nybc.ai.domain.PromptTemplate;
import com.nybc.ai.domain.PromptVersion;
import com.nybc.ai.prompt.dto.*;
import com.nybc.ai.prompt.mapper.PromptTemplateMapper;
import com.nybc.ai.prompt.mapper.PromptVersionMapper;
import com.nybc.ai.service.PromptMgmtService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 类描述：Prompt管理相关的业务服务实现
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class PromptMgmtServiceImpl implements PromptMgmtService {

    @Resource
    private PromptTemplateMapper templateMapper;

    @Resource
    private PromptVersionMapper versionMapper;

    @Override
    @Transactional
    public PromptTemplateDetailResponse createPromptTemplate(PromptTemplateCreateRequest request) {
        // 1. 检查Key的唯一性
        PromptTemplate existing = templateMapper.findByKeyAndTenant(request.getPromptKey(), request.getTenantId());
        Assert.isNull(existing, "该租户下已存在相同的Prompt Key: " + request.getPromptKey());

        // 2. 创建主表记录
        PromptTemplate newTemplate = new PromptTemplate();
        BeanUtils.copyProperties(request, newTemplate);
        newTemplate.setCreateUser(request.getCreateUser());
        newTemplate.setUpdateUser(request.getCreateUser());
        templateMapper.insert(newTemplate);

        // 3. 创建初始版本
        PromptVersion firstVersion = new PromptVersion();
        firstVersion.setTemplateId(newTemplate.getId());
        firstVersion.setTenantId(request.getTenantId());
        firstVersion.setVersionNumber(1);
        firstVersion.setTemplateContent(request.getTemplateContent());
        firstVersion.setChangelog("初始版本创建");
        firstVersion.setCreateUser(request.getCreateUser());
        firstVersion.setUpdateUser(request.getCreateUser());
        versionMapper.insert(firstVersion);
        
        // 4. 回填主表的活动版本信息
        newTemplate.setActiveVersionId(firstVersion.getId());
        newTemplate.setActiveTemplateContent(firstVersion.getTemplateContent());
        // 可以在这里设置默认的provider
        // newTemplate.setActiveRuntimeProvider("default-provider");
        templateMapper.update(newTemplate);

        log.info("成功创建Prompt模板 [key={}, id={}]", newTemplate.getPromptKey(), newTemplate.getId());

        // 5. 构造并返回结果
        return getPromptTemplateDetail(newTemplate.getId());
    }

    @Override
    public PromptVersionResponse createPromptVersion(PromptVersionCreateRequest request) {
        // (省略) 具体实现...
        return null;
    }

    @Override
    public void setActiveVersion(SetActiveVersionRequest request) {
         // (省略) 具体实现...
    }

    @Override
    public PromptTemplateDetailResponse getPromptTemplateDetail(Long templateId) {
        PromptTemplate template = templateMapper.findById(templateId);
        if (template == null) {
            return null;
        }

        List<PromptVersion> versions = versionMapper.findByTemplateId(templateId);

        PromptTemplateDetailResponse response = new PromptTemplateDetailResponse();
        BeanUtils.copyProperties(template, response);

        response.setVersions(versions.stream()
                .map(PromptVersionResponse::fromDomain)
                .collect(Collectors.toList()));
        return response;
    }

    @Override
    public PromptTemplateResponse getPromptTemplateByKey(String promptKey, Long tenantId) {
        // (省略) 具体实现...
        return null;
    }

    @Override
    public List<PromptTemplateResponse> listPromptTemplatesByTenant(Long tenantId) {
        // (省略) 具体实现...
        return null;
    }

    @Override
    @Transactional
    public void deletePromptTemplate(Long templateId, Long userId) {
        // (省略) 具体实现...
        log.info("逻辑删除Prompt模板 [id={}]", templateId);
    }
} 