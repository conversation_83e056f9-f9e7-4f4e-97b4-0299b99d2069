package com.nybc.ai.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * AI文件解析服务接口
 * <p>
 * 提供统一的文件处理入口，根据文件类型提取文本内容，并交由AI进行分析。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
public interface FileParserService {

    /**
     * 解析上传的文件，并使用指定的提示词调用AI进行分析
     *
     * @param file   用户上传的文件
     * @param prompt 用于指导AI分析的提示词
     * @return AI模型的分析结果
     */
    String parseFile(MultipartFile file, String prompt);

    /**
     * 解析指定服务器目录下的所有文件，并使用指定的提示词调用AI进行分析
     *
     * @param directoryPath 服务器上的目录路径
     * @param prompt        用于指导AI分析的提示词
     * @return AI模型的分析结果
     */
    String parseDirectory(String directoryPath, String prompt);
} 