package com.nybc.ai.service;

import com.nybc.ai.service.dto.DomainPromptOptimizationRequest;
import com.nybc.ai.service.dto.DomainPromptOptimizationResponse;

import java.util.Map;

/**
 * 类描述：领域特定Prompt服务接口
 * 专注于计算机、软件学院课程教学的Prompt优化
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface DomainSpecificPromptService {

    /**
     * 针对计算机、软件学院领域优化Prompt
     *
     * @param request 领域优化请求
     * @return 优化结果
     */
    DomainPromptOptimizationResponse optimizeForComputerScience(DomainPromptOptimizationRequest request);

    /**
     * 为代码评估场景优化Prompt
     *
     * @param originalPrompt 原始Prompt
     * @param codeContext 代码上下文信息
     * @param evaluationCriteria 评估标准
     * @return 优化后的Prompt
     */
    String optimizeForCodeEvaluation(String originalPrompt, Map<String, Object> codeContext, 
                                   Map<String, Object> evaluationCriteria);

    /**
     * 为作业批改场景优化Prompt
     *
     * @param originalPrompt 原始Prompt
     * @param assignmentType 作业类型：PROGRAMMING, DESIGN, REPORT, PROJECT
     * @param gradingRubric 评分标准
     * @return 优化后的Prompt
     */
    String optimizeForAssignmentGrading(String originalPrompt, String assignmentType, 
                                      Map<String, Object> gradingRubric);

    /**
     * 为学生答疑场景优化Prompt
     *
     * @param originalPrompt 原始Prompt
     * @param studentLevel 学生水平：BEGINNER, INTERMEDIATE, ADVANCED
     * @param courseContext 课程上下文
     * @return 优化后的Prompt
     */
    String optimizeForStudentTutoring(String originalPrompt, String studentLevel, 
                                    Map<String, Object> courseContext);

    /**
     * 为技术文档分析优化Prompt
     *
     * @param originalPrompt 原始Prompt
     * @param documentType 文档类型：API_DOC, DESIGN_DOC, USER_MANUAL, TECHNICAL_SPEC
     * @param analysisGoal 分析目标
     * @return 优化后的Prompt
     */
    String optimizeForDocumentAnalysis(String originalPrompt, String documentType, 
                                     String analysisGoal);

    /**
     * 自动检测并应用最佳Prompt模式
     *
     * @param originalPrompt 原始Prompt
     * @param context 上下文信息
     * @return 应用最佳模式后的Prompt
     */
    String autoApplyBestPattern(String originalPrompt, Map<String, Object> context);

    /**
     * 基于历史数据自动调优Prompt
     *
     * @param promptKey Prompt模板键
     * @param tenantId 租户ID
     * @param optimizationGoal 优化目标：ACCURACY, SPEED, COMPREHENSIVENESS
     * @return 调优结果
     */
    DomainPromptOptimizationResponse autoTunePrompt(String promptKey, Long tenantId, String optimizationGoal);

    /**
     * 生成领域特定的Prompt模板
     *
     * @param scenario 应用场景
     * @param requirements 具体需求
     * @return 生成的Prompt模板
     */
    String generateDomainTemplate(String scenario, Map<String, Object> requirements);
}
