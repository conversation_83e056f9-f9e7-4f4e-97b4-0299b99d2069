package com.nybc.ai.service;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 类描述：文档查询服务
 * 负责构建最终的Prompt，调用AI模型，并返回格式化的答案。
 * 支持统一AI架构和传统ChatClient两种模式
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Service
public class DocumentQueryService {

    private final ChatClient chatClient;
    private final PromptTemplate promptTemplate;

    // 统一AI服务（优先使用）
    @jakarta.annotation.Resource(required = false)
    private UnifiedAiService unifiedAiService;

    /**
     * DocumentQueryService 构造函数
     *
     * @param chatClientBuilder 由Spring AI starter自动配置的ChatClient.Builder
     * @param resource          Prompt模板资源文件
     */
    public DocumentQueryService(ChatClient.Builder chatClientBuilder,
                                @Value("classpath:/prompts/doc_query.st") Resource resource) {
        this.chatClient = chatClientBuilder.build();
        this.promptTemplate = new PromptTemplate(resource);
    }

    /**
     * 根据用户问题和检索到的上下文，向AI提问并获取JSON回答。
     *
     * @param userQuery 用户的原始问题
     * @param context   从向量数据库中检索到的相关文档块
     * @return AI生成的JSON格式的回答字符串
     */
    public String queryDocument(String userQuery, List<Document> context) {
        // 将上下文文档块列表转换成一个单一的字符串，用分隔符隔开
        String contextStr = context.stream()
            .map(Document::getText)
            .collect(Collectors.joining("\n---\n"));

        // 优先使用统一AI服务
        if (unifiedAiService != null) {
            return queryWithUnifiedService(userQuery, contextStr);
        } else {
            return queryWithChatClient(userQuery, contextStr);
        }
    }

    /**
     * 使用统一AI服务进行查询
     */
    private String queryWithUnifiedService(String userQuery, String contextStr) {
        String promptTemplate = """
                基于以下上下文信息回答用户问题：

                上下文：
                {context_chunks}

                用户问题：{user_query}

                请基于上下文信息提供准确、详细的回答。如果上下文中没有相关信息，请明确说明。
                """;

        Map<String, Object> parameters = Map.of(
            "user_query", userQuery,
            "context_chunks", contextStr
        );

        return unifiedAiService.chat(promptTemplate, parameters);
    }

    /**
     * 使用传统ChatClient进行查询（备用方案）
     */
    private String queryWithChatClient(String userQuery, String contextStr) {
        // 填充Prompt模板
        Map<String, Object> model = Map.of(
            "user_query", userQuery,
            "context_chunks", contextStr
        );
        Prompt prompt = this.promptTemplate.create(model);

        // 调用AI并直接获取字符串内容
        return this.chatClient.prompt(prompt).call().content();
    }

    /**
     * 使用指定AI提供商进行文档查询
     *
     * @param userQuery 用户问题
     * @param context   文档上下文
     * @param provider  AI提供商
     * @return AI回答
     */
    public String queryDocumentWithProvider(String userQuery, List<Document> context, String provider) {
        if (unifiedAiService == null) {
            throw new UnsupportedOperationException("统一AI服务未配置，无法使用指定提供商");
        }

        String contextStr = context.stream()
            .map(Document::getText)
            .collect(Collectors.joining("\n---\n"));

        String promptTemplate = """
                基于以下上下文信息回答用户问题：

                上下文：
                {context_chunks}

                用户问题：{user_query}

                请基于上下文信息提供准确、详细的回答。如果上下文中没有相关信息，请明确说明。
                """;

        Map<String, Object> parameters = Map.of(
            "user_query", userQuery,
            "context_chunks", contextStr
        );

        return unifiedAiService.chatWithProvider(provider, promptTemplate, parameters);
    }
}