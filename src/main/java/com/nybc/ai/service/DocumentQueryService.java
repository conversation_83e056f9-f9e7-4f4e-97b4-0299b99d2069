package com.nybc.ai.service;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 类描述：文档查询服务
 * 负责构建最终的Prompt，调用AI模型，并返回格式化的答案。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Service
public class DocumentQueryService {

    private final ChatClient chatClient;
    private final PromptTemplate promptTemplate;

    /**
     * DocumentQueryService 构造函数
     *
     * @param chatClientBuilder 由Spring AI starter自动配置的ChatClient.Builder
     * @param resource          Prompt模板资源文件
     */
    public DocumentQueryService(ChatClient.Builder chatClientBuilder,
                                @Value("classpath:/prompts/doc_query.st") Resource resource) {
        this.chatClient = chatClientBuilder.build();
        this.promptTemplate = new PromptTemplate(resource);
    }

    /**
     * 根据用户问题和检索到的上下文，向AI提问并获取JSON回答。
     *
     * @param userQuery 用户的原始问题
     * @param context   从向量数据库中检索到的相关文档块
     * @return AI生成的JSON格式的回答字符串
     */
    public String queryDocument(String userQuery, List<Document> context) {
        // 将上下文文档块列表转换成一个单一的字符串，用分隔符隔开
        String contextStr = context.stream()
            .map(Document::getText)
            .collect(Collectors.joining("\n---\n"));

        // 填充Prompt模板
        Map<String, Object> model = Map.of(
            "user_query", userQuery,
            "context_chunks", contextStr
        );
        Prompt prompt = this.promptTemplate.create(model);

        // 调用AI并直接获取字符串内容
        return this.chatClient.prompt(prompt).call().content();
    }
}