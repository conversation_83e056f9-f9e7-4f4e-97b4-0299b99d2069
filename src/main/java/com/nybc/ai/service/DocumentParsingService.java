package com.nybc.ai.service;

import org.springframework.ai.reader.ExtractedTextFormatter;
import org.springframework.ai.reader.pdf.PagePdfDocumentReader;
import org.springframework.ai.reader.pdf.config.PdfDocumentReaderConfig;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.document.Document;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 类描述：文档解析服务
 * 负责将原始文件资源（如PDF）加载、解析并切分成结构化的Document块。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Service
public class DocumentParsingService {

    /**
     * 将文件资源解析并切分成可供AI处理的Document块。
     *
     * @param fileResource 指向用户上传文件的资源
     * @return 一系列带有元数据的Document块
     */
    public List<Document> parseAndChunk(Resource fileResource) {
        // 1. 智能加载 (此处以PDF为例)
        // 使用PagePdfDocumentReader可以按页读取，并保留页码等元数据
        PagePdfDocumentReader documentReader = new PagePdfDocumentReader(fileResource,
            PdfDocumentReaderConfig.builder()
                .withPageExtractedTextFormatter(ExtractedTextFormatter.builder()
                    .withNumberOfBottomTextLinesToDelete(0)
                    .withNumberOfTopTextLinesToDelete(0)
                    .build())
                .build());
        List<Document> documents = documentReader.get();

        // 2. 文本分块 (Chunking)
        // TokenTextSplitter会尝试在不破坏句子完整性的前提下，按Token数量切分。
        // 这是为了确保每个送入模型的文本块都不会超过其上下文窗口限制。
        TokenTextSplitter textSplitter = new TokenTextSplitter();

        return textSplitter.apply(documents);
    }
} 