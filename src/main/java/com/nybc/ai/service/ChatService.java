package com.nybc.ai.service;

import com.nybc.ai.service.dto.ChatRequest;
import reactor.core.publisher.Flux;

import java.util.Map;

/**
 * 类描述：统一的AI聊天服务接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface ChatService {

    /**
     * 流式聊天
     *
     * @param request 聊天请求参数
     * @return 返回一个包含聊天响应的Flux流
     */
    Flux<String> streamChat(ChatRequest request);


    /**
     * 获取该服务支持的提供商名称
     *
     * @return 提供商名称 (e.g., "dashscope", "ollama")
     */
    String getProvider();
} 