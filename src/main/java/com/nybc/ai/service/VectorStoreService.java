package com.nybc.ai.service;

import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import java.util.List;

/**
 * 类描述：向量存储服务
 * 负责将文档块进行向量化索引，并根据用户查询提供上下文检索功能。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Service
public class VectorStoreService {

    @Resource
    private VectorStore vectorStore;

    /**
     * 将文档块列表存入向量数据库进行索引。
     *
     * @param chunks 文档块列表
     * @param fileId 关联的文件ID，用于后续的精确检索
     */
    public void indexDocuments(List<Document> chunks, String fileId) {
        // 为每个文档块附加一个fileId元数据，以便进行过滤搜索
        chunks.forEach(chunk -> chunk.getMetadata().put("file_id", fileId));

        // 批量将文档添加到VectorStore。
        // VectorStore会自动调用其配置的EmbeddingClient来生成向量。
        this.vectorStore.add(chunks);
    }

    /**
     * 根据用户查询和文件ID，从向量数据库中检索最相关的上下文片段。
     *
     * @param userQuery 用户的自然语言查询
     * @param fileId    要检索的特定文件的ID
     * @return 与查询最相关的文档块列表
     */
    public List<Document> retrieveContext(String userQuery, String fileId) {
        // 构建一个符合元数据过滤规范的表达式字符串。
        // 这种方式比编程API更具通用性。
        String filterExpression = "metadata.file_id == '" + fileId + "'";

        // 构建检索请求，查找与用户问题最相似的Top K个文本块
        SearchRequest request = SearchRequest.builder()
                .query(userQuery)
                .topK(5)
                .withFilterExpression(filterExpression)
                .build();

        return this.vectorStore.similaritySearch(request);
    }
} 