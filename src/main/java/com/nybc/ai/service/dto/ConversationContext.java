package com.nybc.ai.service.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 类描述：对话上下文数据传输对象
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class ConversationContext {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 压缩后的对话历史
     */
    private List<CompressedMessage> compressedMessages;

    /**
     * 关键信息摘要
     */
    private String keySummary;

    /**
     * 当前对话主题
     */
    private String currentTopic;

    /**
     * 领域相关的上下文信息
     */
    private Map<String, Object> domainContext;

    /**
     * 提取的关键实体
     */
    private Map<String, Object> keyEntities;

    /**
     * 上下文token数量
     */
    private Integer tokenCount;

    /**
     * 压缩比例
     */
    private Double compressionRatio;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdated;

    @Data
    @Accessors(chain = true)
    public static class CompressedMessage {
        /**
         * 消息角色：user, assistant, system
         */
        private String role;

        /**
         * 压缩后的消息内容
         */
        private String content;

        /**
         * 原始消息的重要性评分
         */
        private Double importance;

        /**
         * 消息时间戳
         */
        private LocalDateTime timestamp;

        /**
         * 是否为关键消息
         */
        private Boolean isKey;
    }
}
