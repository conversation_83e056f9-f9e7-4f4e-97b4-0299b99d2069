package com.nybc.ai.service.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 类描述：对话摘要数据传输对象
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class ConversationSummary {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 摘要类型：BRIEF, DETAILED, TECHNICAL
     */
    private String summaryType;

    /**
     * 主要摘要内容
     */
    private String mainSummary;

    /**
     * 关键讨论点
     */
    private List<String> keyPoints;

    /**
     * 技术要点（针对计算机、软件学院）
     */
    private List<String> technicalPoints;

    /**
     * 学习目标和成果
     */
    private List<String> learningOutcomes;

    /**
     * 问题和解决方案
     */
    private Map<String, String> problemSolutions;

    /**
     * 涉及的技术栈和工具
     */
    private List<String> technologies;

    /**
     * 代码片段和示例
     */
    private List<CodeSnippet> codeExamples;

    /**
     * 后续建议
     */
    private List<String> nextSteps;

    /**
     * 摘要生成时间
     */
    private LocalDateTime generateTime;

    /**
     * 原始对话消息数量
     */
    private Integer originalMessageCount;

    /**
     * 摘要压缩比例
     */
    private Double compressionRatio;

    @Data
    @Accessors(chain = true)
    public static class CodeSnippet {
        /**
         * 编程语言
         */
        private String language;

        /**
         * 代码内容
         */
        private String code;

        /**
         * 代码说明
         */
        private String description;

        /**
         * 相关概念
         */
        private List<String> concepts;
    }
}
