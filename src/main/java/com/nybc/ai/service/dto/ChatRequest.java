package com.nybc.ai.service.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 类描述：统一聊天请求 DTO
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class ChatRequest {

    /**
     * 要使用的模型ID
     */
    private String model;

    /**
     * 聊天消息历史
     */
    private List<ChatMessage> messages;

    // 可以根据需要添加其他参数, 如 temperature, top_p 等
} 