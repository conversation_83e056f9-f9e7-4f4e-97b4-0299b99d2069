package com.nybc.ai.service.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 类描述：多模态文档分析结果
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class MultimodalDocumentAnalysis {

    /**
     * 文档基本信息
     */
    private DocumentInfo documentInfo;

    /**
     * 文本内容分析
     */
    private TextAnalysis textAnalysis;

    /**
     * 图片内容分析
     */
    private List<ImageAnalysis> imageAnalyses;

    /**
     * 表格分析结果
     */
    private List<TableAnalysis> tableAnalyses;

    /**
     * 代码片段分析
     */
    private List<CodeAnalysis> codeAnalyses;

    /**
     * 文档结构分析
     */
    private DocumentStructure documentStructure;

    /**
     * 质量评估结果
     */
    private QualityAssessment qualityAssessment;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 分析类型
     */
    private String analysisType;

    @Data
    @Accessors(chain = true)
    public static class DocumentInfo {
        private String fileName;
        private String fileType;
        private Long fileSize;
        private Integer pageCount;
        private String encoding;
        private String language;
        private Map<String, Object> metadata;
    }

    @Data
    @Accessors(chain = true)
    public static class TextAnalysis {
        private String fullText;
        private Integer wordCount;
        private Integer characterCount;
        private List<String> keyPhrases;
        private List<String> technicalTerms;
        private Map<String, Integer> wordFrequency;
        private Double readabilityScore;
        private String sentiment;
        private List<String> topics;
    }

    @Data
    @Accessors(chain = true)
    public static class ImageAnalysis {
        private String imageName;
        private String imageType;
        private String description;
        private List<String> detectedObjects;
        private String ocrText;
        private Map<String, Object> technicalContent;
        private Double relevanceScore;
    }

    @Data
    @Accessors(chain = true)
    public static class TableAnalysis {
        private String tableName;
        private Integer rowCount;
        private Integer columnCount;
        private List<String> headers;
        private String tableType;
        private Map<String, Object> statistics;
        private String summary;
        private List<List<String>> sampleData;
    }

    @Data
    @Accessors(chain = true)
    public static class CodeAnalysis {
        private String language;
        private String codeSnippet;
        private Integer lineCount;
        private List<String> functions;
        private List<String> classes;
        private List<String> imports;
        private Double complexity;
        private List<String> issues;
        private String purpose;
    }

    @Data
    @Accessors(chain = true)
    public static class QualityAssessment {
        private Double overallScore;
        private Map<String, Double> dimensionScores;
        private List<String> strengths;
        private List<String> weaknesses;
        private List<String> suggestions;
        private Boolean meetsStandards;
    }
}
