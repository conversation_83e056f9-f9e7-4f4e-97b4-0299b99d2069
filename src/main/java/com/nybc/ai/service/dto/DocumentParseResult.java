package com.nybc.ai.service.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 类描述：文档解析结果
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class DocumentParseResult {

    /**
     * 文档基本信息
     */
    private DocumentBasicInfo basicInfo;

    /**
     * 文本内容（保留格式）
     */
    private FormattedTextContent textContent;

    /**
     * 图片内容
     */
    private List<ImageContent> images;

    /**
     * 表格内容
     */
    private List<TableContent> tables;

    /**
     * 代码块内容
     */
    private List<CodeBlockContent> codeBlocks;

    /**
     * 页面布局信息
     */
    private List<PageLayout> pageLayouts;

    /**
     * 文档结构信息
     */
    private DocumentStructureInfo structureInfo;

    /**
     * 解析时间
     */
    private LocalDateTime parseTime;

    /**
     * 解析状态
     */
    private String parseStatus;

    /**
     * 错误信息（如果有）
     */
    private List<String> errors;

    @Data
    @Accessors(chain = true)
    public static class DocumentBasicInfo {
        private String fileName;
        private String fileType;
        private Long fileSize;
        private Integer pageCount;
        private String title;
        private String author;
        private String subject;
        private LocalDateTime creationDate;
        private LocalDateTime modificationDate;
        private Map<String, Object> customProperties;
    }

    @Data
    @Accessors(chain = true)
    public static class FormattedTextContent {
        private String plainText;
        private String htmlText;
        private String markdownText;
        private List<TextSegment> segments;
        private Map<String, Object> formatInfo;
    }

    @Data
    @Accessors(chain = true)
    public static class TextSegment {
        private String text;
        private String type; // TITLE, HEADING, PARAGRAPH, LIST_ITEM
        private Integer level;
        private Integer pageNumber;
        private Map<String, Object> formatting;
    }

    @Data
    @Accessors(chain = true)
    public static class ImageContent {
        private String imageName;
        private String imageType;
        private Integer width;
        private Integer height;
        private Integer pageNumber;
        private byte[] imageData;
        private String base64Data;
        private Map<String, Object> position;
        private String caption;
        private String altText;
    }

    @Data
    @Accessors(chain = true)
    public static class TableContent {
        private String tableName;
        private Integer rowCount;
        private Integer columnCount;
        private Integer pageNumber;
        private List<String> headers;
        private List<List<String>> rows;
        private Map<String, Object> styling;
        private String caption;
        private Map<String, Object> position;
    }

    @Data
    @Accessors(chain = true)
    public static class CodeBlockContent {
        private String code;
        private String language;
        private Integer lineCount;
        private Integer pageNumber;
        private Map<String, Object> position;
        private String syntax;
        private Boolean isInline;
    }

    @Data
    @Accessors(chain = true)
    public static class PageLayout {
        private Integer pageNumber;
        private Double width;
        private Double height;
        private List<LayoutElement> elements;
        private Map<String, Object> margins;
    }

    @Data
    @Accessors(chain = true)
    public static class LayoutElement {
        private String type; // TEXT, IMAGE, TABLE, SHAPE
        private Map<String, Object> bounds;
        private String content;
        private Map<String, Object> properties;
    }

    @Data
    @Accessors(chain = true)
    public static class DocumentStructureInfo {
        private List<OutlineItem> outline;
        private List<String> sections;
        private Boolean hasTableOfContents;
        private Boolean hasIndex;
        private Boolean hasReferences;
        private Integer headingLevels;
    }

    @Data
    @Accessors(chain = true)
    public static class OutlineItem {
        private String title;
        private Integer level;
        private Integer pageNumber;
        private List<OutlineItem> children;
    }
}
