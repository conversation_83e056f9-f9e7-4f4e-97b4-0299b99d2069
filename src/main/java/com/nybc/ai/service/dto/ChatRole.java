package com.nybc.ai.service.dto;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 类描述：聊天消息角色枚举
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Getter
public enum ChatRole {
    SYSTEM("system"),
    USER("user"),
    ASSISTANT("assistant");

    private final String value;

    ChatRole(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }
} 