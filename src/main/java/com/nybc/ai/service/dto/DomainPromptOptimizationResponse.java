package com.nybc.ai.service.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 类描述：领域特定Prompt优化响应
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class DomainPromptOptimizationResponse {

    /**
     * 优化后的Prompt内容
     */
    private String optimizedPrompt;

    /**
     * 原始Prompt
     */
    private String originalPrompt;

    /**
     * 优化策略说明
     */
    private String optimizationStrategy;

    /**
     * 应用的优化技术
     */
    private List<OptimizationTechnique> appliedTechniques;

    /**
     * 领域特定增强
     */
    private DomainEnhancements domainEnhancements;

    /**
     * 预期改进效果
     */
    private Map<String, Object> expectedImprovements;

    /**
     * 使用建议
     */
    private List<String> usageRecommendations;

    /**
     * 优化质量评分 (1-10)
     */
    private Double qualityScore;

    /**
     * 优化时间
     */
    private LocalDateTime optimizationTime;

    /**
     * 适用场景
     */
    private List<String> applicableScenarios;

    @Data
    @Accessors(chain = true)
    public static class OptimizationTechnique {
        /**
         * 技术名称
         */
        private String techniqueName;

        /**
         * 技术描述
         */
        private String description;

        /**
         * 应用原因
         */
        private String rationale;

        /**
         * 预期效果
         */
        private String expectedEffect;
    }

    @Data
    @Accessors(chain = true)
    public static class DomainEnhancements {
        /**
         * 添加的专业术语
         */
        private List<String> addedTerminology;

        /**
         * 增强的技术上下文
         */
        private List<String> enhancedContext;

        /**
         * 领域特定的约束条件
         */
        private List<String> domainConstraints;

        /**
         * 专业评估标准
         */
        private List<String> professionalCriteria;

        /**
         * 教学目标对齐
         */
        private List<String> pedagogicalAlignment;
    }
}
