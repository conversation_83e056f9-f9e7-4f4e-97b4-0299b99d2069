package com.nybc.ai.service.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 类描述：领域特定Prompt优化请求
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class DomainPromptOptimizationRequest {

    /**
     * 原始Prompt内容
     */
    private String originalPrompt;

    /**
     * 目标领域：COMPUTER_SCIENCE, SOFTWARE_ENGINEERING, DATA_STRUCTURE, ALGORITHM, DATABASE
     */
    private String targetDomain;

    /**
     * 应用场景：CODE_REVIEW, ASSIGNMENT_GRADING, STUDENT_TUTORING, DOCUMENT_ANALYSIS
     */
    private String scenario;

    /**
     * 学生水平：BEGINNER, INTERMEDIATE, ADVANCED
     */
    private String studentLevel;

    /**
     * 课程信息
     */
    private CourseInfo courseInfo;

    /**
     * 技术栈信息
     */
    private TechStackInfo techStackInfo;

    /**
     * 优化目标：ACCURACY, CLARITY, COMPREHENSIVENESS, EFFICIENCY
     */
    private String optimizationGoal;

    /**
     * 输出格式要求：JSON, MARKDOWN, PLAIN_TEXT, STRUCTURED
     */
    private String outputFormat;

    /**
     * 额外上下文信息
     */
    private Map<String, Object> additionalContext;

    /**
     * 是否需要中文输出
     */
    private Boolean requireChinese = true;

    @Data
    @Accessors(chain = true)
    public static class CourseInfo {
        /**
         * 课程名称
         */
        private String courseName;

        /**
         * 课程类型：THEORY, PRACTICE, PROJECT, DESIGN
         */
        private String courseType;

        /**
         * 学期阶段：BEGINNING, MIDDLE, FINAL
         */
        private String semesterStage;

        /**
         * 主要知识点
         */
        private String[] keyTopics;
    }

    @Data
    @Accessors(chain = true)
    public static class TechStackInfo {
        /**
         * 编程语言
         */
        private String[] programmingLanguages;

        /**
         * 框架和库
         */
        private String[] frameworks;

        /**
         * 开发工具
         */
        private String[] tools;

        /**
         * 数据库类型
         */
        private String[] databases;

        /**
         * 架构模式
         */
        private String[] architecturePatterns;
    }
}
