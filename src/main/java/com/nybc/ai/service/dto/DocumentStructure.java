package com.nybc.ai.service.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 类描述：文档结构信息
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class DocumentStructure {

    /**
     * 文档层次结构
     */
    private List<StructureNode> hierarchy;

    /**
     * 章节信息
     */
    private List<Section> sections;

    /**
     * 页面信息
     */
    private List<PageInfo> pages;

    /**
     * 内容类型统计
     */
    private ContentTypeStatistics contentStats;

    /**
     * 格式信息
     */
    private FormatInfo formatInfo;

    @Data
    @Accessors(chain = true)
    public static class StructureNode {
        private String type; // TITLE, HEADING, PARAGRAPH, LIST, TABLE, IMAGE, CODE
        private Integer level; // 层级深度
        private String content;
        private Integer pageNumber;
        private Map<String, Object> properties;
        private List<StructureNode> children;
    }

    @Data
    @Accessors(chain = true)
    public static class Section {
        private String title;
        private Integer level;
        private Integer startPage;
        private Integer endPage;
        private String content;
        private List<String> subsections;
        private Map<String, Object> metadata;
    }

    @Data
    @Accessors(chain = true)
    public static class PageInfo {
        private Integer pageNumber;
        private String content;
        private List<String> contentTypes; // TEXT, IMAGE, TABLE, CODE
        private Map<String, Object> layout;
        private Integer wordCount;
    }

    @Data
    @Accessors(chain = true)
    public static class ContentTypeStatistics {
        private Integer textBlocks;
        private Integer images;
        private Integer tables;
        private Integer codeBlocks;
        private Integer lists;
        private Integer headings;
        private Map<String, Integer> customTypes;
    }

    @Data
    @Accessors(chain = true)
    public static class FormatInfo {
        private String documentType;
        private List<String> fonts;
        private List<String> styles;
        private Map<String, Object> formatting;
        private Boolean hasTableOfContents;
        private Boolean hasReferences;
        private Boolean hasAppendices;
    }
}
