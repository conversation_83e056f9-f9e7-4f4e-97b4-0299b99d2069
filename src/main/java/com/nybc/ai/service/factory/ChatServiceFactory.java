package com.nybc.ai.service.factory;

import com.nybc.ai.service.ChatService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类描述：聊天服务工厂类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Component
public class ChatServiceFactory {

    private Map<String, ChatService> chatServiceMap;

    /**
     * 构造函数，自动注入所有ChatService的实现，并构建一个Map
     *
     * @param chatServices Spring自动注入的所有ChatService实例列表
     */
    @Resource
    public void setChatServices(List<ChatService> chatServices) {
        this.chatServiceMap = chatServices.stream()
                .collect(Collectors.toMap(ChatService::getProvider, Function.identity()));
    }

    /**
     * 根据提供商名称获取对应的聊天服务
     *
     * @param provider 提供商名称 (e.g., "dashscope", "ollama")
     * @return 聊天服务的Optional
     */
    public Optional<ChatService> getChatService(String provider) {
        return Optional.ofNullable(chatServiceMap.get(provider));
    }
} 