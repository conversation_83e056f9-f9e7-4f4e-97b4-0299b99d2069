package com.nybc.ai.service;

import com.nybc.ai.prompt.dto.PromptExecuteRequest;
import com.nybc.ai.prompt.dto.PromptExecuteResponse;
import com.nybc.ai.prompt.dto.PromptFeedbackRequest;

/**
 * 类描述：提供Prompt运行时执行与反馈能力的服务接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface PromptRuntimeService {

    /**
     * 执行一个Prompt模板
     *
     * @param request 执行请求
     * @return 包含执行日志ID和AI响应内容的响应体
     */
    PromptExecuteResponse execute(PromptExecuteRequest request);

    /**
     * 为某次执行提交人工反馈
     *
     * @param request 反馈请求
     */
    void submitFeedback(PromptFeedbackRequest request);
} 