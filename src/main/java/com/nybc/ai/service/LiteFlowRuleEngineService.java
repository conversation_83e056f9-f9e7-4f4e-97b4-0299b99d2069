package com.nybc.ai.service;

import com.nybc.ai.rules.context.EvaluationContext;
import com.nybc.ai.rules.dto.HomeworkEvaluationRequest;

/**
 * LiteFlow规则引擎服务接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
public interface LiteFlowRuleEngineService {

    /**
     * 执行作业评估规则链
     *
     * @param request 包含作业内容和元数据的评估请求
     * @return 包含所有评估结果的上下文
     * @throws Exception 执行过程中可能出现的异常
     */
    EvaluationContext execute(HomeworkEvaluationRequest request);
} 