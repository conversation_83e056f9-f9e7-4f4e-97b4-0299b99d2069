package com.nybc.ai.service;

import com.nybc.ai.prompt.dto.*;

import java.util.List;

/**
 * 类描述：Prompt管理相关的业务服务接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface PromptMgmtService {

    /**
     * 创建一个新的Prompt模板及其初始版本
     *
     * @param request 创建请求
     * @return 包含版本详情的模板响应对象
     */
    PromptTemplateDetailResponse createPromptTemplate(PromptTemplateCreateRequest request);

    /**
     * 为指定的Prompt模板创建一个新版本
     *
     * @param request 版本创建请求
     * @return 新创建的版本信息
     */
    PromptVersionResponse createPromptVersion(PromptVersionCreateRequest request);

    /**
     * 设置某个版本为模板的活动版本
     *
     * @param request 设置活动版本的请求
     */
    void setActiveVersion(SetActiveVersionRequest request);

    /**
     * 获取Prompt模板的详细信息，包括所有版本历史
     *
     * @param templateId 模板ID
     * @return 模板详细信息
     */
    PromptTemplateDetailResponse getPromptTemplateDetail(Long templateId);

    /**
     * 根据业务主键和租户ID获取模板信息
     *
     * @param promptKey 业务主键
     * @param tenantId  租户ID
     * @return 模板信息
     */
    PromptTemplateResponse getPromptTemplateByKey(String promptKey, Long tenantId);

    /**
     * 根据租户ID列出其下所有的Prompt模板
     *
     * @param tenantId 租户ID
     * @return 模板信息列表
     */
    List<PromptTemplateResponse> listPromptTemplatesByTenant(Long tenantId);

    /**
     * 逻辑删除一个Prompt模板及其所有版本
     *
     * @param templateId 模板ID
     * @param userId     操作人ID
     */
    void deletePromptTemplate(Long templateId, Long userId);
} 