package com.nybc.ai.service;

import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingResponse;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 类描述：统一的AI服务
 * 基于Spring AI框架，提供统一的AI调用接口
 * 支持多个AI提供商的无缝切换
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class UnifiedAiService {

    @Resource
    private ChatClient.Builder chatClientBuilder;

    @Resource
    private ChatModel chatModel;

    @Autowired(required = false)
    private EmbeddingModel embeddingModel;

    /**
     * 使用默认AI提供商进行聊天
     *
     * @param prompt 用户输入
     * @return AI响应
     */
    public String chat(String prompt) {
        return chatClientBuilder.build()
                .prompt(prompt)
                .call()
                .content();
    }

    /**
     * 使用默认AI提供商进行参数化聊天
     *
     * @param promptTemplate 提示模板
     * @param parameters     参数
     * @return AI响应
     */
    public String chat(String promptTemplate, Map<String, Object> parameters) {
        return chatClientBuilder.build()
                .prompt(promptTemplate)
                .call()
                .content();
    }

    /**
     * 使用指定AI提供商进行聊天（兼容性方法）
     *
     * @param provider 提供商名称（当前忽略，使用默认配置）
     * @param prompt   用户输入
     * @return AI响应
     */
    public String chatWithProvider(String provider, String prompt) {
        log.debug("使用提供商[{}]进行聊天（实际使用默认配置）", provider);
        return chat(prompt);
    }

    /**
     * 使用指定AI提供商进行参数化聊天（兼容性方法）
     *
     * @param provider       提供商名称（当前忽略，使用默认配置）
     * @param promptTemplate 提示模板
     * @param parameters     参数
     * @return AI响应
     */
    public String chatWithProvider(String provider, String promptTemplate, Map<String, Object> parameters) {
        log.debug("使用提供商[{}]进行参数化聊天（实际使用默认配置）", provider);
        return chat(promptTemplate, parameters);
    }

    /**
     * 流式聊天
     *
     * @param prompt 用户输入
     * @return 流式响应
     */
    public Flux<String> streamChat(String prompt) {
        return chatClientBuilder.build()
                .prompt(prompt)
                .stream()
                .content();
    }

    /**
     * 使用指定提供商进行流式聊天（兼容性方法）
     *
     * @param provider 提供商名称（当前忽略，使用默认配置）
     * @param prompt   用户输入
     * @return 流式响应
     */
    public Flux<String> streamChatWithProvider(String provider, String prompt) {
        log.debug("使用提供商[{}]进行流式聊天（实际使用默认配置）", provider);
        return streamChat(prompt);
    }

    /**
     * 生成文本嵌入向量
     *
     * @param text 要嵌入的文本
     * @return 嵌入向量
     */
    public List<Double> generateEmbedding(String text) {
        if (embeddingModel == null) {
            throw new UnsupportedOperationException("嵌入模型未配置");
        }

        try {
            log.debug("生成文本嵌入，文本长度: {}", text.length());

            EmbeddingResponse response = embeddingModel.embedForResponse(List.of(text));
            float[] embeddingArray = response.getResults().get(0).getOutput();
            List<Double> embedding = new ArrayList<>();
            for (float f : embeddingArray) {
                embedding.add((double) f);
            }

            log.debug("嵌入向量生成成功，维度: {}", embedding.size());
            return embedding;

        } catch (Exception e) {
            log.error("生成嵌入向量失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成嵌入向量失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量生成文本嵌入向量
     *
     * @param texts 要嵌入的文本列表
     * @return 嵌入向量列表
     */
    public List<List<Double>> generateEmbeddings(List<String> texts) {
        if (embeddingModel == null) {
            throw new UnsupportedOperationException("嵌入模型未配置");
        }

        try {
            log.debug("批量生成文本嵌入，文本数量: {}", texts.size());

            EmbeddingResponse response = embeddingModel.embedForResponse(texts);
            List<List<Double>> embeddings = response.getResults().stream()
                    .map(result -> {
                        float[] embeddingArray = result.getOutput();
                        return Arrays.stream(embeddingArray)
                                .boxed()
                                .map(Float::doubleValue)
                                .collect(Collectors.toList());
                    })
                    .toList();

            log.debug("批量嵌入向量生成成功");
            return embeddings;

        } catch (Exception e) {
            log.error("批量生成嵌入向量失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量生成嵌入向量失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取可用的AI提供商列表（兼容性方法）
     *
     * @return 可用提供商列表
     */
    public List<String> getAvailableProviders() {
        return List.of("dashscope");
    }

    /**
     * 检查指定提供商是否可用（兼容性方法）
     *
     * @param provider 提供商名称
     * @return 是否可用
     */
    public boolean isProviderAvailable(String provider) {
        return "dashscope".equalsIgnoreCase(provider) && chatModel != null;
    }

    /**
     * 获取系统状态信息
     *
     * @return 系统状态
     */
    public Map<String, Object> getSystemStatus() {
        return Map.of(
                "primaryProvider", "dashscope",
                "availableProviders", getAvailableProviders(),
                "embeddingAvailable", embeddingModel != null,
                "framework", "Spring AI 1.0.0",
                "model", "deepseek-r1"
                     );
    }

}
