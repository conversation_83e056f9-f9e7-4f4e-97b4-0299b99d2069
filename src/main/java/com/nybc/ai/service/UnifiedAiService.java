package com.nybc.ai.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 类描述：统一的AI服务
 * 基于Spring AI框架，提供统一的AI调用接口
 * 支持多个AI提供商的无缝切换
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class UnifiedAiService {

    @Resource
    private ChatClient.Builder chatClientBuilder;

    @Resource
    private ChatModel primaryChatModel;

    @Resource(required = false)
    private EmbeddingModel primaryEmbeddingModel;

    // 特定提供商的ChatModel
    @Resource(required = false)
    @Qualifier("deepseekChatModel")
    private ChatModel deepseekChatModel;

    @Resource(required = false)
    @Qualifier("dashscopeChatModel")
    private ChatModel dashscopeChatModel;

    @Resource(required = false)
    @Qualifier("openaiChatModel")
    private ChatModel openaiChatModel;

    @Resource(required = false)
    @Qualifier("ollamaChatModel")
    private ChatModel ollamaChatModel;

    /**
     * 使用默认AI提供商进行聊天
     *
     * @param prompt 用户输入
     * @return AI响应
     */
    public String chat(String prompt) {
        return chatClientBuilder.build()
                .prompt(prompt)
                .call()
                .content();
    }

    /**
     * 使用默认AI提供商进行参数化聊天
     *
     * @param promptTemplate 提示模板
     * @param parameters     参数
     * @return AI响应
     */
    public String chat(String promptTemplate, Map<String, Object> parameters) {
        return chatClientBuilder.build()
                .prompt(promptTemplate)
                .params(parameters)
                .call()
                .content();
    }

    /**
     * 使用指定AI提供商进行聊天
     *
     * @param provider 提供商名称：deepseek, dashscope, openai, ollama
     * @param prompt   用户输入
     * @return AI响应
     */
    public String chatWithProvider(String provider, String prompt) {
        ChatModel chatModel = getProviderChatModel(provider);
        ChatResponse response = chatModel.call(new Prompt(prompt));
        return response.getResult().getOutput().getContent();
    }

    /**
     * 使用指定AI提供商进行参数化聊天
     *
     * @param provider       提供商名称
     * @param promptTemplate 提示模板
     * @param parameters     参数
     * @return AI响应
     */
    public String chatWithProvider(String provider, String promptTemplate, Map<String, Object> parameters) {
        ChatModel chatModel = getProviderChatModel(provider);
        PromptTemplate template = new PromptTemplate(promptTemplate);
        Prompt prompt = template.create(parameters);
        ChatResponse response = chatModel.call(prompt);
        return response.getResult().getOutput().getContent();
    }

    /**
     * 流式聊天
     *
     * @param prompt 用户输入
     * @return 流式响应
     */
    public Flux<String> streamChat(String prompt) {
        return chatClientBuilder.build()
                .prompt(prompt)
                .stream()
                .content();
    }

    /**
     * 使用指定提供商进行流式聊天
     *
     * @param provider 提供商名称
     * @param prompt   用户输入
     * @return 流式响应
     */
    public Flux<String> streamChatWithProvider(String provider, String prompt) {
        ChatModel chatModel = getProviderChatModel(provider);
        return chatModel.stream(new Prompt(prompt))
                .map(response -> response.getResult().getOutput().getContent());
    }

    /**
     * 生成文本嵌入向量
     *
     * @param text 要嵌入的文本
     * @return 嵌入向量
     */
    public List<Double> generateEmbedding(String text) {
        if (primaryEmbeddingModel == null) {
            throw new UnsupportedOperationException("嵌入模型未配置");
        }

        try {
            log.debug("生成文本嵌入，文本长度: {}", text.length());
            
            EmbeddingResponse response = primaryEmbeddingModel.embedForResponse(List.of(text));
            List<Double> embedding = response.getResults().get(0).getOutput();
            
            log.debug("嵌入向量生成成功，维度: {}", embedding.size());
            return embedding;

        } catch (Exception e) {
            log.error("生成嵌入向量失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成嵌入向量失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量生成文本嵌入向量
     *
     * @param texts 要嵌入的文本列表
     * @return 嵌入向量列表
     */
    public List<List<Double>> generateEmbeddings(List<String> texts) {
        if (primaryEmbeddingModel == null) {
            throw new UnsupportedOperationException("嵌入模型未配置");
        }

        try {
            log.debug("批量生成文本嵌入，文本数量: {}", texts.size());
            
            EmbeddingResponse response = primaryEmbeddingModel.embedForResponse(texts);
            List<List<Double>> embeddings = response.getResults().stream()
                    .map(result -> result.getOutput())
                    .toList();
            
            log.debug("批量嵌入向量生成成功");
            return embeddings;

        } catch (Exception e) {
            log.error("批量生成嵌入向量失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量生成嵌入向量失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取可用的AI提供商列表
     *
     * @return 可用提供商列表
     */
    public List<String> getAvailableProviders() {
        return List.of("deepseek", "dashscope", "openai", "ollama").stream()
                .filter(this::isProviderAvailable)
                .toList();
    }

    /**
     * 检查指定提供商是否可用
     *
     * @param provider 提供商名称
     * @return 是否可用
     */
    public boolean isProviderAvailable(String provider) {
        try {
            ChatModel chatModel = getProviderChatModel(provider);
            return chatModel != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取指定提供商的ChatModel
     *
     * @param provider 提供商名称
     * @return ChatModel
     */
    private ChatModel getProviderChatModel(String provider) {
        return switch (provider.toLowerCase()) {
            case "deepseek" -> Optional.ofNullable(deepseekChatModel)
                    .orElseThrow(() -> new IllegalArgumentException("DeepSeek未配置或不可用"));
            case "dashscope" -> Optional.ofNullable(dashscopeChatModel)
                    .orElseThrow(() -> new IllegalArgumentException("DashScope未配置或不可用"));
            case "openai" -> Optional.ofNullable(openaiChatModel)
                    .orElseThrow(() -> new IllegalArgumentException("OpenAI未配置或不可用"));
            case "ollama" -> Optional.ofNullable(ollamaChatModel)
                    .orElseThrow(() -> new IllegalArgumentException("Ollama未配置或不可用"));
            default -> throw new IllegalArgumentException("不支持的AI提供商: " + provider);
        };
    }

    /**
     * 获取系统状态信息
     *
     * @return 系统状态
     */
    public Map<String, Object> getSystemStatus() {
        return Map.of(
                "primaryProvider", "auto-selected",
                "availableProviders", getAvailableProviders(),
                "embeddingAvailable", primaryEmbeddingModel != null,
                "framework", "Spring AI 1.0.0"
        );
    }
}
