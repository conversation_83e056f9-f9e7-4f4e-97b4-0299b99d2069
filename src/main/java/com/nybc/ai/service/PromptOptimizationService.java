package com.nybc.ai.service;

import com.nybc.ai.prompt.dto.PromptVersionResponse;
import com.nybc.ai.prompt.dto.PromptOptimizeWithFeedbackRequest;

/**
 * 类描述：提供Prompt优化能力的服务接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface PromptOptimizationService {

    /**
     * 基于用户的原始想法，调用AI模型进行优化，并创建一个新的Prompt版本
     *
     * @param templateId    所属的模板ID
     * @param rawIdeaText   用户的原始想法
     * @param operatorId    操作人ID
     * @param providerName  指定使用哪个AI模型进行优化 (e.g., "deepseek")
     * @return 新创建的版本信息
     */
    PromptVersionResponse optimizeAndCreateVersion(Long templateId, String rawIdeaText, Long operatorId, String providerName);
    
    /**
     * 基于已有的人工反馈，调用AI模型进行优化，并创建一个新的Prompt版本
     *
     * @param request 包含执行日志ID和操作人等信息的请求
     * @return 新创建的版本信息
     */
    PromptVersionResponse optimizeWithFeedback(PromptOptimizeWithFeedbackRequest request);
} 