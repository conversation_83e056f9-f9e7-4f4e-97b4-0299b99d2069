package com.nybc.ai.id;

import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
public class IdTimeService {
    /**
     * 时间戳格式化器 (年、月、日、时、分、秒)
     * 强制使用UTC或特定业务时区以保证跨服务器一致性。
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyMMddHHmmssSSS");
    /**
     * 固定的时区，用于时间格式化，避免系统默认时区带来的不一致。
     * 建议使用 "UTC" 或业务标准时区如 "Asia/Shanghai"。
     */
    private static final ZoneId FORMATTING_ZONE_ID = ZoneId.of("Asia/Shanghai");

    /**
     * 序列号最大值 (99意味着两位十进制数 00-99)。
     */
    private static final long MAX_SEQUENCE = 99L;
    /**
     * 序列号占用的位数 (用于格式化)。
     */
    private static final int SEQUENCE_BITS_LENGTH = 2;

    /**
     * 随机数最大值 (9意味着一位十进制数 0-9)。
     */
    private static final int MAX_RANDOM = 9;
    /**
     * 随机数占用的位数 (用于格式化)。
     */
    private static final int RANDOM_BITS_LENGTH = 1;

    private final AtomicLong sequence = new AtomicLong(0L);
    private volatile long lastTimestamp = -1L;

    private IdTimeService() {
        // 私有构造，防止外部实例化
    }

    /**
     * 获取 IdTimeService 的单例。
     *
     * @return IdTimeService 实例
     */
    public static IdTimeService getInstance() {
        return IdHandle.INSTANCE;
    }

    /**
     * 获取一个全局唯一的、基于时间的ID。
     *
     * @return 生成的唯一ID
     */
    public static Long getId() {
        return getInstance().nextId();
    }

    /**
     * 生成下一个唯一的长整型ID。
     * 此方法是线程安全的。
     *
     * @return 生成的唯一ID
     */
    public synchronized long nextId() {
        long currentTimestamp = getCurrentTimestamp();

        if (currentTimestamp < lastTimestamp) {
            log.warn("时钟发生回拨。正在等待时间追赶... 上次时间戳: {}, 当前时间戳: {}", lastTimestamp, currentTimestamp);
            currentTimestamp = waitUntilNextMillis(lastTimestamp);
        }

        String sequencePart;
        if (currentTimestamp == lastTimestamp) {
            long currentSeq = sequence.incrementAndGet();
            if (currentSeq > MAX_SEQUENCE) {
                // 当前毫秒内序列已用完，等待到下一毫秒
                currentTimestamp = waitUntilNextMillis(lastTimestamp);
                sequence.set(1L); // 重置序列为1 (或0，如果希望从0开始)
                currentSeq = 1L;
            }
            sequencePart = String.format("%0" + SEQUENCE_BITS_LENGTH + "d", currentSeq);
        } else {
            // 新的毫秒，重置序列为1 (或0)
            sequence.set(1L);
            sequencePart = String.format("%0" + SEQUENCE_BITS_LENGTH + "d", 1L);
        }

        lastTimestamp = currentTimestamp;

        // 生成随机数部分
        int randomNumber = ThreadLocalRandom.current().nextInt(0, MAX_RANDOM + 1);
        String randomPart = String.format("%0" + RANDOM_BITS_LENGTH + "d", randomNumber);

        // 时间部分格式化
        String timestampPart = formatTimestamp(currentTimestamp);

        // 拼接ID字符串
        String idString = timestampPart + sequencePart + randomPart;

        try {
            return Long.parseLong(idString);
        } catch (NumberFormatException e) {
            // 理论上拼接的都是数字，不太可能发生，但作为防御
            log.error("生成的ID字符串 '{}' 无法解析为long类型。", idString, e);
            // 可以选择抛出异常，或者返回一个错误标记值，或者重试
            // 这里简单重试一次（但不推荐无限重试）
            return fallbackIdGeneration(); // 提供一个备用方案或明确抛出异常
        }
    }

    /**
     * 获取当前时间戳（毫秒）。
     */
    private long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳。
     *
     * @param lastTs 上一个时间戳
     * @return 下一个毫秒的时间戳
     */
    private long waitUntilNextMillis(final long lastTs) {
        long timestamp = getCurrentTimestamp();
        while (timestamp <= lastTs) {
            timestamp = getCurrentTimestamp();
        }
        return timestamp;
    }

    /**
     * 格式化给定的时间戳为 "yyMMddHHmmss" 字符串。
     *
     * @param timestamp 要格式化的时间戳 (毫秒)
     * @return 格式化后的时间字符串
     */
    private String formatTimestamp(long timestamp) {
        return DATE_TIME_FORMATTER.format(Instant.ofEpochMilli(timestamp).atZone(FORMATTING_ZONE_ID));
    }

    /**
     * 当主ID生成逻辑因意外（如NumberFormatException）失败时的备用ID生成方案。
     * 可以考虑使用UUID或其他简单唯一ID，或记录更详细错误后抛出。
     */
    private long fallbackIdGeneration() {
        // 这是一个非常简化的备用方案，实际中可能需要更健壮的机制
        log.warn("主ID生成失败，启用备用ID生成方案。");
        return Instant.now().toEpochMilli() + ThreadLocalRandom.current().nextInt(1000);
    }

    /**
     * 静态内部类实现单例。
     */
    private static class IdHandle {
        private static final IdTimeService INSTANCE = new IdTimeService();
    }

    public static void main(String[] args) {
        Set<Long> ids = new HashSet<>();
        int conflicts = 0;
        int count = 100000;
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < count; i++) {
            long id = IdTimeService.getId();
            if (!ids.add(id)) {
                conflicts++;
                System.err.println("发生冲突: " + id);
            }
            if (i < 100 || i > count - 100) {
                System.out.println(id);
            }
        }
        long endTime = System.currentTimeMillis();
        System.out.println("--------------------------------------");
        System.out.println("生成 " + count + " 个ID耗时: " + (endTime - startTime) + " ms");
        System.out.println("冲突数量: " + conflicts);
        System.out.println("示例ID (15位): 241008100000011 (yyMMddHHmmss + SS + R)");
        // 验证长度
        long exampleId = 241008100000011L;
        System.out.println("示例ID长度: " + String.valueOf(exampleId).length());
    }
}
