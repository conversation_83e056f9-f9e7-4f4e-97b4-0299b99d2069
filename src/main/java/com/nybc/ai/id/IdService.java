package com.nybc.common.tool.id;

import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 类描述：主键唯一ID生成器
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
public class IdService {
    // 起始的时间戳 (可以自定义，例如项目上线时间)
    // 2024-01-01 00:00:00 UTC
    private static final long EPOCH = 1704067200000L;

    // --- 位数分配 ---
    // 机器 id 所占的位数
    private static final long WORKER_ID_BITS = 5L;
    // 数据标识 id 所占的位数 (如果不需要可以设为0，并将位数给 workerId 或 sequence)
    private static final long DATACENTER_ID_BITS = 5L;
    // 支持的最大机器 id，结果是 31 (这个移位算法可以很快的计算出几位二进制数所能表示的最大十进制数)
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);
    // 支持的最大数据标识 id，结果是 31
    private static final long MAX_DATACENTER_ID = ~(-1L << DATACENTER_ID_BITS);
    // 序列在 id 中占的位数
    private static final long SEQUENCE_BITS = 12L;

    // --- 移位计算 ---
    // 机器 ID 向左移 12 位
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;
    // 数据标识 id 向左移 17 位(12+5)
    private static final long DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;
    // 时间截向左移 22 位(5+5+12)
    private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATACENTER_ID_BITS;

    // 生成序列的掩码，这里为 4095 (0b111111111111=0xfff=4095)
    private static final long SEQUENCE_MASK = ~(-1L << SEQUENCE_BITS);

    // --- 实例变量 ---
    // 工作机器 ID (0~31)
    private final long workerId;
    // 数据中心 ID (0~31)
    private final long datacenterId;

    // 毫秒内序列 (0~4095) - 使用 AtomicLong 保证线程安全
    private final AtomicLong sequence = new AtomicLong(0L);
    // 上次生成 ID 的时间截 - 使用 volatile 保证可见性
    private volatile long lastTimestamp = -1L;


    /**
     * 构造函数
     * @param workerId 工作 ID (0~31)
     * @param datacenterId 数据中心 ID (0~31)
     */
    public IdService(long workerId, long datacenterId) {
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException(String.format("Worker ID can't be greater than %d or less than 0", MAX_WORKER_ID));
        }
        if (datacenterId > MAX_DATACENTER_ID || datacenterId < 0) {
            throw new IllegalArgumentException(String.format("Datacenter ID can't be greater than %d or less than 0", MAX_DATACENTER_ID));
        }
        this.workerId = workerId;
        this.datacenterId = datacenterId;
        log.info("IdService initialized with Worker ID: {}, Datacenter ID: {}", workerId, datacenterId);
    }

    /**
     * 获取下一个 ID (核心方法)
     * 使用 synchronized 保证对 lastTimestamp 和 sequence 操作的原子性，
     * 但锁的粒度非常小，只在需要更新时间戳或序列时加锁，性能远高于原方法。
     * @return SnowflakeId
     */
    public synchronized long nextId() {
        long timestamp = timeGen();

        // 如果当前时间小于上一次 ID 生成的时间戳，说明系统时钟回退过，抛出异常
        if (timestamp < lastTimestamp) {
            log.error("Clock moved backwards. Refusing to generate id for {} milliseconds. Last timestamp: {}, Current timestamp: {}",
                      lastTimestamp - timestamp, lastTimestamp, timestamp);
            // 更稳妥的做法是抛异常，而不是等待，因为等待可能很久或永远不结束
            throw new RuntimeException(String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
            // 如果非要等待，至少设置一个最大等待时间
            // timestamp = tilNextMillis(lastTimestamp);
        }

        long currentSequence;
        // 如果是同一时间生成的，则进行毫秒内序列
        if (lastTimestamp == timestamp) {
            currentSequence = sequence.incrementAndGet() & SEQUENCE_MASK;
            // 毫秒内序列溢出 (达到 4096)
            if (currentSequence == 0) {
                // 阻塞到下一个毫秒,获得新的时间戳
                timestamp = tilNextMillis(lastTimestamp);
                // 新的毫秒，序列从 0 开始
                sequence.set(0L); // 重置序列计数器
                currentSequence = sequence.get(); // 获取当前序列值 (为 0)
            }
        } else {
            // 时间戳改变，毫秒内序列重置
            sequence.set(0L);
            currentSequence = sequence.get(); // 获取当前序列值 (为 0)
        }

        // 更新最后时间戳
        lastTimestamp = timestamp;

        // 移位并通过或运算拼到一起组成 64 位的 ID
        return ((timestamp - EPOCH) << TIMESTAMP_LEFT_SHIFT) // 时间戳部分
                | (datacenterId << DATACENTER_ID_SHIFT)      // 数据中心部分
                | (workerId << WORKER_ID_SHIFT)          // 机器标识部分
                | currentSequence;                         // 序列号部分
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     * @param lastTimestamp 上次生成 ID 的时间截
     * @return 当前时间戳
     */
    protected long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            // অল্প সময়ের জন্য থ্রেডকে বিরতি দিন (যদি busy-wait এর পরিবর্তে ব্যবহার করতে চান)
            // try { Thread.sleep(0, 1000); } catch (InterruptedException e) { Thread.currentThread().interrupt(); }
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 返回以毫秒为单位的当前时间
     * @return 当前时间(毫秒)
     */
    protected long timeGen() {
        return System.currentTimeMillis();
    }

    // --- 单例模式和 Worker ID/Datacenter ID 获取 ---

    private static IdService instance;

    /**
     * 获取单例实例 (需要确保 workerId 和 datacenterId 被正确初始化)
     * @return 单例
     */
    public static synchronized IdService getInstance() {
        if (instance == null) {
            // 在实际应用中， workerId 和 datacenterId 需要通过配置、环境变量、
            // 服务发现(如 Zookeeper, etcd) 或其他机制获取，确保在分布式环境中唯一。
            long workerId = 1;
            long datacenterId = getDataCenterId();
            instance = new IdService(workerId, datacenterId);
        }
        return instance;
    }

    /**
     * 获取唯一 ID 的静态方法
     * @return 唯一ID
     */
    public static Long getId() {
        return getInstance().nextId();
    }

    // --- 示例: 获取 Worker ID 和 Datacenter ID (需要根据实际部署环境调整) ---
    // 这是一个简单的示例，不保证在所有环境中唯一，生产环境需要更可靠的机制

    private static long getWorkerId() {
        try {
            // 尝试从系统属性或环境变量获取
            String workerIdProp = System.getProperty("snowflake.workerId");
            if (workerIdProp != null) {
                return Long.parseLong(workerIdProp) % (MAX_WORKER_ID + 1);
            }
            String workerIdEnv = System.getenv("SNOWFLAKE_WORKER_ID");
            if (workerIdEnv != null) {
                return Long.parseLong(workerIdEnv) % (MAX_WORKER_ID + 1);
            }

            // 尝试基于 MAC 地址生成 (不完全可靠，可能有冲突或获取不到)
            InetAddress ip = InetAddress.getLocalHost();
            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            if (network == null) {
                return ThreadLocalRandom.current().nextLong(0, MAX_WORKER_ID + 1); // Fallback to random
            }
            byte[] mac = network.getHardwareAddress();
            if (mac == null) {
                return ThreadLocalRandom.current().nextLong(0, MAX_WORKER_ID + 1); // Fallback to random
            }
            long id = ((0x000000FF & (long) mac[mac.length - 1])
                    | (0x0000FF00 & (((long) mac[mac.length - 2]) << 8))) >> 6; // 取 MAC 地址后两字节的部分信息
            return id % (MAX_WORKER_ID + 1);
        } catch (Exception e) {
            log.warn("Failed to get worker ID based on MAC/Env/Prop, using random. Error: {}", e.getMessage());
            // Fallback to random if any error occurs
            return ThreadLocalRandom.current().nextLong(0, MAX_WORKER_ID + 1);
        }
    }

    private static long getDataCenterId() {
        // 实现逻辑类似于 getWorkerId，可以基于配置、环境、机房信息等
        // 这里简单地使用 PID 对最大值取模作为示例 (非常不推荐用于生产)
        try {
            String processName = ManagementFactory.getRuntimeMXBean().getName();
            if (processName != null && processName.contains("@")) {
                long pid = Long.parseLong(processName.substring(0, processName.indexOf('@')));
                return pid % (MAX_DATACENTER_ID + 1);
            }
        } catch (Exception e) {
            log.warn("Failed to get datacenter ID based on PID, using random. Error: {}", e.getMessage());
        }
        // Fallback to random
        return ThreadLocalRandom.current().nextLong(0, MAX_DATACENTER_ID + 1);
    }

    // --- main 方法测试 ---
    public static void main(String[] args) {
        // 测试单例获取ID
        System.out.println("Using Singleton:");
        for (int i = 0; i < 10; i++) {
            System.out.println(IdService.getId());
        }

        System.out.println("\nUsing Specific Instance (Worker 1, Datacenter 1):");
        // 测试特定实例
        IdService idWorker = new IdService(1, 1);
        for (int i = 0; i < 10; i++) {
            long id = idWorker.nextId();
            System.out.println(id);
        }

        // 模拟高并发测试 (简单示例)
        System.out.println("\nConcurrency Test:");
        int threadCount = 10;
        int idsPerThread = 1000;
        java.util.Set<Long> generatedIds = java.util.Collections.synchronizedSet(new java.util.HashSet<>());
        Thread[] threads = new Thread[threadCount];
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < idsPerThread; j++) {
                    long id = IdService.getId(); // 使用单例获取
                    if (!generatedIds.add(id)) {
                        System.err.println("Collision detected: " + id); // 理论上不应发生
                    }
                }
            });
            threads[i].start();
        }

        try {
            for (Thread t : threads) {
                t.join();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        long endTime = System.currentTimeMillis();
        System.out.println("Generated " + generatedIds.size() + " unique IDs in " + (endTime - startTime) + " ms.");
        // 检查是否有冲突
        if (generatedIds.size() != threadCount * idsPerThread) {
            System.err.println("Error: Number of generated IDs does not match expected count. Possible collisions or errors.");
        } else {
            System.out.println("No collisions detected.");
        }
    }
}