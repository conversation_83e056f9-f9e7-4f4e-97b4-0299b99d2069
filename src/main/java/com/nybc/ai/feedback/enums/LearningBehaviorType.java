package com.nybc.ai.feedback.enums;

/**
 * 类描述：学习行为类型枚举
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public enum LearningBehaviorType {

    /**
     * 提交作业
     */
    SUBMIT("提交作业"),

    /**
     * 查看反馈
     */
    VIEW_FEEDBACK("查看反馈"),

    /**
     * 提出问题
     */
    ASK_QUESTION("提出问题"),

    /**
     * 修订作业
     */
    REVISE("修订作业"),

    /**
     * 查看学习建议
     */
    VIEW_SUGGESTION("查看学习建议"),

    /**
     * 应用学习建议
     */
    APPLY_SUGGESTION("应用学习建议"),

    /**
     * 访问学习资源
     */
    ACCESS_RESOURCE("访问学习资源"),

    /**
     * 参与讨论
     */
    PARTICIPATE_DISCUSSION("参与讨论");

    private final String description;

    LearningBehaviorType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
