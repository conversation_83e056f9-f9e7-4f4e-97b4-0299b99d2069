package com.nybc.ai.feedback.enums;

/**
 * 类描述：学习建议类型枚举
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public enum SuggestionType {

    /**
     * 知识缺口建议
     */
    KNOWLEDGE_GAP("知识缺口", "针对学生薄弱知识点的补强建议"),

    /**
     * 学习方法建议
     */
    STUDY_METHOD("学习方法", "改进学习方法和策略的建议"),

    /**
     * 资源推荐建议
     */
    RESOURCE_RECOMMEND("资源推荐", "推荐相关学习资源和材料"),

    /**
     * 练习强化建议
     */
    PRACTICE_ENHANCEMENT("练习强化", "针对特定技能的练习建议"),

    /**
     * 时间管理建议
     */
    TIME_MANAGEMENT("时间管理", "学习时间安排和管理建议"),

    /**
     * 复习策略建议
     */
    REVIEW_STRATEGY("复习策略", "复习计划和策略建议");

    private final String displayName;
    private final String description;

    SuggestionType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }
}
