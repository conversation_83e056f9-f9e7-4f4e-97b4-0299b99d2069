package com.nybc.ai.feedback.enums;

/**
 * 类描述：学习风格枚举
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public enum LearningStyle {

    /**
     * 视觉型学习者
     */
    VISUAL("视觉型", "通过图表、图像、颜色等视觉元素学习效果更好"),

    /**
     * 听觉型学习者
     */
    AUDITORY("听觉型", "通过听讲、讨论、音频等方式学习效果更好"),

    /**
     * 动觉型学习者
     */
    KINESTHETIC("动觉型", "通过实践操作、动手实验等方式学习效果更好"),

    /**
     * 阅读型学习者
     */
    READING("阅读型", "通过阅读文字、笔记等方式学习效果更好");

    private final String displayName;
    private final String description;

    LearningStyle(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }
}
