package com.nybc.ai.feedback.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类描述：学生学习行为数据实体类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class StudentLearningBehavior implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 作业ID
     */
    private Long assignmentId;

    /**
     * 行为类型：SUBMIT, VIEW_FEEDBACK, ASK_QUESTION, REVISE
     */
    private String behaviorType;

    /**
     * 行为详细数据(JSON格式)
     */
    private String behaviorData;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
