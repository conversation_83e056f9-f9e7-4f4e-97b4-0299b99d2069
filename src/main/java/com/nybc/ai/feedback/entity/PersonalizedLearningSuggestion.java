package com.nybc.ai.feedback.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类描述：个性化学习建议实体类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class PersonalizedLearningSuggestion implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 建议类型：KNOWLEDGE_GAP, STUDY_METHOD, RESOURCE_RECOMMEND
     */
    private String suggestionType;

    /**
     * 建议标题
     */
    private String title;

    /**
     * 建议内容
     */
    private String content;

    /**
     * 优先级：1-5，5最高
     */
    private Integer priorityLevel;

    /**
     * 目标知识点(JSON格式)
     */
    private String targetKnowledgePoints;

    /**
     * 推荐学习资源(JSON格式)
     */
    private String suggestedResources;

    /**
     * 是否已读
     */
    private Boolean isRead;

    /**
     * 是否已应用
     */
    private Boolean isApplied;

    /**
     * 建议有效性评分
     */
    private Double effectivenessScore;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 建议过期时间
     */
    private LocalDateTime expireTime;
}
