package com.nybc.ai.feedback.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类描述：Prompt性能分析实体类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class PromptPerformanceAnalytics implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * Prompt模板ID
     */
    private Long templateId;

    /**
     * Prompt版本ID
     */
    private Long versionId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 分析周期：DAILY, WEEKLY, MONTHLY
     */
    private String analysisPeriod;

    /**
     * 总执行次数
     */
    private Integer totalExecutions;

    /**
     * 成功率
     */
    private Double successRate;

    /**
     * 平均执行时间(ms)
     */
    private Double avgExecutionTime;

    /**
     * 平均反馈评分
     */
    private Double avgFeedbackScore;

    /**
     * 正面反馈率
     */
    private Double positiveFeedbackRate;

    /**
     * 常见失败模式(JSON格式)
     */
    private String commonFailurePatterns;

    /**
     * 优化建议(JSON格式)
     */
    private String optimizationSuggestions;

    /**
     * 性能趋势数据(JSON格式)
     */
    private String performanceTrend;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;
}
