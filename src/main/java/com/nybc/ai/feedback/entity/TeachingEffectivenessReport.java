package com.nybc.ai.feedback.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类描述：教学效果分析报告实体类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class TeachingEffectivenessReport implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 教师ID
     */
    private Long teacherId;

    /**
     * 作业ID
     */
    private Long assignmentId;

    /**
     * 报告类型：ASSIGNMENT, COURSE, SEMESTER
     */
    private String reportType;

    /**
     * 报告周期：WEEKLY, MONTHLY, SEMESTER
     */
    private String reportPeriod;

    /**
     * 分析数据(JSON格式)
     */
    private String analysisData;

    /**
     * 关键洞察(JSON格式)
     */
    private String keyInsights;

    /**
     * 改进建议(JSON格式)
     */
    private String improvementSuggestions;

    /**
     * 性能指标(JSON格式)
     */
    private String performanceMetrics;

    /**
     * 学生反馈汇总(JSON格式)
     */
    private String studentFeedbackSummary;

    /**
     * 生成时间
     */
    private LocalDateTime generateTime;

    /**
     * 创建用户ID
     */
    private Long createUser;
}
