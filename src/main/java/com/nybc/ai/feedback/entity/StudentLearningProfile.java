package com.nybc.ai.feedback.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 类描述：学生个性化学习档案实体类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
public class StudentLearningProfile implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 学习风格：VISUAL, AUDITORY, KINESTHETIC, READING
     */
    private String learningStyle;

    /**
     * 知识水平：BEGINNER, INTERMEDIATE, ADVANCED
     */
    private String knowledgeLevel;

    /**
     * 薄弱知识点列表(JSON格式)
     */
    private String weakAreas;

    /**
     * 擅长知识点列表(JSON格式)
     */
    private String strongAreas;

    /**
     * 学习偏好设置(JSON格式)
     */
    private String learningPreferences;

    /**
     * 成绩趋势数据(JSON格式)
     */
    private String performanceTrends;

    /**
     * 最后分析时间
     */
    private LocalDateTime lastAnalysisTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
