package com.nybc.ai.feedback.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.feedback.dto.PersonalizedSuggestionResponse;
import com.nybc.ai.feedback.dto.TeachingEffectivenessResponse;
import com.nybc.ai.feedback.service.FeedbackOptimizationService;
import com.nybc.ai.rules.context.EvaluationContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 类描述：反馈优化控制器
 * 整合智能反馈与闭环优化的所有功能
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/feedback/optimization")
@Tag(name = "智能反馈与闭环优化", description = "提供智能反馈分析和闭环优化功能")
public class FeedbackOptimizationController {

    @Resource
    private FeedbackOptimizationService feedbackOptimizationService;

    /**
     * 基于作业评估结果生成闭环优化建议
     *
     * @param evaluationContext 作业评估上下文
     * @return 优化建议
     */
    @PostMapping("/closed-loop")
    @Operation(summary = "生成闭环优化建议", 
               description = "基于作业评估结果，生成包含个性化学习建议、教学改进建议和系统优化建议的闭环反馈")
    public ResultInfo<Map<String, Object>> generateClosedLoopOptimization(
            @Valid @RequestBody EvaluationContext evaluationContext) {
        try {
            Map<String, Object> optimization = feedbackOptimizationService.generateClosedLoopOptimization(evaluationContext);
            return ResultInfo.success(optimization);
        } catch (Exception e) {
            log.error("生成闭环优化建议失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "生成闭环优化建议失败：" + e.getMessage());
        }
    }

    /**
     * 为学生生成基于评估结果的个性化学习建议
     *
     * @param studentId         学生ID
     * @param tenantId          租户ID
     * @param evaluationContext 评估上下文
     * @return 个性化学习建议
     */
    @PostMapping("/post-evaluation-suggestions")
    @Operation(summary = "生成评估后个性化建议", 
               description = "基于学生的作业评估结果，生成针对性的个性化学习建议")
    public ResultInfo<PersonalizedSuggestionResponse> generatePostEvaluationSuggestions(
            @Parameter(description = "学生ID") @RequestParam Long studentId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Valid @RequestBody EvaluationContext evaluationContext) {
        try {
            PersonalizedSuggestionResponse suggestions = feedbackOptimizationService.generatePostEvaluationSuggestions(
                    studentId, tenantId, evaluationContext);
            return ResultInfo.success(suggestions);
        } catch (Exception e) {
            log.error("生成评估后个性化建议失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "生成评估后个性化建议失败：" + e.getMessage());
        }
    }

    /**
     * 分析教学效果并提供改进建议
     *
     * @param tenantId  租户ID
     * @param teacherId 教师ID，可为空
     * @return 教学效果分析结果
     */
    @PostMapping("/teaching-effectiveness")
    @Operation(summary = "分析教学效果", 
               description = "分析教学效果并提供基于数据的改进建议")
    public ResultInfo<TeachingEffectivenessResponse> analyzeTeachingEffectiveness(
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Parameter(description = "教师ID，可为空表示分析整个租户") @RequestParam(required = false) Long teacherId) {
        try {
            TeachingEffectivenessResponse response = feedbackOptimizationService.analyzeTeachingEffectivenessWithOptimization(
                    tenantId, teacherId);
            return ResultInfo.success(response);
        } catch (Exception e) {
            log.error("分析教学效果失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "分析教学效果失败：" + e.getMessage());
        }
    }

    /**
     * 基于历史数据自动优化系统Prompt
     *
     * @param tenantId 租户ID
     * @return 优化结果
     */
    @PostMapping("/auto-optimize-prompts")
    @Operation(summary = "自动优化系统Prompt", 
               description = "基于历史执行数据和反馈，自动分析并优化系统中的Prompt模板")
    public ResultInfo<Map<String, Object>> autoOptimizeSystemPrompts(
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {
        try {
            Map<String, Object> result = feedbackOptimizationService.autoOptimizeSystemPrompts(tenantId);
            return ResultInfo.success(result);
        } catch (Exception e) {
            log.error("自动优化系统Prompt失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "自动优化系统Prompt失败：" + e.getMessage());
        }
    }

    /**
     * 生成综合的反馈优化报告
     *
     * @param tenantId 租户ID
     * @return 综合优化报告
     */
    @GetMapping("/comprehensive-report")
    @Operation(summary = "生成综合反馈优化报告", 
               description = "生成包含教学效果、Prompt性能、系统优化等多维度的综合分析报告")
    public ResultInfo<Map<String, Object>> generateComprehensiveFeedbackReport(
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {
        try {
            Map<String, Object> report = feedbackOptimizationService.generateComprehensiveFeedbackReport(tenantId);
            return ResultInfo.success(report);
        } catch (Exception e) {
            log.error("生成综合反馈优化报告失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "生成综合反馈优化报告失败：" + e.getMessage());
        }
    }
}
