package com.nybc.ai.feedback.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.feedback.dto.PersonalizedSuggestionRequest;
import com.nybc.ai.feedback.dto.PersonalizedSuggestionResponse;
import com.nybc.ai.feedback.entity.StudentLearningBehavior;
import com.nybc.ai.feedback.entity.StudentLearningProfile;
import com.nybc.ai.feedback.service.PersonalizedLearningService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

/**
 * 类描述：个性化学习控制器
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/feedback/personalized-learning")
@Tag(name = "个性化学习", description = "提供个性化学习建议和学习行为分析功能")
public class PersonalizedLearningController {

    @Resource
    private PersonalizedLearningService personalizedLearningService;

    /**
     * 记录学习行为
     *
     * @param behavior 学习行为数据
     * @return 操作结果
     */
    @PostMapping("/behavior/record")
    @Operation(summary = "记录学习行为", description = "记录学生的学习行为数据，用于后续的个性化分析")
    public ResultInfo<Void> recordLearningBehavior(@Valid @RequestBody StudentLearningBehavior behavior) {
        try {
            personalizedLearningService.recordLearningBehavior(behavior);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("记录学习行为失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "记录学习行为失败：" + e.getMessage());
        }
    }

    /**
     * 获取学生学习档案
     *
     * @param studentId 学生ID
     * @param tenantId  租户ID
     * @return 学习档案
     */
    @GetMapping("/profile/{studentId}")
    @Operation(summary = "获取学生学习档案", description = "获取指定学生的个性化学习档案信息")
    public ResultInfo<StudentLearningProfile> getStudentProfile(
            @Parameter(description = "学生ID") @PathVariable Long studentId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {
        try {
            StudentLearningProfile profile = personalizedLearningService.getStudentProfile(studentId, tenantId);
            if (profile == null) {
                return ResultInfo.error(HttpStatus.NOT_FOUND.value(), "未找到学生学习档案");
            }
            return ResultInfo.success(profile);
        } catch (Exception e) {
            log.error("获取学生学习档案失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取学生学习档案失败：" + e.getMessage());
        }
    }

    /**
     * 分析并更新学生学习档案
     *
     * @param studentId 学生ID
     * @param tenantId  租户ID
     * @return 更新后的学习档案
     */
    @PostMapping("/profile/{studentId}/analyze")
    @Operation(summary = "分析并更新学习档案", description = "基于学生的学习行为数据，使用AI分析并更新学习档案")
    public ResultInfo<StudentLearningProfile> analyzeAndUpdateProfile(
            @Parameter(description = "学生ID") @PathVariable Long studentId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {
        try {
            StudentLearningProfile profile = personalizedLearningService.analyzeAndUpdateProfile(studentId, tenantId);
            return ResultInfo.success(profile);
        } catch (Exception e) {
            log.error("分析学习档案失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "分析学习档案失败：" + e.getMessage());
        }
    }

    /**
     * 生成个性化学习建议
     *
     * @param request 生成建议的请求
     * @return 个性化学习建议
     */
    @PostMapping("/suggestions/generate")
    @Operation(summary = "生成个性化学习建议", description = "基于学生的学习档案和行为数据，生成个性化的学习建议")
    public ResultInfo<PersonalizedSuggestionResponse> generatePersonalizedSuggestions(
            @Valid @RequestBody PersonalizedSuggestionRequest request) {
        try {
            PersonalizedSuggestionResponse response = personalizedLearningService.generatePersonalizedSuggestions(request);
            return ResultInfo.success(response);
        } catch (Exception e) {
            log.error("生成个性化学习建议失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "生成个性化学习建议失败：" + e.getMessage());
        }
    }

    /**
     * 标记建议为已读
     *
     * @param suggestionId 建议ID
     * @param studentId    学生ID
     * @return 操作结果
     */
    @PostMapping("/suggestions/{suggestionId}/mark-read")
    @Operation(summary = "标记建议为已读", description = "标记指定的学习建议为已读状态")
    public ResultInfo<Void> markSuggestionAsRead(
            @Parameter(description = "建议ID") @PathVariable Long suggestionId,
            @Parameter(description = "学生ID") @RequestParam Long studentId) {
        try {
            personalizedLearningService.markSuggestionAsRead(suggestionId, studentId);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("标记建议为已读失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "标记建议为已读失败：" + e.getMessage());
        }
    }

    /**
     * 标记建议为已应用
     *
     * @param suggestionId 建议ID
     * @param studentId    学生ID
     * @return 操作结果
     */
    @PostMapping("/suggestions/{suggestionId}/mark-applied")
    @Operation(summary = "标记建议为已应用", description = "标记指定的学习建议为已应用状态")
    public ResultInfo<Void> markSuggestionAsApplied(
            @Parameter(description = "建议ID") @PathVariable Long suggestionId,
            @Parameter(description = "学生ID") @RequestParam Long studentId) {
        try {
            personalizedLearningService.markSuggestionAsApplied(suggestionId, studentId);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("标记建议为已应用失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "标记建议为已应用失败：" + e.getMessage());
        }
    }

    /**
     * 评价建议的有效性
     *
     * @param suggestionId       建议ID
     * @param studentId          学生ID
     * @param effectivenessScore 有效性评分(1-5)
     * @return 操作结果
     */
    @PostMapping("/suggestions/{suggestionId}/rate")
    @Operation(summary = "评价建议有效性", description = "对学习建议的有效性进行评分，用于改进建议质量")
    public ResultInfo<Void> rateSuggestionEffectiveness(
            @Parameter(description = "建议ID") @PathVariable Long suggestionId,
            @Parameter(description = "学生ID") @RequestParam Long studentId,
            @Parameter(description = "有效性评分(1-5)") @RequestParam Double effectivenessScore) {
        try {
            if (effectivenessScore < 1 || effectivenessScore > 5) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "评分必须在1-5之间");
            }
            personalizedLearningService.rateSuggestionEffectiveness(suggestionId, studentId, effectivenessScore);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("评价建议有效性失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "评价建议有效性失败：" + e.getMessage());
        }
    }
}
