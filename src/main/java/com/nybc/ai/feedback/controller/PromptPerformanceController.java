package com.nybc.ai.feedback.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.feedback.entity.PromptPerformanceAnalytics;
import com.nybc.ai.feedback.service.PromptPerformanceAnalyticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 类描述：Prompt性能分析控制器
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/feedback/prompt-performance")
@Tag(name = "Prompt性能分析", description = "提供Prompt性能分析和自动优化功能")
public class PromptPerformanceController {

    @Resource
    private PromptPerformanceAnalyticsService promptPerformanceAnalyticsService;

    /**
     * 分析Prompt性能
     *
     * @param templateId     模板ID
     * @param versionId      版本ID，可为空
     * @param tenantId       租户ID
     * @param analysisPeriod 分析周期
     * @return 操作结果
     */
    @PostMapping("/analyze")
    @Operation(summary = "分析Prompt性能", description = "分析指定Prompt模板的性能指标并生成分析报告")
    public ResultInfo<Void> analyzePromptPerformance(
            @Parameter(description = "模板ID") @RequestParam Long templateId,
            @Parameter(description = "版本ID，可为空表示分析所有版本") @RequestParam(required = false) Long versionId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Parameter(description = "分析周期：DAILY, WEEKLY, MONTHLY") @RequestParam(defaultValue = "WEEKLY") String analysisPeriod) {
        try {
            promptPerformanceAnalyticsService.analyzePromptPerformance(templateId, versionId, tenantId, analysisPeriod);
            return ResultInfo.success();
        } catch (Exception e) {
            log.error("分析Prompt性能失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "分析Prompt性能失败：" + e.getMessage());
        }
    }

    /**
     * 获取Prompt性能分析结果
     *
     * @param templateId     模板ID
     * @param versionId      版本ID，可为空
     * @param tenantId       租户ID
     * @param analysisPeriod 分析周期
     * @return 性能分析结果
     */
    @GetMapping("/analytics")
    @Operation(summary = "获取性能分析结果", description = "获取指定Prompt的性能分析结果")
    public ResultInfo<PromptPerformanceAnalytics> getPerformanceAnalytics(
            @Parameter(description = "模板ID") @RequestParam Long templateId,
            @Parameter(description = "版本ID，可为空") @RequestParam(required = false) Long versionId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Parameter(description = "分析周期") @RequestParam(defaultValue = "WEEKLY") String analysisPeriod) {
        try {
            PromptPerformanceAnalytics analytics = promptPerformanceAnalyticsService.getPerformanceAnalytics(
                    templateId, versionId, tenantId, analysisPeriod);
            if (analytics == null) {
                return ResultInfo.error(HttpStatus.NOT_FOUND.value(), "未找到性能分析数据");
            }
            return ResultInfo.success(analytics);
        } catch (Exception e) {
            log.error("获取性能分析结果失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取性能分析结果失败：" + e.getMessage());
        }
    }

    /**
     * 获取性能趋势数据
     *
     * @param templateId  模板ID
     * @param tenantId    租户ID
     * @param periodCount 周期数量
     * @return 性能趋势数据
     */
    @GetMapping("/trend")
    @Operation(summary = "获取性能趋势", description = "获取指定Prompt的性能变化趋势数据")
    public ResultInfo<List<PromptPerformanceAnalytics>> getPerformanceTrend(
            @Parameter(description = "模板ID") @RequestParam Long templateId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Parameter(description = "周期数量，默认5") @RequestParam(defaultValue = "5") Integer periodCount) {
        try {
            List<PromptPerformanceAnalytics> trendData = promptPerformanceAnalyticsService.getPerformanceTrend(
                    templateId, tenantId, periodCount);
            return ResultInfo.success(trendData);
        } catch (Exception e) {
            log.error("获取性能趋势数据失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取性能趋势数据失败：" + e.getMessage());
        }
    }

    /**
     * 自动优化Prompt
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @param operatorId 操作人ID
     * @return 优化建议
     */
    @PostMapping("/auto-optimize")
    @Operation(summary = "自动优化Prompt", description = "基于性能分析结果自动生成Prompt优化建议")
    public ResultInfo<Map<String, Object>> autoOptimizePrompt(
            @Parameter(description = "模板ID") @RequestParam Long templateId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Parameter(description = "操作人ID") @RequestParam Long operatorId) {
        try {
            Map<String, Object> result = promptPerformanceAnalyticsService.autoOptimizePrompt(
                    templateId, tenantId, operatorId);
            return ResultInfo.success(result);
        } catch (IllegalStateException e) {
            log.warn("自动优化Prompt失败，数据不足: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), e.getMessage());
        } catch (Exception e) {
            log.error("自动优化Prompt失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "自动优化Prompt失败：" + e.getMessage());
        }
    }

    /**
     * 获取最佳性能版本
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @return 最佳版本ID
     */
    @GetMapping("/best-version")
    @Operation(summary = "获取最佳性能版本", description = "获取指定Prompt模板中性能最佳的版本")
    public ResultInfo<Long> getBestPerformingVersion(
            @Parameter(description = "模板ID") @RequestParam Long templateId,
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {
        try {
            Long bestVersionId = promptPerformanceAnalyticsService.getBestPerformingVersion(templateId, tenantId);
            if (bestVersionId == null) {
                return ResultInfo.error(HttpStatus.NOT_FOUND.value(), "未找到性能数据");
            }
            return ResultInfo.success(bestVersionId);
        } catch (Exception e) {
            log.error("获取最佳性能版本失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取最佳性能版本失败：" + e.getMessage());
        }
    }

    /**
     * 比较版本性能
     *
     * @param templateId 模板ID
     * @param versionIds 版本ID列表
     * @param tenantId   租户ID
     * @return 版本性能比较结果
     */
    @PostMapping("/compare-versions")
    @Operation(summary = "比较版本性能", description = "比较指定Prompt模板的不同版本的性能表现")
    public ResultInfo<Map<Long, PromptPerformanceAnalytics>> compareVersionPerformance(
            @Parameter(description = "模板ID") @RequestParam Long templateId,
            @Parameter(description = "版本ID列表") @RequestBody List<Long> versionIds,
            @Parameter(description = "租户ID") @RequestParam Long tenantId) {
        try {
            if (versionIds == null || versionIds.isEmpty()) {
                return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), "版本ID列表不能为空");
            }
            Map<Long, PromptPerformanceAnalytics> comparison = 
                    promptPerformanceAnalyticsService.compareVersionPerformance(templateId, versionIds, tenantId);
            return ResultInfo.success(comparison);
        } catch (Exception e) {
            log.error("比较版本性能失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "比较版本性能失败：" + e.getMessage());
        }
    }
}
