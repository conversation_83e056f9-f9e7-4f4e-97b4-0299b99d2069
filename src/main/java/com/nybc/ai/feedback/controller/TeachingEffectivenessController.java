package com.nybc.ai.feedback.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.feedback.dto.TeachingEffectivenessRequest;
import com.nybc.ai.feedback.dto.TeachingEffectivenessResponse;
import com.nybc.ai.feedback.entity.TeachingEffectivenessReport;
import com.nybc.ai.feedback.service.TeachingEffectivenessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 类描述：教学效果分析控制器
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/feedback/teaching-effectiveness")
@Tag(name = "教学效果分析", description = "提供教学效果分析和改进建议功能")
public class TeachingEffectivenessController {

    @Resource
    private TeachingEffectivenessService teachingEffectivenessService;

    /**
     * 生成教学效果分析报告
     *
     * @param request 分析请求
     * @return 教学效果分析响应
     */
    @PostMapping("/reports/generate")
    @Operation(summary = "生成教学效果分析报告", description = "基于教学数据生成详细的教学效果分析报告")
    public ResultInfo<TeachingEffectivenessResponse> generateEffectivenessReport(
            @Valid @RequestBody TeachingEffectivenessRequest request) {
        try {
            TeachingEffectivenessResponse response = teachingEffectivenessService.generateEffectivenessReport(request);
            return ResultInfo.success(response);
        } catch (Exception e) {
            log.error("生成教学效果分析报告失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "生成教学效果分析报告失败：" + e.getMessage());
        }
    }

    /**
     * 获取历史教学效果报告
     *
     * @param tenantId  租户ID
     * @param teacherId 教师ID，可为空
     * @param limit     限制数量
     * @return 历史报告列表
     */
    @GetMapping("/reports/history")
    @Operation(summary = "获取历史教学效果报告", description = "获取指定租户或教师的历史教学效果报告")
    public ResultInfo<List<TeachingEffectivenessReport>> getHistoricalReports(
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Parameter(description = "教师ID，可为空") @RequestParam(required = false) Long teacherId,
            @Parameter(description = "限制数量，默认10") @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<TeachingEffectivenessReport> reports = 
                    teachingEffectivenessService.getHistoricalReports(tenantId, teacherId, limit);
            return ResultInfo.success(reports);
        } catch (Exception e) {
            log.error("获取历史教学效果报告失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取历史教学效果报告失败：" + e.getMessage());
        }
    }

    /**
     * 获取报告详情
     *
     * @param reportId 报告ID
     * @return 教学效果分析响应
     */
    @GetMapping("/reports/{reportId}")
    @Operation(summary = "获取报告详情", description = "获取指定ID的教学效果分析报告详情")
    public ResultInfo<TeachingEffectivenessResponse> getReportDetail(
            @Parameter(description = "报告ID") @PathVariable Long reportId) {
        try {
            TeachingEffectivenessResponse response = teachingEffectivenessService.getReportDetail(reportId);
            return ResultInfo.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("获取报告详情失败，报告不存在: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.NOT_FOUND.value(), e.getMessage());
        } catch (Exception e) {
            log.error("获取报告详情失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取报告详情失败：" + e.getMessage());
        }
    }

    /**
     * 比较不同时期的教学效果
     *
     * @param tenantId    租户ID
     * @param teacherId   教师ID，可为空
     * @param reportType  报告类型
     * @param periodCount 比较的周期数量
     * @return 比较分析结果
     */
    @GetMapping("/reports/compare")
    @Operation(summary = "比较教学效果", description = "比较不同时期的教学效果，分析变化趋势")
    public ResultInfo<TeachingEffectivenessResponse> compareTeachingEffectiveness(
            @Parameter(description = "租户ID") @RequestParam Long tenantId,
            @Parameter(description = "教师ID，可为空") @RequestParam(required = false) Long teacherId,
            @Parameter(description = "报告类型") @RequestParam String reportType,
            @Parameter(description = "比较的周期数量，默认3") @RequestParam(defaultValue = "3") Integer periodCount) {
        try {
            TeachingEffectivenessResponse response = teachingEffectivenessService.compareTeachingEffectiveness(
                    tenantId, teacherId, reportType, periodCount);
            return ResultInfo.success(response);
        } catch (IllegalArgumentException e) {
            log.warn("比较教学效果失败，参数错误: {}", e.getMessage());
            return ResultInfo.error(HttpStatus.BAD_REQUEST.value(), e.getMessage());
        } catch (Exception e) {
            log.error("比较教学效果失败: {}", e.getMessage(), e);
            return ResultInfo.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "比较教学效果失败：" + e.getMessage());
        }
    }
}
