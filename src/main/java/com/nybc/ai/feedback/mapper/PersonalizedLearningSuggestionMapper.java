package com.nybc.ai.feedback.mapper;

import com.nybc.ai.feedback.entity.PersonalizedLearningSuggestion;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类描述：个性化学习建议数据访问层
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Repository
public interface PersonalizedLearningSuggestionMapper {

    /**
     * 插入学习建议
     *
     * @param suggestion 学习建议
     * @return 影响行数
     */
    int insert(PersonalizedLearningSuggestion suggestion);

    /**
     * 批量插入学习建议
     *
     * @param suggestions 学习建议列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<PersonalizedLearningSuggestion> suggestions);

    /**
     * 根据学生ID和租户ID查询有效的学习建议
     *
     * @param studentId 学生ID
     * @param tenantId  租户ID
     * @param limit     限制数量
     * @return 学习建议列表
     */
    List<PersonalizedLearningSuggestion> findActiveByStudent(@Param("studentId") Long studentId,
                                                             @Param("tenantId") Long tenantId,
                                                             @Param("limit") Integer limit);

    /**
     * 根据建议类型查询学习建议
     *
     * @param studentId      学生ID
     * @param tenantId       租户ID
     * @param suggestionType 建议类型
     * @return 学习建议列表
     */
    List<PersonalizedLearningSuggestion> findByType(@Param("studentId") Long studentId,
                                                    @Param("tenantId") Long tenantId,
                                                    @Param("suggestionType") String suggestionType);

    /**
     * 更新建议的阅读状态
     *
     * @param id     建议ID
     * @param isRead 是否已读
     * @return 影响行数
     */
    int updateReadStatus(@Param("id") Long id, @Param("isRead") Boolean isRead);

    /**
     * 更新建议的应用状态
     *
     * @param id        建议ID
     * @param isApplied 是否已应用
     * @return 影响行数
     */
    int updateAppliedStatus(@Param("id") Long id, @Param("isApplied") Boolean isApplied);

    /**
     * 更新建议的有效性评分
     *
     * @param id                  建议ID
     * @param effectivenessScore 有效性评分
     * @return 影响行数
     */
    int updateEffectivenessScore(@Param("id") Long id, @Param("effectivenessScore") Double effectivenessScore);

    /**
     * 删除过期的建议
     *
     * @param expireTime 过期时间
     * @return 影响行数
     */
    int deleteExpired(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 根据优先级查询建议
     *
     * @param studentId     学生ID
     * @param tenantId      租户ID
     * @param priorityLevel 优先级
     * @return 学习建议列表
     */
    List<PersonalizedLearningSuggestion> findByPriority(@Param("studentId") Long studentId,
                                                        @Param("tenantId") Long tenantId,
                                                        @Param("priorityLevel") Integer priorityLevel);
}
