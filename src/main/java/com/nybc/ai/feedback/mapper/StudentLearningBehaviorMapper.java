package com.nybc.ai.feedback.mapper;

import com.nybc.ai.feedback.entity.StudentLearningBehavior;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类描述：学生学习行为数据访问层
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Repository
public interface StudentLearningBehaviorMapper {

    /**
     * 插入学习行为记录
     *
     * @param behavior 学习行为记录
     * @return 影响行数
     */
    int insert(StudentLearningBehavior behavior);

    /**
     * 批量插入学习行为记录
     *
     * @param behaviors 学习行为记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<StudentLearningBehavior> behaviors);

    /**
     * 根据学生ID和租户ID查询学习行为
     *
     * @param studentId 学生ID
     * @param tenantId  租户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 学习行为列表
     */
    List<StudentLearningBehavior> findByStudentAndTenant(@Param("studentId") Long studentId,
                                                         @Param("tenantId") Long tenantId,
                                                         @Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 根据作业ID查询学习行为
     *
     * @param assignmentId 作业ID
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 学习行为列表
     */
    List<StudentLearningBehavior> findByAssignment(@Param("assignmentId") Long assignmentId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 根据行为类型统计数量
     *
     * @param studentId    学生ID
     * @param tenantId     租户ID
     * @param behaviorType 行为类型
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 行为数量
     */
    int countByBehaviorType(@Param("studentId") Long studentId,
                            @Param("tenantId") Long tenantId,
                            @Param("behaviorType") String behaviorType,
                            @Param("startTime") LocalDateTime startTime,
                            @Param("endTime") LocalDateTime endTime);

    /**
     * 获取学生最近的学习行为
     *
     * @param studentId 学生ID
     * @param tenantId  租户ID
     * @param limit     限制数量
     * @return 最近的学习行为列表
     */
    List<StudentLearningBehavior> findRecentBehaviors(@Param("studentId") Long studentId,
                                                      @Param("tenantId") Long tenantId,
                                                      @Param("limit") Integer limit);
}
