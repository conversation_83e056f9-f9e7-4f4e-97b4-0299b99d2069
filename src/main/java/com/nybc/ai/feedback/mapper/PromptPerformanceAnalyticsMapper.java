package com.nybc.ai.feedback.mapper;

import com.nybc.ai.feedback.entity.PromptPerformanceAnalytics;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类描述：Prompt性能分析数据访问层
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Repository
public interface PromptPerformanceAnalyticsMapper {

    /**
     * 插入性能分析记录
     *
     * @param analytics 性能分析记录
     * @return 影响行数
     */
    int insert(PromptPerformanceAnalytics analytics);

    /**
     * 根据模板和周期查询性能分析
     *
     * @param templateId     模板ID
     * @param versionId      版本ID，可为空
     * @param tenantId       租户ID
     * @param analysisPeriod 分析周期
     * @return 性能分析记录
     */
    PromptPerformanceAnalytics findByTemplateAndPeriod(@Param("templateId") Long templateId,
                                                       @Param("versionId") Long versionId,
                                                       @Param("tenantId") Long tenantId,
                                                       @Param("analysisPeriod") String analysisPeriod);

    /**
     * 获取性能趋势数据
     *
     * @param templateId  模板ID
     * @param tenantId    租户ID
     * @param periodCount 周期数量
     * @return 性能分析记录列表
     */
    List<PromptPerformanceAnalytics> findTrendData(@Param("templateId") Long templateId,
                                                   @Param("tenantId") Long tenantId,
                                                   @Param("periodCount") Integer periodCount);

    /**
     * 获取最近的性能分析数据
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @param limit      限制数量
     * @return 性能分析记录列表
     */
    List<PromptPerformanceAnalytics> findRecentAnalytics(@Param("templateId") Long templateId,
                                                         @Param("tenantId") Long tenantId,
                                                         @Param("limit") Integer limit);

    /**
     * 查找最佳性能版本
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @return 最佳版本ID
     */
    Long findBestPerformingVersion(@Param("templateId") Long templateId,
                                   @Param("tenantId") Long tenantId);

    /**
     * 根据版本查询最新的性能分析
     *
     * @param templateId 模板ID
     * @param versionId  版本ID
     * @param tenantId   租户ID
     * @return 性能分析记录
     */
    PromptPerformanceAnalytics findLatestByVersion(@Param("templateId") Long templateId,
                                                   @Param("versionId") Long versionId,
                                                   @Param("tenantId") Long tenantId);

    /**
     * 根据时间范围查询性能分析
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 性能分析记录列表
     */
    List<PromptPerformanceAnalytics> findByTimeRange(@Param("templateId") Long templateId,
                                                     @Param("tenantId") Long tenantId,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 更新性能分析记录
     *
     * @param analytics 性能分析记录
     * @return 影响行数
     */
    int update(PromptPerformanceAnalytics analytics);

    /**
     * 删除过期的性能分析记录
     *
     * @param expireTime 过期时间
     * @return 影响行数
     */
    int deleteExpired(@Param("expireTime") LocalDateTime expireTime);
}
