package com.nybc.ai.feedback.mapper;

import com.nybc.ai.feedback.entity.TeachingEffectivenessReport;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 类描述：教学效果分析报告数据访问层
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Repository
public interface TeachingEffectivenessReportMapper {

    /**
     * 插入教学效果报告
     *
     * @param report 教学效果报告
     * @return 影响行数
     */
    int insert(TeachingEffectivenessReport report);

    /**
     * 根据ID查询报告
     *
     * @param id 报告ID
     * @return 教学效果报告
     */
    TeachingEffectivenessReport findById(@Param("id") Long id);

    /**
     * 查询历史报告
     *
     * @param tenantId  租户ID
     * @param teacherId 教师ID，可为空
     * @param limit     限制数量
     * @return 历史报告列表
     */
    List<TeachingEffectivenessReport> findHistoricalReports(@Param("tenantId") Long tenantId,
                                                            @Param("teacherId") Long teacherId,
                                                            @Param("limit") Integer limit);

    /**
     * 根据报告类型和周期查询报告
     *
     * @param tenantId    租户ID
     * @param teacherId   教师ID，可为空
     * @param reportType  报告类型
     * @param periodCount 周期数量
     * @return 报告列表
     */
    List<TeachingEffectivenessReport> findReportsByTypeAndPeriod(@Param("tenantId") Long tenantId,
                                                                 @Param("teacherId") Long teacherId,
                                                                 @Param("reportType") String reportType,
                                                                 @Param("periodCount") Integer periodCount);

    /**
     * 根据租户和时间范围查询报告
     *
     * @param tenantId  租户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 报告列表
     */
    List<TeachingEffectivenessReport> findByTenantAndTimeRange(@Param("tenantId") Long tenantId,
                                                               @Param("startTime") LocalDateTime startTime,
                                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 根据作业ID查询报告
     *
     * @param assignmentId 作业ID
     * @return 报告列表
     */
    List<TeachingEffectivenessReport> findByAssignment(@Param("assignmentId") Long assignmentId);

    /**
     * 更新报告
     *
     * @param report 教学效果报告
     * @return 影响行数
     */
    int update(TeachingEffectivenessReport report);

    /**
     * 删除报告
     *
     * @param id 报告ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
}
