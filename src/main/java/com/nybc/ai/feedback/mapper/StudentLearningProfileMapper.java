package com.nybc.ai.feedback.mapper;

import com.nybc.ai.feedback.entity.StudentLearningProfile;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 类描述：学生个性化学习档案数据访问层
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Repository
public interface StudentLearningProfileMapper {

    /**
     * 根据学生ID和租户ID查询学习档案
     *
     * @param studentId 学生ID
     * @param tenantId  租户ID
     * @return 学习档案
     */
    StudentLearningProfile findByStudentAndTenant(@Param("studentId") Long studentId,
                                                  @Param("tenantId") Long tenantId);

    /**
     * 插入学习档案
     *
     * @param profile 学习档案
     * @return 影响行数
     */
    int insert(StudentLearningProfile profile);

    /**
     * 更新学习档案
     *
     * @param profile 学习档案
     * @return 影响行数
     */
    int update(StudentLearningProfile profile);

    /**
     * 根据租户ID查询所有学习档案
     *
     * @param tenantId 租户ID
     * @return 学习档案列表
     */
    List<StudentLearningProfile> findByTenant(@Param("tenantId") Long tenantId);

    /**
     * 根据学习风格查询学习档案
     *
     * @param tenantId      租户ID
     * @param learningStyle 学习风格
     * @return 学习档案列表
     */
    List<StudentLearningProfile> findByLearningStyle(@Param("tenantId") Long tenantId,
                                                     @Param("learningStyle") String learningStyle);

    /**
     * 根据知识水平查询学习档案
     *
     * @param tenantId       租户ID
     * @param knowledgeLevel 知识水平
     * @return 学习档案列表
     */
    List<StudentLearningProfile> findByKnowledgeLevel(@Param("tenantId") Long tenantId,
                                                      @Param("knowledgeLevel") String knowledgeLevel);
}
