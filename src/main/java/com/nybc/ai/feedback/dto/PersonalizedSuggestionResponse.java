package com.nybc.ai.feedback.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 类描述：个性化学习建议响应体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "个性化学习建议响应体")
public class PersonalizedSuggestionResponse {

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "学习档案信息")
    private LearningProfileInfo profileInfo;

    @Schema(description = "个性化建议列表")
    private List<SuggestionInfo> suggestions;

    @Schema(description = "生成时间")
    private LocalDateTime generateTime;

    @Data
    @Schema(description = "学习档案信息")
    public static class LearningProfileInfo {
        @Schema(description = "学习风格")
        private String learningStyle;

        @Schema(description = "知识水平")
        private String knowledgeLevel;

        @Schema(description = "薄弱知识点")
        private List<String> weakAreas;

        @Schema(description = "擅长知识点")
        private List<String> strongAreas;

        @Schema(description = "最近表现趋势")
        private String performanceTrend;
    }

    @Data
    @Schema(description = "建议信息")
    public static class SuggestionInfo {
        @Schema(description = "建议ID")
        private Long id;

        @Schema(description = "建议类型")
        private String suggestionType;

        @Schema(description = "建议标题")
        private String title;

        @Schema(description = "建议内容")
        private String content;

        @Schema(description = "优先级")
        private Integer priorityLevel;

        @Schema(description = "目标知识点")
        private List<String> targetKnowledgePoints;

        @Schema(description = "推荐资源")
        private List<Map<String, Object>> suggestedResources;

        @Schema(description = "过期时间")
        private LocalDateTime expireTime;
    }
}
