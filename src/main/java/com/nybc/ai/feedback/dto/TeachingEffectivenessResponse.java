package com.nybc.ai.feedback.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 类描述：教学效果分析响应体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "教学效果分析响应体")
public class TeachingEffectivenessResponse {

    @Schema(description = "报告ID")
    private Long reportId;

    @Schema(description = "报告类型")
    private String reportType;

    @Schema(description = "报告周期")
    private String reportPeriod;

    @Schema(description = "分析数据")
    private AnalysisData analysisData;

    @Schema(description = "关键洞察")
    private List<String> keyInsights;

    @Schema(description = "改进建议")
    private List<ImprovementSuggestion> improvementSuggestions;

    @Schema(description = "性能指标")
    private PerformanceMetrics performanceMetrics;

    @Schema(description = "学生反馈汇总")
    private StudentFeedbackSummary studentFeedbackSummary;

    @Schema(description = "生成时间")
    private LocalDateTime generateTime;

    @Data
    @Schema(description = "分析数据")
    public static class AnalysisData {
        @Schema(description = "总学生数")
        private Integer totalStudents;

        @Schema(description = "总作业数")
        private Integer totalAssignments;

        @Schema(description = "平均分数")
        private Double averageScore;

        @Schema(description = "及格率")
        private Double passRate;

        @Schema(description = "优秀率")
        private Double excellentRate;

        @Schema(description = "分数分布")
        private Map<String, Integer> scoreDistribution;

        @Schema(description = "知识点掌握情况")
        private Map<String, Double> knowledgePointMastery;
    }

    @Data
    @Schema(description = "改进建议")
    public static class ImprovementSuggestion {
        @Schema(description = "建议类型")
        private String suggestionType;

        @Schema(description = "建议内容")
        private String content;

        @Schema(description = "优先级")
        private Integer priority;

        @Schema(description = "预期效果")
        private String expectedEffect;
    }

    @Data
    @Schema(description = "性能指标")
    public static class PerformanceMetrics {
        @Schema(description = "教学质量评分")
        private Double teachingQualityScore;

        @Schema(description = "学生参与度")
        private Double studentEngagement;

        @Schema(description = "作业完成率")
        private Double assignmentCompletionRate;

        @Schema(description = "平均反馈时间(小时)")
        private Double avgFeedbackTime;

        @Schema(description = "学生满意度")
        private Double studentSatisfaction;
    }

    @Data
    @Schema(description = "学生反馈汇总")
    public static class StudentFeedbackSummary {
        @Schema(description = "总反馈数")
        private Integer totalFeedbacks;

        @Schema(description = "正面反馈率")
        private Double positiveFeedbackRate;

        @Schema(description = "常见问题")
        private List<String> commonIssues;

        @Schema(description = "学生建议")
        private List<String> studentSuggestions;
    }
}
