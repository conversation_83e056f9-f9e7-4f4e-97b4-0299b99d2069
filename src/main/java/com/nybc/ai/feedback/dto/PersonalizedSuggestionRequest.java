package com.nybc.ai.feedback.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 类描述：生成个性化学习建议的请求体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Accessors(chain = true)
@Schema(description = "生成个性化学习建议的请求体")
public class PersonalizedSuggestionRequest {

    @NotNull(message = "学生ID不能为空")
    @Schema(description = "学生ID")
    private Long studentId;

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "作业ID，如果指定则针对特定作业生成建议")
    private Long assignmentId;

    @Schema(description = "是否强制重新分析，默认false")
    private Boolean forceReanalyze = false;

    @Schema(description = "建议数量限制，默认10条")
    private Integer suggestionLimit = 10;
}
