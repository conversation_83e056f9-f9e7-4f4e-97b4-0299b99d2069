package com.nybc.ai.feedback.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 类描述：教学效果分析请求体
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Schema(description = "教学效果分析请求体")
public class TeachingEffectivenessRequest {

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "教师ID，如果不指定则分析整个租户的教学效果")
    private Long teacherId;

    @Schema(description = "作业ID，如果指定则针对特定作业进行分析")
    private Long assignmentId;

    @NotBlank(message = "报告类型不能为空")
    @Schema(description = "报告类型：ASSIGNMENT, COURSE, SEMESTER")
    private String reportType;

    @Schema(description = "报告周期：WEEKLY, MONTHLY, SEMESTER")
    private String reportPeriod;

    @Schema(description = "分析开始时间")
    private LocalDateTime startTime;

    @Schema(description = "分析结束时间")
    private LocalDateTime endTime;

    @NotNull(message = "创建用户ID不能为空")
    @Schema(description = "创建用户ID")
    private Long createUser;
}
