package com.nybc.ai.feedback.service;

import com.nybc.ai.feedback.dto.TeachingEffectivenessRequest;
import com.nybc.ai.feedback.dto.TeachingEffectivenessResponse;
import com.nybc.ai.feedback.entity.TeachingEffectivenessReport;

import java.util.List;

/**
 * 类描述：教学效果分析服务接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface TeachingEffectivenessService {

    /**
     * 生成教学效果分析报告
     *
     * @param request 分析请求
     * @return 教学效果分析响应
     */
    TeachingEffectivenessResponse generateEffectivenessReport(TeachingEffectivenessRequest request);

    /**
     * 获取历史教学效果报告
     *
     * @param tenantId  租户ID
     * @param teacherId 教师ID，可为空
     * @param limit     限制数量
     * @return 历史报告列表
     */
    List<TeachingEffectivenessReport> getHistoricalReports(Long tenantId, Long teacherId, Integer limit);

    /**
     * 根据报告ID获取详细报告
     *
     * @param reportId 报告ID
     * @return 教学效果分析响应
     */
    TeachingEffectivenessResponse getReportDetail(Long reportId);

    /**
     * 比较不同时期的教学效果
     *
     * @param tenantId    租户ID
     * @param teacherId   教师ID，可为空
     * @param reportType  报告类型
     * @param periodCount 比较的周期数量
     * @return 比较分析结果
     */
    TeachingEffectivenessResponse compareTeachingEffectiveness(Long tenantId, Long teacherId, 
                                                               String reportType, Integer periodCount);
}
