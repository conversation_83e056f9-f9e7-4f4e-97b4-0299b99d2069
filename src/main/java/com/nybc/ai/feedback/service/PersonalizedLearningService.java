package com.nybc.ai.feedback.service;

import com.nybc.ai.feedback.dto.PersonalizedSuggestionRequest;
import com.nybc.ai.feedback.dto.PersonalizedSuggestionResponse;
import com.nybc.ai.feedback.entity.StudentLearningBehavior;
import com.nybc.ai.feedback.entity.StudentLearningProfile;

/**
 * 类描述：个性化学习服务接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface PersonalizedLearningService {

    /**
     * 记录学生学习行为
     *
     * @param behavior 学习行为数据
     */
    void recordLearningBehavior(StudentLearningBehavior behavior);

    /**
     * 分析并更新学生学习档案
     *
     * @param studentId 学生ID
     * @param tenantId  租户ID
     * @return 更新后的学习档案
     */
    StudentLearningProfile analyzeAndUpdateProfile(Long studentId, Long tenantId);

    /**
     * 生成个性化学习建议
     *
     * @param request 生成建议的请求
     * @return 个性化学习建议响应
     */
    PersonalizedSuggestionResponse generatePersonalizedSuggestions(PersonalizedSuggestionRequest request);

    /**
     * 获取学生的学习档案
     *
     * @param studentId 学生ID
     * @param tenantId  租户ID
     * @return 学习档案
     */
    StudentLearningProfile getStudentProfile(Long studentId, Long tenantId);

    /**
     * 标记建议为已读
     *
     * @param suggestionId 建议ID
     * @param studentId    学生ID
     */
    void markSuggestionAsRead(Long suggestionId, Long studentId);

    /**
     * 标记建议为已应用
     *
     * @param suggestionId 建议ID
     * @param studentId    学生ID
     */
    void markSuggestionAsApplied(Long suggestionId, Long studentId);

    /**
     * 评价建议的有效性
     *
     * @param suggestionId       建议ID
     * @param studentId          学生ID
     * @param effectivenessScore 有效性评分(1-5)
     */
    void rateSuggestionEffectiveness(Long suggestionId, Long studentId, Double effectivenessScore);
}
