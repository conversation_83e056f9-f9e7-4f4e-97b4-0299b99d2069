package com.nybc.ai.feedback.service;

import com.nybc.ai.feedback.entity.PromptPerformanceAnalytics;

import java.util.List;
import java.util.Map;

/**
 * 类描述：Prompt性能分析服务接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface PromptPerformanceAnalyticsService {

    /**
     * 分析Prompt性能并生成分析报告
     *
     * @param templateId     模板ID
     * @param versionId      版本ID，可为空表示分析所有版本
     * @param tenantId       租户ID
     * @param analysisPeriod 分析周期：DAILY, WEEKLY, MONTHLY
     */
    void analyzePromptPerformance(Long templateId, Long versionId, Long tenantId, String analysisPeriod);

    /**
     * 获取Prompt性能分析结果
     *
     * @param templateId     模板ID
     * @param versionId      版本ID，可为空
     * @param tenantId       租户ID
     * @param analysisPeriod 分析周期
     * @return 性能分析结果
     */
    PromptPerformanceAnalytics getPerformanceAnalytics(Long templateId, Long versionId, 
                                                       Long tenantId, String analysisPeriod);

    /**
     * 获取性能趋势数据
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @param periodCount 周期数量
     * @return 性能趋势数据
     */
    List<PromptPerformanceAnalytics> getPerformanceTrend(Long templateId, Long tenantId, Integer periodCount);

    /**
     * 基于性能分析自动优化Prompt
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @param operatorId 操作人ID
     * @return 优化建议
     */
    Map<String, Object> autoOptimizePrompt(Long templateId, Long tenantId, Long operatorId);

    /**
     * 获取性能最佳的Prompt版本
     *
     * @param templateId 模板ID
     * @param tenantId   租户ID
     * @return 最佳版本ID
     */
    Long getBestPerformingVersion(Long templateId, Long tenantId);

    /**
     * 比较不同版本的性能
     *
     * @param templateId 模板ID
     * @param versionIds 版本ID列表
     * @param tenantId   租户ID
     * @return 版本性能比较结果
     */
    Map<Long, PromptPerformanceAnalytics> compareVersionPerformance(Long templateId, 
                                                                    List<Long> versionIds, 
                                                                    Long tenantId);
}
