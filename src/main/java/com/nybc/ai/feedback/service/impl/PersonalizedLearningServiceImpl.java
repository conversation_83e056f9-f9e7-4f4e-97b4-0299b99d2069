package com.nybc.ai.feedback.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.feedback.dto.PersonalizedSuggestionRequest;
import com.nybc.ai.feedback.dto.PersonalizedSuggestionResponse;
import com.nybc.ai.feedback.entity.PersonalizedLearningSuggestion;
import com.nybc.ai.feedback.entity.StudentLearningBehavior;
import com.nybc.ai.feedback.entity.StudentLearningProfile;
import com.nybc.ai.feedback.enums.LearningBehaviorType;
import com.nybc.ai.feedback.enums.LearningStyle;
import com.nybc.ai.feedback.enums.SuggestionType;
import com.nybc.ai.feedback.mapper.PersonalizedLearningSuggestionMapper;
import com.nybc.ai.feedback.mapper.StudentLearningBehaviorMapper;
import com.nybc.ai.feedback.mapper.StudentLearningProfileMapper;
import com.nybc.ai.feedback.service.PersonalizedLearningService;
import com.nybc.ai.infra.ai.AiModelService;
import com.nybc.ai.infra.ai.AiModelServiceFactory;
import com.nybc.ai.rules.context.EvaluationContext;
import com.nybc.ai.rules.mapper.RuleChainMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述：个性化学习服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class PersonalizedLearningServiceImpl implements PersonalizedLearningService {

    @Resource
    private StudentLearningBehaviorMapper behaviorMapper;

    @Resource
    private StudentLearningProfileMapper profileMapper;

    @Resource
    private PersonalizedLearningSuggestionMapper suggestionMapper;

    @Resource
    private AiModelServiceFactory aiModelServiceFactory;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${app.ai.default-provider:deepseek}")
    private String defaultAiProvider;

    @Override
    @Transactional
    public void recordLearningBehavior(StudentLearningBehavior behavior) {
        try {
            behavior.setCreateTime(LocalDateTime.now());
            behaviorMapper.insert(behavior);
            log.info("记录学习行为成功: 学生ID[{}], 行为类型[{}]", behavior.getStudentId(), behavior.getBehaviorType());
        } catch (Exception e) {
            log.error("记录学习行为失败: {}", e.getMessage(), e);
            throw new RuntimeException("记录学习行为失败", e);
        }
    }

    @Override
    @Transactional
    public StudentLearningProfile analyzeAndUpdateProfile(Long studentId, Long tenantId) {
        try {
            log.info("开始分析学生学习档案: 学生ID[{}], 租户ID[{}]", studentId, tenantId);

            // 1. 获取现有档案或创建新档案
            StudentLearningProfile profile = profileMapper.findByStudentAndTenant(studentId, tenantId);
            boolean isNewProfile = (profile == null);
            if (isNewProfile) {
                profile = new StudentLearningProfile()
                        .setStudentId(studentId)
                        .setTenantId(tenantId)
                        .setCreateTime(LocalDateTime.now());
            }

            // 2. 分析最近30天的学习行为
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minus(30, ChronoUnit.DAYS);
            List<StudentLearningBehavior> recentBehaviors = behaviorMapper.findByStudentAndTenant(
                    studentId, tenantId, startTime, endTime);

            // 3. 使用AI分析学习模式和特征
            String analysisResult = analyzeStudentLearningPattern(recentBehaviors);
            Map<String, Object> analysisData = parseAnalysisResult(analysisResult);

            // 4. 更新学习档案
            updateProfileFromAnalysis(profile, analysisData);
            profile.setLastAnalysisTime(LocalDateTime.now())
                    .setUpdateTime(LocalDateTime.now());

            // 5. 保存或更新档案
            if (isNewProfile) {
                profileMapper.insert(profile);
            } else {
                profileMapper.update(profile);
            }

            log.info("学生学习档案分析完成: 学生ID[{}], 学习风格[{}], 知识水平[{}]", 
                    studentId, profile.getLearningStyle(), profile.getKnowledgeLevel());
            return profile;

        } catch (Exception e) {
            log.error("分析学生学习档案失败: {}", e.getMessage(), e);
            throw new RuntimeException("分析学生学习档案失败", e);
        }
    }

    @Override
    @Transactional
    public PersonalizedSuggestionResponse generatePersonalizedSuggestions(PersonalizedSuggestionRequest request) {
        try {
            log.info("开始生成个性化学习建议: 学生ID[{}], 租户ID[{}]", request.getStudentId(), request.getTenantId());

            // 1. 获取或更新学生档案
            StudentLearningProfile profile;
            if (request.getForceReanalyze()) {
                profile = analyzeAndUpdateProfile(request.getStudentId(), request.getTenantId());
            } else {
                profile = getStudentProfile(request.getStudentId(), request.getTenantId());
                if (profile == null || shouldUpdateProfile(profile)) {
                    profile = analyzeAndUpdateProfile(request.getStudentId(), request.getTenantId());
                }
            }

            // 2. 使用AI生成个性化建议
            String suggestionsJson = generateAISuggestions(profile, request);
            List<Map<String, Object>> aiSuggestions = parseAISuggestions(suggestionsJson);

            // 3. 保存建议到数据库
            List<PersonalizedLearningSuggestion> suggestions = saveSuggestions(aiSuggestions, request);

            // 4. 构建响应
            PersonalizedSuggestionResponse response = buildResponse(profile, suggestions);
            response.setGenerateTime(LocalDateTime.now());

            log.info("个性化学习建议生成完成: 学生ID[{}], 建议数量[{}]", 
                    request.getStudentId(), suggestions.size());
            return response;

        } catch (Exception e) {
            log.error("生成个性化学习建议失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成个性化学习建议失败", e);
        }
    }

    @Override
    public StudentLearningProfile getStudentProfile(Long studentId, Long tenantId) {
        return profileMapper.findByStudentAndTenant(studentId, tenantId);
    }

    @Override
    @Transactional
    public void markSuggestionAsRead(Long suggestionId, Long studentId) {
        try {
            suggestionMapper.updateReadStatus(suggestionId, true);
            
            // 记录学习行为
            StudentLearningBehavior behavior = new StudentLearningBehavior()
                    .setStudentId(studentId)
                    .setBehaviorType(LearningBehaviorType.VIEW_SUGGESTION.name())
                    .setBehaviorData("{\"suggestionId\":" + suggestionId + "}")
                    .setCreateTime(LocalDateTime.now());
            recordLearningBehavior(behavior);
            
            log.info("标记建议为已读: 建议ID[{}], 学生ID[{}]", suggestionId, studentId);
        } catch (Exception e) {
            log.error("标记建议为已读失败: {}", e.getMessage(), e);
            throw new RuntimeException("标记建议为已读失败", e);
        }
    }

    @Override
    @Transactional
    public void markSuggestionAsApplied(Long suggestionId, Long studentId) {
        try {
            suggestionMapper.updateAppliedStatus(suggestionId, true);
            
            // 记录学习行为
            StudentLearningBehavior behavior = new StudentLearningBehavior()
                    .setStudentId(studentId)
                    .setBehaviorType(LearningBehaviorType.APPLY_SUGGESTION.name())
                    .setBehaviorData("{\"suggestionId\":" + suggestionId + "}")
                    .setCreateTime(LocalDateTime.now());
            recordLearningBehavior(behavior);
            
            log.info("标记建议为已应用: 建议ID[{}], 学生ID[{}]", suggestionId, studentId);
        } catch (Exception e) {
            log.error("标记建议为已应用失败: {}", e.getMessage(), e);
            throw new RuntimeException("标记建议为已应用失败", e);
        }
    }

    @Override
    @Transactional
    public void rateSuggestionEffectiveness(Long suggestionId, Long studentId, Double effectivenessScore) {
        try {
            suggestionMapper.updateEffectivenessScore(suggestionId, effectivenessScore);
            log.info("评价建议有效性: 建议ID[{}], 学生ID[{}], 评分[{}]", 
                    suggestionId, studentId, effectivenessScore);
        } catch (Exception e) {
            log.error("评价建议有效性失败: {}", e.getMessage(), e);
            throw new RuntimeException("评价建议有效性失败", e);
        }
    }

    /**
     * 使用AI分析学生学习模式
     */
    private String analyzeStudentLearningPattern(List<StudentLearningBehavior> behaviors) {
        if (CollectionUtils.isEmpty(behaviors)) {
            return getDefaultAnalysisResult();
        }

        String prompt = buildLearningPatternAnalysisPrompt(behaviors);
        AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
        return aiService.call(prompt, Collections.singletonMap("temperature", 0.3));
    }

    /**
     * 构建学习模式分析的Prompt
     */
    private String buildLearningPatternAnalysisPrompt(List<StudentLearningBehavior> behaviors) {
        StringBuilder behaviorSummary = new StringBuilder();
        Map<String, Long> behaviorCounts = behaviors.stream()
                .collect(Collectors.groupingBy(StudentLearningBehavior::getBehaviorType, Collectors.counting()));

        behaviorCounts.forEach((type, count) -> 
                behaviorSummary.append(String.format("- %s: %d次\n", type, count)));

        return String.format("""
                你是一位专业的教育数据分析师，请根据学生的学习行为数据，分析其学习模式和特征。
                
                ## 学生学习行为统计（最近30天）：
                %s
                
                ## 分析要求：
                请从以下维度进行分析，并以JSON格式返回结果：
                1. 学习风格（VISUAL/AUDITORY/KINESTHETIC/READING）
                2. 知识水平（BEGINNER/INTERMEDIATE/ADVANCED）
                3. 薄弱知识点（数组格式）
                4. 擅长知识点（数组格式）
                5. 学习偏好（对象格式）
                6. 成绩趋势（对象格式，包含trend和description字段）
                
                ## 返回格式：
                ```json
                {
                  "learningStyle": "VISUAL",
                  "knowledgeLevel": "INTERMEDIATE",
                  "weakAreas": ["数据结构", "算法复杂度"],
                  "strongAreas": ["Java基础", "面向对象编程"],
                  "learningPreferences": {
                    "preferredTime": "evening",
                    "studyDuration": "2-3hours",
                    "feedbackStyle": "detailed"
                  },
                  "performanceTrends": {
                    "trend": "improving",
                    "description": "最近表现呈上升趋势，学习积极性较高"
                  }
                }
                ```
                """, behaviorSummary.toString());
    }

    /**
     * 解析AI分析结果
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseAnalysisResult(String analysisResult) {
        try {
            return objectMapper.readValue(analysisResult, Map.class);
        } catch (Exception e) {
            log.warn("解析AI分析结果失败，使用默认结果: {}", e.getMessage());
            return getDefaultAnalysisData();
        }
    }

    /**
     * 获取默认分析结果
     */
    private String getDefaultAnalysisResult() {
        return """
                {
                  "learningStyle": "READING",
                  "knowledgeLevel": "BEGINNER",
                  "weakAreas": ["基础概念"],
                  "strongAreas": [],
                  "learningPreferences": {
                    "preferredTime": "morning",
                    "studyDuration": "1-2hours",
                    "feedbackStyle": "simple"
                  },
                  "performanceTrends": {
                    "trend": "stable",
                    "description": "学习表现稳定，需要更多练习"
                  }
                }
                """;
    }

    /**
     * 获取默认分析数据
     */
    private Map<String, Object> getDefaultAnalysisData() {
        Map<String, Object> data = new HashMap<>();
        data.put("learningStyle", "READING");
        data.put("knowledgeLevel", "BEGINNER");
        data.put("weakAreas", Arrays.asList("基础概念"));
        data.put("strongAreas", new ArrayList<>());
        
        Map<String, String> preferences = new HashMap<>();
        preferences.put("preferredTime", "morning");
        preferences.put("studyDuration", "1-2hours");
        preferences.put("feedbackStyle", "simple");
        data.put("learningPreferences", preferences);
        
        Map<String, String> trends = new HashMap<>();
        trends.put("trend", "stable");
        trends.put("description", "学习表现稳定，需要更多练习");
        data.put("performanceTrends", trends);
        
        return data;
    }

    /**
     * 从分析结果更新档案
     */
    private void updateProfileFromAnalysis(StudentLearningProfile profile, Map<String, Object> analysisData) {
        try {
            profile.setLearningStyle((String) analysisData.get("learningStyle"));
            profile.setKnowledgeLevel((String) analysisData.get("knowledgeLevel"));
            profile.setWeakAreas(objectMapper.writeValueAsString(analysisData.get("weakAreas")));
            profile.setStrongAreas(objectMapper.writeValueAsString(analysisData.get("strongAreas")));
            profile.setLearningPreferences(objectMapper.writeValueAsString(analysisData.get("learningPreferences")));
            profile.setPerformanceTrends(objectMapper.writeValueAsString(analysisData.get("performanceTrends")));
        } catch (Exception e) {
            log.error("更新学习档案失败: {}", e.getMessage(), e);
            throw new RuntimeException("更新学习档案失败", e);
        }
    }

    /**
     * 判断是否需要更新档案
     */
    private boolean shouldUpdateProfile(StudentLearningProfile profile) {
        if (profile.getLastAnalysisTime() == null) {
            return true;
        }
        // 如果超过7天未分析，则需要重新分析
        return ChronoUnit.DAYS.between(profile.getLastAnalysisTime(), LocalDateTime.now()) > 7;
    }

    /**
     * 使用AI生成个性化建议
     */
    private String generateAISuggestions(StudentLearningProfile profile, PersonalizedSuggestionRequest request) {
        String prompt = buildSuggestionGenerationPrompt(profile, request);
        AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
        return aiService.call(prompt, Collections.singletonMap("temperature", 0.5));
    }

    /**
     * 构建建议生成的Prompt
     */
    private String buildSuggestionGenerationPrompt(StudentLearningProfile profile, PersonalizedSuggestionRequest request) {
        return String.format("""
                你是一位专业的个性化学习顾问，请根据学生的学习档案生成个性化的学习建议。
                
                ## 学生学习档案：
                - 学习风格: %s
                - 知识水平: %s
                - 薄弱知识点: %s
                - 擅长知识点: %s
                - 学习偏好: %s
                - 成绩趋势: %s
                
                ## 要求：
                1. 生成%d条个性化学习建议
                2. 建议类型包括：KNOWLEDGE_GAP（知识缺口）、STUDY_METHOD（学习方法）、RESOURCE_RECOMMEND（资源推荐）
                3. 每条建议包含标题、内容、优先级（1-5）、目标知识点、推荐资源
                4. 建议要具体可行，符合学生的学习风格和水平
                
                ## 返回格式（JSON数组）：
                ```json
                [
                  {
                    "suggestionType": "KNOWLEDGE_GAP",
                    "title": "加强数据结构基础",
                    "content": "建议重点学习链表、栈、队列等基础数据结构...",
                    "priorityLevel": 5,
                    "targetKnowledgePoints": ["数据结构", "链表", "栈"],
                    "suggestedResources": [
                      {"type": "video", "title": "数据结构入门视频", "url": "http://example.com"},
                      {"type": "book", "title": "数据结构与算法", "author": "作者名"}
                    ]
                  }
                ]
                ```
                """, 
                profile.getLearningStyle(), 
                profile.getKnowledgeLevel(),
                profile.getWeakAreas(),
                profile.getStrongAreas(),
                profile.getLearningPreferences(),
                profile.getPerformanceTrends(),
                request.getSuggestionLimit());
    }

    /**
     * 解析AI生成的建议
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> parseAISuggestions(String suggestionsJson) {
        try {
            return objectMapper.readValue(suggestionsJson, new TypeReference<List<Map<String, Object>>>() {});
        } catch (Exception e) {
            log.warn("解析AI建议失败，返回默认建议: {}", e.getMessage());
            return getDefaultSuggestions();
        }
    }

    /**
     * 获取默认建议
     */
    private List<Map<String, Object>> getDefaultSuggestions() {
        Map<String, Object> suggestion = new HashMap<>();
        suggestion.put("suggestionType", "STUDY_METHOD");
        suggestion.put("title", "制定学习计划");
        suggestion.put("content", "建议制定详细的学习计划，合理安排学习时间，提高学习效率。");
        suggestion.put("priorityLevel", 3);
        suggestion.put("targetKnowledgePoints", Arrays.asList("学习方法", "时间管理"));
        suggestion.put("suggestedResources", new ArrayList<>());
        
        return Arrays.asList(suggestion);
    }

    /**
     * 保存建议到数据库
     */
    private List<PersonalizedLearningSuggestion> saveSuggestions(List<Map<String, Object>> aiSuggestions, 
                                                                PersonalizedSuggestionRequest request) {
        List<PersonalizedLearningSuggestion> suggestions = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (Map<String, Object> aiSuggestion : aiSuggestions) {
            try {
                PersonalizedLearningSuggestion suggestion = new PersonalizedLearningSuggestion()
                        .setStudentId(request.getStudentId())
                        .setTenantId(request.getTenantId())
                        .setSuggestionType((String) aiSuggestion.get("suggestionType"))
                        .setTitle((String) aiSuggestion.get("title"))
                        .setContent((String) aiSuggestion.get("content"))
                        .setPriorityLevel((Integer) aiSuggestion.get("priorityLevel"))
                        .setTargetKnowledgePoints(objectMapper.writeValueAsString(aiSuggestion.get("targetKnowledgePoints")))
                        .setSuggestedResources(objectMapper.writeValueAsString(aiSuggestion.get("suggestedResources")))
                        .setIsRead(false)
                        .setIsApplied(false)
                        .setCreateTime(now)
                        .setExpireTime(now.plusDays(30)); // 建议30天后过期
                
                suggestions.add(suggestion);
            } catch (Exception e) {
                log.warn("保存建议失败，跳过该建议: {}", e.getMessage());
            }
        }
        
        if (!suggestions.isEmpty()) {
            suggestionMapper.insertBatch(suggestions);
        }
        
        return suggestions;
    }

    /**
     * 构建响应对象
     */
    @SuppressWarnings("unchecked")
    private PersonalizedSuggestionResponse buildResponse(StudentLearningProfile profile, 
                                                        List<PersonalizedLearningSuggestion> suggestions) {
        PersonalizedSuggestionResponse response = new PersonalizedSuggestionResponse();
        response.setStudentId(profile.getStudentId());
        
        // 构建档案信息
        PersonalizedSuggestionResponse.LearningProfileInfo profileInfo = 
                new PersonalizedSuggestionResponse.LearningProfileInfo();
        profileInfo.setLearningStyle(profile.getLearningStyle());
        profileInfo.setKnowledgeLevel(profile.getKnowledgeLevel());
        
        try {
            profileInfo.setWeakAreas(objectMapper.readValue(profile.getWeakAreas(), List.class));
            profileInfo.setStrongAreas(objectMapper.readValue(profile.getStrongAreas(), List.class));
            
            Map<String, String> trends = objectMapper.readValue(profile.getPerformanceTrends(), Map.class);
            profileInfo.setPerformanceTrend(trends.get("description"));
        } catch (Exception e) {
            log.warn("解析档案信息失败: {}", e.getMessage());
            profileInfo.setWeakAreas(new ArrayList<>());
            profileInfo.setStrongAreas(new ArrayList<>());
            profileInfo.setPerformanceTrend("数据解析失败");
        }
        
        response.setProfileInfo(profileInfo);
        
        // 构建建议信息
        List<PersonalizedSuggestionResponse.SuggestionInfo> suggestionInfos = suggestions.stream()
                .map(this::convertToSuggestionInfo)
                .collect(Collectors.toList());
        response.setSuggestions(suggestionInfos);
        
        return response;
    }

    /**
     * 转换建议实体为响应对象
     */
    @SuppressWarnings("unchecked")
    private PersonalizedSuggestionResponse.SuggestionInfo convertToSuggestionInfo(PersonalizedLearningSuggestion suggestion) {
        PersonalizedSuggestionResponse.SuggestionInfo info = new PersonalizedSuggestionResponse.SuggestionInfo();
        info.setId(suggestion.getId());
        info.setSuggestionType(suggestion.getSuggestionType());
        info.setTitle(suggestion.getTitle());
        info.setContent(suggestion.getContent());
        info.setPriorityLevel(suggestion.getPriorityLevel());
        info.setExpireTime(suggestion.getExpireTime());
        
        try {
            info.setTargetKnowledgePoints(objectMapper.readValue(suggestion.getTargetKnowledgePoints(), List.class));
            info.setSuggestedResources(objectMapper.readValue(suggestion.getSuggestedResources(), List.class));
        } catch (Exception e) {
            log.warn("解析建议详情失败: {}", e.getMessage());
            info.setTargetKnowledgePoints(new ArrayList<>());
            info.setSuggestedResources(new ArrayList<>());
        }
        
        return info;
    }
}
