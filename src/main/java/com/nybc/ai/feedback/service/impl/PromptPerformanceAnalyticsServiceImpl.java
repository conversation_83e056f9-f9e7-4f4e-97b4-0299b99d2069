package com.nybc.ai.feedback.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.feedback.entity.PromptPerformanceAnalytics;
import com.nybc.ai.feedback.mapper.PromptPerformanceAnalyticsMapper;
import com.nybc.ai.feedback.service.PromptPerformanceAnalyticsService;
import com.nybc.ai.infra.ai.AiModelService;
import com.nybc.ai.service.UnifiedAiService;
import com.nybc.ai.prompt.domain.PromptExecutionLog;
import com.nybc.ai.prompt.mapper.PromptExecutionLogMapper;
import com.nybc.ai.service.PromptOptimizationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述：Prompt性能分析服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class PromptPerformanceAnalyticsServiceImpl implements PromptPerformanceAnalyticsService {

    @Resource
    private PromptPerformanceAnalyticsMapper analyticsMapper;

    @Resource
    private PromptExecutionLogMapper executionLogMapper;

    @Resource
    private UnifiedAiService unifiedAiService;

    @Resource
    private PromptOptimizationService promptOptimizationService;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${app.ai.default-provider:deepseek}")
    private String defaultAiProvider;

    @Override
    @Transactional
    public void analyzePromptPerformance(Long templateId, Long versionId, Long tenantId, String analysisPeriod) {
        try {
            log.info("开始分析Prompt性能: 模板ID[{}], 版本ID[{}], 分析周期[{}]", templateId, versionId, analysisPeriod);

            // 1. 计算分析时间范围
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = calculateStartTime(endTime, analysisPeriod);

            // 2. 收集执行日志数据
            List<PromptExecutionLog> executionLogs = collectExecutionLogs(templateId, versionId, tenantId, startTime, endTime);

            if (executionLogs.isEmpty()) {
                log.warn("指定时间范围内没有找到执行日志，跳过性能分析");
                return;
            }

            // 3. 计算性能指标
            Map<String, Object> performanceMetrics = calculatePerformanceMetrics(executionLogs);

            // 4. 使用AI分析性能模式和优化建议
            String analysisResult = analyzePerformancePatterns(performanceMetrics, executionLogs);
            Map<String, Object> analysisData = parseAnalysisResult(analysisResult);

            // 5. 保存分析结果
            savePerformanceAnalytics(templateId, versionId, tenantId, analysisPeriod, performanceMetrics, analysisData);

            log.info("Prompt性能分析完成: 模板ID[{}], 执行次数[{}]", templateId, executionLogs.size());

        } catch (Exception e) {
            log.error("分析Prompt性能失败: {}", e.getMessage(), e);
            throw new RuntimeException("分析Prompt性能失败", e);
        }
    }

    @Override
    public PromptPerformanceAnalytics getPerformanceAnalytics(Long templateId, Long versionId, 
                                                              Long tenantId, String analysisPeriod) {
        try {
            return analyticsMapper.findByTemplateAndPeriod(templateId, versionId, tenantId, analysisPeriod);
        } catch (Exception e) {
            log.error("获取性能分析结果失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取性能分析结果失败", e);
        }
    }

    @Override
    public List<PromptPerformanceAnalytics> getPerformanceTrend(Long templateId, Long tenantId, Integer periodCount) {
        try {
            return analyticsMapper.findTrendData(templateId, tenantId, periodCount);
        } catch (Exception e) {
            log.error("获取性能趋势数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取性能趋势数据失败", e);
        }
    }

    @Override
    @Transactional
    public Map<String, Object> autoOptimizePrompt(Long templateId, Long tenantId, Long operatorId) {
        try {
            log.info("开始基于性能分析自动优化Prompt: 模板ID[{}]", templateId);

            // 1. 获取最近的性能分析数据
            List<PromptPerformanceAnalytics> recentAnalytics = 
                    analyticsMapper.findRecentAnalytics(templateId, tenantId, 3);

            if (recentAnalytics.isEmpty()) {
                throw new IllegalStateException("没有足够的性能分析数据进行自动优化");
            }

            // 2. 分析性能问题
            Map<String, Object> performanceIssues = identifyPerformanceIssues(recentAnalytics);

            // 3. 生成优化建议
            String optimizationSuggestions = generateOptimizationSuggestions(performanceIssues);

            // 4. 如果性能问题严重，自动触发Prompt优化
            boolean shouldAutoOptimize = shouldTriggerAutoOptimization(performanceIssues);
            Map<String, Object> result = new HashMap<>();
            result.put("performanceIssues", performanceIssues);
            result.put("optimizationSuggestions", optimizationSuggestions);
            result.put("autoOptimized", shouldAutoOptimize);

            if (shouldAutoOptimize) {
                // 这里可以调用Prompt优化服务
                log.info("性能问题严重，建议手动触发Prompt优化");
                result.put("recommendation", "建议手动触发Prompt优化以改善性能");
            }

            log.info("Prompt自动优化分析完成: 模板ID[{}]", templateId);
            return result;

        } catch (Exception e) {
            log.error("自动优化Prompt失败: {}", e.getMessage(), e);
            throw new RuntimeException("自动优化Prompt失败", e);
        }
    }

    @Override
    public Long getBestPerformingVersion(Long templateId, Long tenantId) {
        try {
            return analyticsMapper.findBestPerformingVersion(templateId, tenantId);
        } catch (Exception e) {
            log.error("获取最佳性能版本失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取最佳性能版本失败", e);
        }
    }

    @Override
    public Map<Long, PromptPerformanceAnalytics> compareVersionPerformance(Long templateId, 
                                                                           List<Long> versionIds, 
                                                                           Long tenantId) {
        try {
            Map<Long, PromptPerformanceAnalytics> comparison = new HashMap<>();
            
            for (Long versionId : versionIds) {
                PromptPerformanceAnalytics analytics = 
                        analyticsMapper.findLatestByVersion(templateId, versionId, tenantId);
                if (analytics != null) {
                    comparison.put(versionId, analytics);
                }
            }
            
            return comparison;
            
        } catch (Exception e) {
            log.error("比较版本性能失败: {}", e.getMessage(), e);
            throw new RuntimeException("比较版本性能失败", e);
        }
    }

    /**
     * 计算开始时间
     */
    private LocalDateTime calculateStartTime(LocalDateTime endTime, String analysisPeriod) {
        return switch (analysisPeriod.toUpperCase()) {
            case "DAILY" -> endTime.minus(1, ChronoUnit.DAYS);
            case "WEEKLY" -> endTime.minus(7, ChronoUnit.DAYS);
            case "MONTHLY" -> endTime.minus(30, ChronoUnit.DAYS);
            default -> endTime.minus(7, ChronoUnit.DAYS);
        };
    }

    /**
     * 收集执行日志数据
     */
    private List<PromptExecutionLog> collectExecutionLogs(Long templateId, Long versionId, Long tenantId,
                                                          LocalDateTime startTime, LocalDateTime endTime) {
        if (versionId != null) {
            return executionLogMapper.findByVersionAndTimeRange(templateId, tenantId, versionId, startTime, endTime);
        } else {
            return executionLogMapper.findByTemplateAndTimeRange(templateId, tenantId, startTime, endTime);
        }
    }

    /**
     * 计算性能指标
     */
    private Map<String, Object> calculatePerformanceMetrics(List<PromptExecutionLog> executionLogs) {
        Map<String, Object> metrics = new HashMap<>();

        // 总执行次数
        int totalExecutions = executionLogs.size();
        metrics.put("totalExecutions", totalExecutions);

        // 成功率（假设没有异常的都是成功的）
        long successCount = executionLogs.stream()
                .filter(log -> log.getRawAiResponse() != null && !log.getRawAiResponse().isEmpty())
                .count();
        double successRate = totalExecutions > 0 ? (double) successCount / totalExecutions : 0.0;
        metrics.put("successRate", successRate);

        // 平均执行时间
        double avgExecutionTime = executionLogs.stream()
                .filter(log -> log.getExecutionTimeMs() != null)
                .mapToLong(PromptExecutionLog::getExecutionTimeMs)
                .average()
                .orElse(0.0);
        metrics.put("avgExecutionTime", avgExecutionTime);

        // 平均反馈评分
        OptionalDouble avgFeedbackScore = executionLogs.stream()
                .filter(log -> log.getFeedbackScore() != null)
                .mapToInt(PromptExecutionLog::getFeedbackScore)
                .average();
        if (avgFeedbackScore.isPresent()) {
            metrics.put("avgFeedbackScore", avgFeedbackScore.getAsDouble());
        }

        // 正面反馈率
        long totalFeedbacks = executionLogs.stream()
                .filter(log -> log.getFeedbackStatus() != null && !log.getFeedbackStatus().equals("PENDING"))
                .count();
        long positiveFeedbacks = executionLogs.stream()
                .filter(log -> "CORRECT".equals(log.getFeedbackStatus()))
                .count();
        double positiveFeedbackRate = totalFeedbacks > 0 ? (double) positiveFeedbacks / totalFeedbacks : 0.0;
        metrics.put("positiveFeedbackRate", positiveFeedbackRate);

        return metrics;
    }

    /**
     * 使用AI分析性能模式
     */
    private String analyzePerformancePatterns(Map<String, Object> performanceMetrics, 
                                             List<PromptExecutionLog> executionLogs) {
        String prompt = buildPerformanceAnalysisPrompt(performanceMetrics, executionLogs);
        return unifiedAiService.chat(prompt, Collections.singletonMap("temperature", 0.3));
    }

    /**
     * 构建性能分析的Prompt
     */
    private String buildPerformanceAnalysisPrompt(Map<String, Object> performanceMetrics, 
                                                 List<PromptExecutionLog> executionLogs) {
        // 分析失败案例的模式
        List<String> failurePatterns = executionLogs.stream()
                .filter(log -> "INCORRECT".equals(log.getFeedbackStatus()))
                .map(log -> log.getFeedbackNotes() != null ? log.getFeedbackNotes() : "无具体反馈")
                .limit(5)
                .collect(Collectors.toList());

        return String.format("""
                你是一位专业的AI系统性能分析师，请根据Prompt执行的性能数据进行深入分析。
                
                ## 性能指标：
                - 总执行次数: %s
                - 成功率: %.2f%%
                - 平均执行时间: %.2f毫秒
                - 平均反馈评分: %s
                - 正面反馈率: %.2f%%
                
                ## 失败案例反馈样本：
                %s
                
                ## 分析要求：
                请从以下维度进行分析，并以JSON格式返回结果：
                1. 常见失败模式（数组格式，总结失败的主要原因）
                2. 优化建议（数组格式，具体的改进建议）
                3. 性能趋势评估（对象格式，包含趋势方向和关键指标）
                
                ## 返回格式：
                ```json
                {
                  "commonFailurePatterns": [
                    "响应格式不符合要求",
                    "理解用户意图不准确"
                  ],
                  "optimizationSuggestions": [
                    "在Prompt中增加更明确的格式要求",
                    "添加更多的上下文信息"
                  ],
                  "performanceTrend": {
                    "direction": "improving/stable/declining",
                    "keyMetrics": {
                      "successRateChange": "+5%%",
                      "responseTimeChange": "-10%%"
                    },
                    "recommendations": "继续监控性能变化"
                  }
                }
                ```
                """,
                performanceMetrics.get("totalExecutions"),
                ((Number) performanceMetrics.get("successRate")).doubleValue() * 100,
                ((Number) performanceMetrics.get("avgExecutionTime")).doubleValue(),
                performanceMetrics.get("avgFeedbackScore"),
                ((Number) performanceMetrics.get("positiveFeedbackRate")).doubleValue() * 100,
                String.join("\n- ", failurePatterns));
    }

    /**
     * 解析AI分析结果
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseAnalysisResult(String analysisResult) {
        try {
            return objectMapper.readValue(analysisResult, Map.class);
        } catch (Exception e) {
            log.warn("解析AI分析结果失败，使用默认结果: {}", e.getMessage());
            return getDefaultAnalysisData();
        }
    }

    /**
     * 获取默认分析数据
     */
    private Map<String, Object> getDefaultAnalysisData() {
        Map<String, Object> data = new HashMap<>();
        data.put("commonFailurePatterns", Arrays.asList("数据解析失败", "无法获取详细分析"));
        data.put("optimizationSuggestions", Arrays.asList("建议检查Prompt格式", "增加错误处理机制"));
        
        Map<String, Object> trend = new HashMap<>();
        trend.put("direction", "stable");
        trend.put("keyMetrics", Map.of("successRateChange", "0%", "responseTimeChange", "0%"));
        trend.put("recommendations", "需要更多数据进行分析");
        data.put("performanceTrend", trend);
        
        return data;
    }

    /**
     * 保存性能分析结果
     */
    private void savePerformanceAnalytics(Long templateId, Long versionId, Long tenantId, String analysisPeriod,
                                         Map<String, Object> performanceMetrics, Map<String, Object> analysisData) {
        try {
            PromptPerformanceAnalytics analytics = new PromptPerformanceAnalytics()
                    .setTemplateId(templateId)
                    .setVersionId(versionId)
                    .setTenantId(tenantId)
                    .setAnalysisPeriod(analysisPeriod)
                    .setTotalExecutions((Integer) performanceMetrics.get("totalExecutions"))
                    .setSuccessRate((Double) performanceMetrics.get("successRate"))
                    .setAvgExecutionTime((Double) performanceMetrics.get("avgExecutionTime"))
                    .setAvgFeedbackScore((Double) performanceMetrics.get("avgFeedbackScore"))
                    .setPositiveFeedbackRate((Double) performanceMetrics.get("positiveFeedbackRate"))
                    .setCommonFailurePatterns(objectMapper.writeValueAsString(analysisData.get("commonFailurePatterns")))
                    .setOptimizationSuggestions(objectMapper.writeValueAsString(analysisData.get("optimizationSuggestions")))
                    .setPerformanceTrend(objectMapper.writeValueAsString(analysisData.get("performanceTrend")))
                    .setAnalysisTime(LocalDateTime.now());

            analyticsMapper.insert(analytics);

        } catch (Exception e) {
            log.error("保存性能分析结果失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存性能分析结果失败", e);
        }
    }

    /**
     * 识别性能问题
     */
    private Map<String, Object> identifyPerformanceIssues(List<PromptPerformanceAnalytics> recentAnalytics) {
        Map<String, Object> issues = new HashMap<>();
        
        // 计算平均指标
        double avgSuccessRate = recentAnalytics.stream()
                .mapToDouble(PromptPerformanceAnalytics::getSuccessRate)
                .average().orElse(0.0);
        
        double avgPositiveFeedbackRate = recentAnalytics.stream()
                .mapToDouble(PromptPerformanceAnalytics::getPositiveFeedbackRate)
                .average().orElse(0.0);
        
        double avgExecutionTime = recentAnalytics.stream()
                .mapToDouble(PromptPerformanceAnalytics::getAvgExecutionTime)
                .average().orElse(0.0);

        // 识别问题
        List<String> identifiedIssues = new ArrayList<>();
        if (avgSuccessRate < 0.8) {
            identifiedIssues.add("成功率偏低");
        }
        if (avgPositiveFeedbackRate < 0.7) {
            identifiedIssues.add("用户满意度不高");
        }
        if (avgExecutionTime > 5000) {
            identifiedIssues.add("响应时间过长");
        }

        issues.put("avgSuccessRate", avgSuccessRate);
        issues.put("avgPositiveFeedbackRate", avgPositiveFeedbackRate);
        issues.put("avgExecutionTime", avgExecutionTime);
        issues.put("identifiedIssues", identifiedIssues);
        issues.put("severityLevel", identifiedIssues.size() > 2 ? "HIGH" : identifiedIssues.size() > 0 ? "MEDIUM" : "LOW");

        return issues;
    }

    /**
     * 生成优化建议
     */
    private String generateOptimizationSuggestions(Map<String, Object> performanceIssues) {
        @SuppressWarnings("unchecked")
        List<String> issues = (List<String>) performanceIssues.get("identifiedIssues");
        
        if (issues.isEmpty()) {
            return "当前性能表现良好，建议继续监控";
        }

        StringBuilder suggestions = new StringBuilder("基于性能分析，建议：\n");
        for (String issue : issues) {
            switch (issue) {
                case "成功率偏低" -> suggestions.append("- 优化Prompt结构，增加更清晰的指令\n");
                case "用户满意度不高" -> suggestions.append("- 改进输出格式，提高回答质量\n");
                case "响应时间过长" -> suggestions.append("- 简化Prompt内容，减少不必要的复杂性\n");
            }
        }

        return suggestions.toString();
    }

    /**
     * 判断是否应该触发自动优化
     */
    private boolean shouldTriggerAutoOptimization(Map<String, Object> performanceIssues) {
        String severityLevel = (String) performanceIssues.get("severityLevel");
        return "HIGH".equals(severityLevel);
    }
}
