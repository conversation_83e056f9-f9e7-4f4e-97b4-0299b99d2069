package com.nybc.ai.feedback.service;

import com.nybc.ai.feedback.dto.PersonalizedSuggestionResponse;
import com.nybc.ai.feedback.dto.TeachingEffectivenessResponse;
import com.nybc.ai.rules.context.EvaluationContext;

import java.util.Map;

/**
 * 类描述：反馈优化服务接口
 * 整合个性化学习、教学效果分析和Prompt优化功能
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface FeedbackOptimizationService {

    /**
     * 基于作业评估结果生成闭环优化建议
     *
     * @param evaluationContext 作业评估上下文
     * @return 优化建议
     */
    Map<String, Object> generateClosedLoopOptimization(EvaluationContext evaluationContext);

    /**
     * 为学生生成基于评估结果的个性化学习建议
     *
     * @param studentId         学生ID
     * @param tenantId          租户ID
     * @param evaluationContext 评估上下文
     * @return 个性化学习建议
     */
    PersonalizedSuggestionResponse generatePostEvaluationSuggestions(Long studentId, Long tenantId, 
                                                                     EvaluationContext evaluationContext);

    /**
     * 分析教学效果并提供改进建议
     *
     * @param tenantId  租户ID
     * @param teacherId 教师ID
     * @return 教学效果分析结果
     */
    TeachingEffectivenessResponse analyzeTeachingEffectivenessWithOptimization(Long tenantId, Long teacherId);

    /**
     * 基于历史数据自动优化系统Prompt
     *
     * @param tenantId 租户ID
     * @return 优化结果
     */
    Map<String, Object> autoOptimizeSystemPrompts(Long tenantId);

    /**
     * 生成综合的反馈优化报告
     *
     * @param tenantId 租户ID
     * @return 综合优化报告
     */
    Map<String, Object> generateComprehensiveFeedbackReport(Long tenantId);
}
