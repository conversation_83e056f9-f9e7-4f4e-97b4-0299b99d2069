package com.nybc.ai.feedback.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.feedback.dto.TeachingEffectivenessRequest;
import com.nybc.ai.feedback.dto.TeachingEffectivenessResponse;
import com.nybc.ai.feedback.entity.TeachingEffectivenessReport;
import com.nybc.ai.feedback.mapper.TeachingEffectivenessReportMapper;
import com.nybc.ai.feedback.service.TeachingEffectivenessService;
import com.nybc.ai.infra.ai.AiModelService;
import com.nybc.ai.infra.ai.AiModelServiceFactory;
import com.nybc.ai.rules.context.EvaluationContext;
import com.nybc.ai.rules.mapper.RuleChainMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述：教学效果分析服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class TeachingEffectivenessServiceImpl implements TeachingEffectivenessService {

    @Resource
    private TeachingEffectivenessReportMapper reportMapper;

    @Resource
    private AiModelServiceFactory aiModelServiceFactory;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${app.ai.default-provider:deepseek}")
    private String defaultAiProvider;

    @Override
    @Transactional
    public TeachingEffectivenessResponse generateEffectivenessReport(TeachingEffectivenessRequest request) {
        try {
            log.info("开始生成教学效果分析报告: 租户ID[{}], 教师ID[{}], 报告类型[{}]", 
                    request.getTenantId(), request.getTeacherId(), request.getReportType());

            // 1. 收集教学数据
            Map<String, Object> teachingData = collectTeachingData(request);

            // 2. 使用AI分析教学效果
            String analysisResult = analyzeTeachingEffectiveness(teachingData, request);
            Map<String, Object> analysisData = parseAnalysisResult(analysisResult);

            // 3. 保存分析报告
            TeachingEffectivenessReport report = saveAnalysisReport(analysisData, request);

            // 4. 构建响应
            TeachingEffectivenessResponse response = buildResponse(report, analysisData);

            log.info("教学效果分析报告生成完成: 报告ID[{}]", report.getId());
            return response;

        } catch (Exception e) {
            log.error("生成教学效果分析报告失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成教学效果分析报告失败", e);
        }
    }

    @Override
    public List<TeachingEffectivenessReport> getHistoricalReports(Long tenantId, Long teacherId, Integer limit) {
        try {
            return reportMapper.findHistoricalReports(tenantId, teacherId, limit);
        } catch (Exception e) {
            log.error("获取历史教学效果报告失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取历史教学效果报告失败", e);
        }
    }

    @Override
    public TeachingEffectivenessResponse getReportDetail(Long reportId) {
        try {
            TeachingEffectivenessReport report = reportMapper.findById(reportId);
            if (report == null) {
                throw new IllegalArgumentException("找不到指定的报告: " + reportId);
            }

            Map<String, Object> analysisData = parseStoredAnalysisData(report);
            return buildResponse(report, analysisData);

        } catch (Exception e) {
            log.error("获取报告详情失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取报告详情失败", e);
        }
    }

    @Override
    public TeachingEffectivenessResponse compareTeachingEffectiveness(Long tenantId, Long teacherId, 
                                                                     String reportType, Integer periodCount) {
        try {
            log.info("开始比较教学效果: 租户ID[{}], 教师ID[{}], 周期数[{}]", tenantId, teacherId, periodCount);

            // 1. 获取历史报告
            List<TeachingEffectivenessReport> historicalReports = 
                    reportMapper.findReportsByTypeAndPeriod(tenantId, teacherId, reportType, periodCount);

            if (historicalReports.size() < 2) {
                throw new IllegalArgumentException("历史数据不足，无法进行比较分析");
            }

            // 2. 使用AI进行比较分析
            String comparisonResult = performComparisonAnalysis(historicalReports);
            Map<String, Object> comparisonData = parseAnalysisResult(comparisonResult);

            // 3. 构建比较分析响应
            TeachingEffectivenessResponse response = buildComparisonResponse(comparisonData, historicalReports);

            log.info("教学效果比较分析完成");
            return response;

        } catch (Exception e) {
            log.error("比较教学效果失败: {}", e.getMessage(), e);
            throw new RuntimeException("比较教学效果失败", e);
        }
    }

    /**
     * 收集教学数据
     */
    private Map<String, Object> collectTeachingData(TeachingEffectivenessRequest request) {
        Map<String, Object> data = new HashMap<>();

        // 设置时间范围
        LocalDateTime endTime = request.getEndTime() != null ? request.getEndTime() : LocalDateTime.now();
        LocalDateTime startTime = request.getStartTime() != null ? request.getStartTime() : 
                calculateStartTime(endTime, request.getReportPeriod());

        // 收集基础统计数据
        data.put("timeRange", Map.of("start", startTime, "end", endTime));
        data.put("tenantId", request.getTenantId());
        data.put("teacherId", request.getTeacherId());
        data.put("assignmentId", request.getAssignmentId());

        // 模拟收集教学数据（实际应用中应从相关表查询）
        data.put("totalStudents", 45);
        data.put("totalAssignments", 8);
        data.put("averageScore", 78.5);
        data.put("passRate", 0.82);
        data.put("excellentRate", 0.28);
        data.put("assignmentCompletionRate", 0.91);
        data.put("avgFeedbackTime", 24.5);

        // 分数分布
        Map<String, Integer> scoreDistribution = new HashMap<>();
        scoreDistribution.put("90-100", 12);
        scoreDistribution.put("80-89", 15);
        scoreDistribution.put("70-79", 10);
        scoreDistribution.put("60-69", 5);
        scoreDistribution.put("0-59", 3);
        data.put("scoreDistribution", scoreDistribution);

        // 知识点掌握情况
        Map<String, Double> knowledgePointMastery = new HashMap<>();
        knowledgePointMastery.put("Java基础", 0.85);
        knowledgePointMastery.put("面向对象", 0.78);
        knowledgePointMastery.put("数据结构", 0.65);
        knowledgePointMastery.put("算法", 0.58);
        data.put("knowledgePointMastery", knowledgePointMastery);

        return data;
    }

    /**
     * 计算开始时间
     */
    private LocalDateTime calculateStartTime(LocalDateTime endTime, String reportPeriod) {
        if (reportPeriod == null) {
            return endTime.minus(30, ChronoUnit.DAYS);
        }

        return switch (reportPeriod.toUpperCase()) {
            case "WEEKLY" -> endTime.minus(7, ChronoUnit.DAYS);
            case "MONTHLY" -> endTime.minus(30, ChronoUnit.DAYS);
            case "SEMESTER" -> endTime.minus(120, ChronoUnit.DAYS);
            default -> endTime.minus(30, ChronoUnit.DAYS);
        };
    }

    /**
     * 使用AI分析教学效果
     */
    private String analyzeTeachingEffectiveness(Map<String, Object> teachingData, TeachingEffectivenessRequest request) {
        String prompt = buildTeachingAnalysisPrompt(teachingData, request);
        AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
        return aiService.call(prompt, Collections.singletonMap("temperature", 0.3));
    }

    /**
     * 构建教学分析的Prompt
     */
    private String buildTeachingAnalysisPrompt(Map<String, Object> teachingData, TeachingEffectivenessRequest request) {
        return String.format("""
                你是一位专业的教育数据分析师，请根据提供的教学数据进行深入分析，并给出教学效果评估和改进建议。
                
                ## 教学数据：
                - 报告类型: %s
                - 报告周期: %s
                - 学生总数: %s
                - 作业总数: %s
                - 平均分数: %s
                - 及格率: %s
                - 优秀率: %s
                - 作业完成率: %s
                - 平均反馈时间: %s小时
                - 分数分布: %s
                - 知识点掌握情况: %s
                
                ## 分析要求：
                请从以下维度进行分析，并以JSON格式返回结果：
                1. 关键洞察（数组格式，3-5条重要发现）
                2. 改进建议（数组格式，每条包含类型、内容、优先级、预期效果）
                3. 性能指标（对象格式，包含教学质量评分、学生参与度、学生满意度等）
                4. 学生反馈汇总（对象格式，包含总反馈数、正面反馈率、常见问题、学生建议）
                
                ## 返回格式：
                ```json
                {
                  "keyInsights": [
                    "学生在数据结构方面掌握程度较低，需要加强基础训练",
                    "作业完成率较高，说明学生学习积极性良好"
                  ],
                  "improvementSuggestions": [
                    {
                      "suggestionType": "TEACHING_METHOD",
                      "content": "建议增加数据结构的实践练习",
                      "priority": 5,
                      "expectedEffect": "提高学生对数据结构的理解和应用能力"
                    }
                  ],
                  "performanceMetrics": {
                    "teachingQualityScore": 82.5,
                    "studentEngagement": 78.0,
                    "studentSatisfaction": 85.0
                  },
                  "studentFeedbackSummary": {
                    "totalFeedbacks": 35,
                    "positiveFeedbackRate": 0.8,
                    "commonIssues": ["概念理解困难", "练习时间不足"],
                    "studentSuggestions": ["增加实例讲解", "提供更多练习题"]
                  }
                }
                ```
                """,
                request.getReportType(),
                request.getReportPeriod(),
                teachingData.get("totalStudents"),
                teachingData.get("totalAssignments"),
                teachingData.get("averageScore"),
                teachingData.get("passRate"),
                teachingData.get("excellentRate"),
                teachingData.get("assignmentCompletionRate"),
                teachingData.get("avgFeedbackTime"),
                teachingData.get("scoreDistribution"),
                teachingData.get("knowledgePointMastery"));
    }

    /**
     * 解析AI分析结果
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseAnalysisResult(String analysisResult) {
        try {
            return objectMapper.readValue(analysisResult, Map.class);
        } catch (Exception e) {
            log.warn("解析AI分析结果失败，使用默认结果: {}", e.getMessage());
            return getDefaultAnalysisData();
        }
    }

    /**
     * 获取默认分析数据
     */
    private Map<String, Object> getDefaultAnalysisData() {
        Map<String, Object> data = new HashMap<>();
        
        data.put("keyInsights", Arrays.asList(
                "教学数据分析中，发现学生整体表现良好",
                "部分知识点需要加强练习和巩固"
        ));
        
        List<Map<String, Object>> suggestions = new ArrayList<>();
        Map<String, Object> suggestion = new HashMap<>();
        suggestion.put("suggestionType", "TEACHING_METHOD");
        suggestion.put("content", "建议增加互动式教学环节");
        suggestion.put("priority", 3);
        suggestion.put("expectedEffect", "提高学生参与度和学习效果");
        suggestions.add(suggestion);
        data.put("improvementSuggestions", suggestions);
        
        Map<String, Double> metrics = new HashMap<>();
        metrics.put("teachingQualityScore", 75.0);
        metrics.put("studentEngagement", 70.0);
        metrics.put("studentSatisfaction", 80.0);
        data.put("performanceMetrics", metrics);
        
        Map<String, Object> feedbackSummary = new HashMap<>();
        feedbackSummary.put("totalFeedbacks", 20);
        feedbackSummary.put("positiveFeedbackRate", 0.75);
        feedbackSummary.put("commonIssues", Arrays.asList("理解困难", "时间不足"));
        feedbackSummary.put("studentSuggestions", Arrays.asList("增加练习", "详细讲解"));
        data.put("studentFeedbackSummary", feedbackSummary);
        
        return data;
    }

    /**
     * 保存分析报告
     */
    private TeachingEffectivenessReport saveAnalysisReport(Map<String, Object> analysisData, 
                                                          TeachingEffectivenessRequest request) {
        try {
            TeachingEffectivenessReport report = new TeachingEffectivenessReport()
                    .setTenantId(request.getTenantId())
                    .setTeacherId(request.getTeacherId())
                    .setAssignmentId(request.getAssignmentId())
                    .setReportType(request.getReportType())
                    .setReportPeriod(request.getReportPeriod())
                    .setAnalysisData(objectMapper.writeValueAsString(analysisData))
                    .setKeyInsights(objectMapper.writeValueAsString(analysisData.get("keyInsights")))
                    .setImprovementSuggestions(objectMapper.writeValueAsString(analysisData.get("improvementSuggestions")))
                    .setPerformanceMetrics(objectMapper.writeValueAsString(analysisData.get("performanceMetrics")))
                    .setStudentFeedbackSummary(objectMapper.writeValueAsString(analysisData.get("studentFeedbackSummary")))
                    .setGenerateTime(LocalDateTime.now())
                    .setCreateUser(request.getCreateUser());

            reportMapper.insert(report);
            return report;

        } catch (Exception e) {
            log.error("保存分析报告失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存分析报告失败", e);
        }
    }

    /**
     * 构建响应对象
     */
    @SuppressWarnings("unchecked")
    private TeachingEffectivenessResponse buildResponse(TeachingEffectivenessReport report, 
                                                       Map<String, Object> analysisData) {
        TeachingEffectivenessResponse response = new TeachingEffectivenessResponse();
        response.setReportId(report.getId());
        response.setReportType(report.getReportType());
        response.setReportPeriod(report.getReportPeriod());
        response.setGenerateTime(report.getGenerateTime());

        try {
            // 构建分析数据
            TeachingEffectivenessResponse.AnalysisData analysisDataObj = 
                    new TeachingEffectivenessResponse.AnalysisData();
            // 这里应该从实际的教学数据中获取，暂时使用模拟数据
            analysisDataObj.setTotalStudents(45);
            analysisDataObj.setTotalAssignments(8);
            analysisDataObj.setAverageScore(78.5);
            analysisDataObj.setPassRate(0.82);
            analysisDataObj.setExcellentRate(0.28);
            response.setAnalysisData(analysisDataObj);

            // 设置关键洞察
            List<String> keyInsights = (List<String>) analysisData.get("keyInsights");
            response.setKeyInsights(keyInsights);

            // 构建改进建议
            List<Map<String, Object>> suggestions = (List<Map<String, Object>>) analysisData.get("improvementSuggestions");
            List<TeachingEffectivenessResponse.ImprovementSuggestion> improvementSuggestions = 
                    suggestions.stream().map(this::convertToImprovementSuggestion).collect(Collectors.toList());
            response.setImprovementSuggestions(improvementSuggestions);

            // 构建性能指标
            Map<String, Object> metricsData = (Map<String, Object>) analysisData.get("performanceMetrics");
            TeachingEffectivenessResponse.PerformanceMetrics metrics = 
                    new TeachingEffectivenessResponse.PerformanceMetrics();
            metrics.setTeachingQualityScore(((Number) metricsData.get("teachingQualityScore")).doubleValue());
            metrics.setStudentEngagement(((Number) metricsData.get("studentEngagement")).doubleValue());
            metrics.setStudentSatisfaction(((Number) metricsData.get("studentSatisfaction")).doubleValue());
            metrics.setAssignmentCompletionRate(0.91);
            metrics.setAvgFeedbackTime(24.5);
            response.setPerformanceMetrics(metrics);

            // 构建学生反馈汇总
            Map<String, Object> feedbackData = (Map<String, Object>) analysisData.get("studentFeedbackSummary");
            TeachingEffectivenessResponse.StudentFeedbackSummary feedbackSummary = 
                    new TeachingEffectivenessResponse.StudentFeedbackSummary();
            feedbackSummary.setTotalFeedbacks(((Number) feedbackData.get("totalFeedbacks")).intValue());
            feedbackSummary.setPositiveFeedbackRate(((Number) feedbackData.get("positiveFeedbackRate")).doubleValue());
            feedbackSummary.setCommonIssues((List<String>) feedbackData.get("commonIssues"));
            feedbackSummary.setStudentSuggestions((List<String>) feedbackData.get("studentSuggestions"));
            response.setStudentFeedbackSummary(feedbackSummary);

        } catch (Exception e) {
            log.warn("构建响应对象失败: {}", e.getMessage());
            // 设置默认值
            response.setKeyInsights(Arrays.asList("数据解析失败"));
            response.setImprovementSuggestions(new ArrayList<>());
        }

        return response;
    }

    /**
     * 转换改进建议
     */
    private TeachingEffectivenessResponse.ImprovementSuggestion convertToImprovementSuggestion(Map<String, Object> data) {
        TeachingEffectivenessResponse.ImprovementSuggestion suggestion = 
                new TeachingEffectivenessResponse.ImprovementSuggestion();
        suggestion.setSuggestionType((String) data.get("suggestionType"));
        suggestion.setContent((String) data.get("content"));
        suggestion.setPriority(((Number) data.get("priority")).intValue());
        suggestion.setExpectedEffect((String) data.get("expectedEffect"));
        return suggestion;
    }

    /**
     * 解析存储的分析数据
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> parseStoredAnalysisData(TeachingEffectivenessReport report) {
        try {
            return objectMapper.readValue(report.getAnalysisData(), Map.class);
        } catch (Exception e) {
            log.warn("解析存储的分析数据失败: {}", e.getMessage());
            return getDefaultAnalysisData();
        }
    }

    /**
     * 执行比较分析
     */
    private String performComparisonAnalysis(List<TeachingEffectivenessReport> historicalReports) {
        String prompt = buildComparisonAnalysisPrompt(historicalReports);
        AiModelService aiService = aiModelServiceFactory.getService(defaultAiProvider);
        return aiService.call(prompt, Collections.singletonMap("temperature", 0.3));
    }

    /**
     * 构建比较分析的Prompt
     */
    private String buildComparisonAnalysisPrompt(List<TeachingEffectivenessReport> reports) {
        StringBuilder reportSummary = new StringBuilder();
        for (int i = 0; i < reports.size(); i++) {
            TeachingEffectivenessReport report = reports.get(i);
            reportSummary.append(String.format("## 报告%d（%s）：\n", i + 1, report.getGenerateTime()));
            reportSummary.append(String.format("- 报告类型: %s\n", report.getReportType()));
            reportSummary.append(String.format("- 关键洞察: %s\n", report.getKeyInsights()));
            reportSummary.append(String.format("- 性能指标: %s\n\n", report.getPerformanceMetrics()));
        }

        return String.format("""
                你是一位专业的教育数据分析师，请对以下多个时期的教学效果报告进行比较分析。
                
                %s
                
                ## 分析要求：
                1. 比较不同时期的教学效果变化趋势
                2. 识别改进的方面和仍需关注的问题
                3. 提供基于趋势的未来改进建议
                4. 以JSON格式返回比较分析结果
                
                ## 返回格式：
                ```json
                {
                  "trendAnalysis": {
                    "overallTrend": "improving/stable/declining",
                    "keyChanges": ["变化1", "变化2"],
                    "improvementAreas": ["改进方面1", "改进方面2"],
                    "concernAreas": ["关注点1", "关注点2"]
                  },
                  "performanceComparison": {
                    "teachingQualityTrend": "上升/稳定/下降",
                    "studentEngagementTrend": "上升/稳定/下降",
                    "satisfactionTrend": "上升/稳定/下降"
                  },
                  "futureRecommendations": [
                    {
                      "area": "教学方法",
                      "recommendation": "具体建议",
                      "priority": 5
                    }
                  ]
                }
                ```
                """, reportSummary.toString());
    }

    /**
     * 构建比较分析响应
     */
    @SuppressWarnings("unchecked")
    private TeachingEffectivenessResponse buildComparisonResponse(Map<String, Object> comparisonData, 
                                                                 List<TeachingEffectivenessReport> reports) {
        TeachingEffectivenessResponse response = new TeachingEffectivenessResponse();
        response.setReportType("COMPARISON");
        response.setGenerateTime(LocalDateTime.now());

        try {
            Map<String, Object> trendAnalysis = (Map<String, Object>) comparisonData.get("trendAnalysis");
            List<String> keyInsights = new ArrayList<>();
            keyInsights.add("整体趋势: " + trendAnalysis.get("overallTrend"));
            keyInsights.addAll((List<String>) trendAnalysis.get("keyChanges"));
            response.setKeyInsights(keyInsights);

            List<Map<String, Object>> recommendations = (List<Map<String, Object>>) comparisonData.get("futureRecommendations");
            List<TeachingEffectivenessResponse.ImprovementSuggestion> suggestions = 
                    recommendations.stream().map(this::convertToImprovementSuggestion).collect(Collectors.toList());
            response.setImprovementSuggestions(suggestions);

        } catch (Exception e) {
            log.warn("构建比较分析响应失败: {}", e.getMessage());
            response.setKeyInsights(Arrays.asList("比较分析数据解析失败"));
            response.setImprovementSuggestions(new ArrayList<>());
        }

        return response;
    }
}
