package com.nybc.ai.feedback.service.impl;

import com.nybc.ai.feedback.dto.PersonalizedSuggestionRequest;
import com.nybc.ai.feedback.dto.PersonalizedSuggestionResponse;
import com.nybc.ai.feedback.dto.TeachingEffectivenessRequest;
import com.nybc.ai.feedback.dto.TeachingEffectivenessResponse;
import com.nybc.ai.feedback.entity.StudentLearningBehavior;
import com.nybc.ai.feedback.enums.LearningBehaviorType;
import com.nybc.ai.feedback.service.FeedbackOptimizationService;
import com.nybc.ai.feedback.service.PersonalizedLearningService;
import com.nybc.ai.feedback.service.PromptPerformanceAnalyticsService;
import com.nybc.ai.feedback.service.TeachingEffectivenessService;
import com.nybc.ai.rules.context.EvaluationContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 类描述：反馈优化服务实现类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Service
public class FeedbackOptimizationServiceImpl implements FeedbackOptimizationService {

    @Resource
    private PersonalizedLearningService personalizedLearningService;

    @Resource
    private TeachingEffectivenessService teachingEffectivenessService;

    @Resource
    private PromptPerformanceAnalyticsService promptPerformanceAnalyticsService;

    @Override
    public Map<String, Object> generateClosedLoopOptimization(EvaluationContext evaluationContext) {
        try {
            log.info("开始生成闭环优化建议: 学生ID[{}], 租户ID[{}]", 
                    evaluationContext.getStudentId(), evaluationContext.getTenantId());

            Map<String, Object> optimization = new HashMap<>();

            // 1. 记录评估行为
            recordEvaluationBehavior(evaluationContext);

            // 2. 分析评估结果，识别改进点
            Map<String, Object> improvementAreas = analyzeEvaluationResults(evaluationContext);
            optimization.put("improvementAreas", improvementAreas);

            // 3. 生成个性化学习建议
            if (evaluationContext.getStudentId() != null) {
                PersonalizedSuggestionResponse suggestions = generatePostEvaluationSuggestions(
                        evaluationContext.getStudentId(), 
                        evaluationContext.getTenantId(), 
                        evaluationContext);
                optimization.put("personalizedSuggestions", suggestions);
            }

            // 4. 分析Prompt性能（如果使用了AI评估）
            if (evaluationContext.getChainName() != null) {
                Map<String, Object> promptAnalysis = analyzePromptPerformance(evaluationContext);
                optimization.put("promptAnalysis", promptAnalysis);
            }

            // 5. 生成教学改进建议
            Map<String, Object> teachingImprovements = generateTeachingImprovements(evaluationContext);
            optimization.put("teachingImprovements", teachingImprovements);

            optimization.put("generateTime", LocalDateTime.now());
            optimization.put("optimizationType", "CLOSED_LOOP");

            log.info("闭环优化建议生成完成: 学生ID[{}]", evaluationContext.getStudentId());
            return optimization;

        } catch (Exception e) {
            log.error("生成闭环优化建议失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成闭环优化建议失败", e);
        }
    }

    @Override
    public PersonalizedSuggestionResponse generatePostEvaluationSuggestions(Long studentId, Long tenantId, 
                                                                            EvaluationContext evaluationContext) {
        try {
            log.info("基于评估结果生成个性化建议: 学生ID[{}], 最终得分[{}]", 
                    studentId, evaluationContext.getFinalScore());

            // 记录提交作业行为
            StudentLearningBehavior behavior = new StudentLearningBehavior()
                    .setStudentId(studentId)
                    .setTenantId(tenantId)
                    .setAssignmentId(evaluationContext.getAssignmentId())
                    .setBehaviorType(LearningBehaviorType.SUBMIT.name())
                    .setBehaviorData(buildBehaviorData(evaluationContext))
                    .setCreateTime(LocalDateTime.now());
            
            personalizedLearningService.recordLearningBehavior(behavior);

            // 生成个性化建议
            PersonalizedSuggestionRequest request = new PersonalizedSuggestionRequest()
                    .setStudentId(studentId)
                    .setTenantId(tenantId)
                    .setAssignmentId(evaluationContext.getAssignmentId())
                    .setForceReanalyze(true) // 基于最新评估结果重新分析
                    .setSuggestionLimit(8);

            return personalizedLearningService.generatePersonalizedSuggestions(request);

        } catch (Exception e) {
            log.error("生成评估后个性化建议失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成评估后个性化建议失败", e);
        }
    }

    @Override
    public TeachingEffectivenessResponse analyzeTeachingEffectivenessWithOptimization(Long tenantId, Long teacherId) {
        try {
            log.info("分析教学效果并提供优化建议: 租户ID[{}], 教师ID[{}]", tenantId, teacherId);

            TeachingEffectivenessRequest request = new TeachingEffectivenessRequest()
                    .setTenantId(tenantId)
                    .setTeacherId(teacherId)
                    .setReportType("OPTIMIZATION")
                    .setReportPeriod("MONTHLY")
                    .setCreateUser(0L); // 系统自动生成

            return teachingEffectivenessService.generateEffectivenessReport(request);

        } catch (Exception e) {
            log.error("分析教学效果失败: {}", e.getMessage(), e);
            throw new RuntimeException("分析教学效果失败", e);
        }
    }

    @Override
    public Map<String, Object> autoOptimizeSystemPrompts(Long tenantId) {
        try {
            log.info("开始自动优化系统Prompt: 租户ID[{}]", tenantId);

            Map<String, Object> result = new HashMap<>();
            
            // 这里应该获取租户相关的所有Prompt模板
            // 暂时使用示例逻辑
            result.put("optimizedPrompts", 0);
            result.put("analyzedPrompts", 0);
            result.put("recommendations", "暂无需要优化的Prompt");
            result.put("optimizationTime", LocalDateTime.now());

            log.info("系统Prompt自动优化完成: 租户ID[{}]", tenantId);
            return result;

        } catch (Exception e) {
            log.error("自动优化系统Prompt失败: {}", e.getMessage(), e);
            throw new RuntimeException("自动优化系统Prompt失败", e);
        }
    }

    @Override
    public Map<String, Object> generateComprehensiveFeedbackReport(Long tenantId) {
        try {
            log.info("生成综合反馈优化报告: 租户ID[{}]", tenantId);

            Map<String, Object> report = new HashMap<>();

            // 1. 教学效果分析
            TeachingEffectivenessResponse teachingAnalysis = 
                    analyzeTeachingEffectivenessWithOptimization(tenantId, null);
            report.put("teachingEffectiveness", teachingAnalysis);

            // 2. Prompt性能分析
            Map<String, Object> promptOptimization = autoOptimizeSystemPrompts(tenantId);
            report.put("promptOptimization", promptOptimization);

            // 3. 系统整体优化建议
            Map<String, Object> systemOptimization = generateSystemOptimizationSuggestions(tenantId);
            report.put("systemOptimization", systemOptimization);

            report.put("reportType", "COMPREHENSIVE_FEEDBACK");
            report.put("generateTime", LocalDateTime.now());
            report.put("tenantId", tenantId);

            log.info("综合反馈优化报告生成完成: 租户ID[{}]", tenantId);
            return report;

        } catch (Exception e) {
            log.error("生成综合反馈优化报告失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成综合反馈优化报告失败", e);
        }
    }

    /**
     * 记录评估行为
     */
    private void recordEvaluationBehavior(EvaluationContext evaluationContext) {
        if (evaluationContext.getStudentId() != null) {
            StudentLearningBehavior behavior = new StudentLearningBehavior()
                    .setStudentId(evaluationContext.getStudentId())
                    .setTenantId(evaluationContext.getTenantId())
                    .setAssignmentId(evaluationContext.getAssignmentId())
                    .setBehaviorType("EVALUATION_COMPLETED")
                    .setBehaviorData(buildBehaviorData(evaluationContext))
                    .setCreateTime(LocalDateTime.now());
            
            personalizedLearningService.recordLearningBehavior(behavior);
        }
    }

    /**
     * 构建行为数据
     */
    private String buildBehaviorData(EvaluationContext evaluationContext) {
        return String.format("""
                {
                  "finalScore": %.2f,
                  "chainName": "%s",
                  "submissionType": "%s",
                  "syntaxErrorCount": %d,
                  "cyclomaticComplexity": %.2f,
                  "plagiarismScore": %.2f
                }
                """,
                evaluationContext.getFinalScore(),
                evaluationContext.getChainName(),
                evaluationContext.getSubmissionType(),
                evaluationContext.getSyntaxErrorCount(),
                evaluationContext.getCyclomaticComplexity(),
                evaluationContext.getPlagiarismScore());
    }

    /**
     * 分析评估结果
     */
    private Map<String, Object> analyzeEvaluationResults(EvaluationContext evaluationContext) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 分析得分情况
        double finalScore = evaluationContext.getFinalScore();
        if (finalScore >= 90) {
            analysis.put("performanceLevel", "EXCELLENT");
        } else if (finalScore >= 80) {
            analysis.put("performanceLevel", "GOOD");
        } else if (finalScore >= 70) {
            analysis.put("performanceLevel", "AVERAGE");
        } else {
            analysis.put("performanceLevel", "NEEDS_IMPROVEMENT");
        }

        // 识别主要问题
        if (evaluationContext.getSyntaxErrorCount() > 0) {
            analysis.put("syntaxIssues", true);
        }
        if (evaluationContext.getPlagiarismScore() > 0.3) {
            analysis.put("plagiarismConcern", true);
        }
        if (evaluationContext.getCyclomaticComplexity() > 10) {
            analysis.put("complexityIssue", true);
        }

        return analysis;
    }

    /**
     * 分析Prompt性能
     */
    private Map<String, Object> analyzePromptPerformance(EvaluationContext evaluationContext) {
        Map<String, Object> analysis = new HashMap<>();
        
        // 这里应该基于实际的Prompt执行情况进行分析
        analysis.put("promptEffectiveness", "GOOD");
        analysis.put("recommendOptimization", false);
        analysis.put("analysisTime", LocalDateTime.now());
        
        return analysis;
    }

    /**
     * 生成教学改进建议
     */
    private Map<String, Object> generateTeachingImprovements(EvaluationContext evaluationContext) {
        Map<String, Object> improvements = new HashMap<>();
        
        // 基于评估结果生成教学改进建议
        if (evaluationContext.getFinalScore() < 70) {
            improvements.put("suggestion", "建议加强基础知识讲解");
            improvements.put("priority", "HIGH");
        } else {
            improvements.put("suggestion", "继续保持当前教学质量");
            improvements.put("priority", "LOW");
        }
        
        return improvements;
    }

    /**
     * 生成系统优化建议
     */
    private Map<String, Object> generateSystemOptimizationSuggestions(Long tenantId) {
        Map<String, Object> suggestions = new HashMap<>();
        
        suggestions.put("dataQuality", "GOOD");
        suggestions.put("systemPerformance", "OPTIMAL");
        suggestions.put("userSatisfaction", "HIGH");
        suggestions.put("recommendedActions", "继续监控系统性能");
        
        return suggestions;
    }
}
