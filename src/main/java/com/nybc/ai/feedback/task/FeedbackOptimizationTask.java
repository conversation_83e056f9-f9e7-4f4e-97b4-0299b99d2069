package com.nybc.ai.feedback.task;

import com.nybc.ai.feedback.service.PromptPerformanceAnalyticsService;
import com.nybc.ai.prompt.mapper.PromptTemplateMapper;
import com.nybc.ai.prompt.domain.PromptTemplate;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 类描述：反馈优化定时任务
 * 定期执行Prompt性能分析和自动优化
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Component
public class FeedbackOptimizationTask {

    @Resource
    private PromptPerformanceAnalyticsService promptPerformanceAnalyticsService;

    @Resource
    private PromptTemplateMapper promptTemplateMapper;

    /**
     * 每日执行Prompt性能分析
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyPromptPerformanceAnalysis() {
        log.info("开始执行每日Prompt性能分析任务");
        
        try {
            // 获取所有活跃的Prompt模板
            List<PromptTemplate> activeTemplates = promptTemplateMapper.findActiveTemplates();
            
            for (PromptTemplate template : activeTemplates) {
                try {
                    // 执行日度性能分析
                    promptPerformanceAnalyticsService.analyzePromptPerformance(
                            template.getId(), null, template.getTenantId(), "DAILY");
                    
                    log.debug("完成模板[{}]的日度性能分析", template.getId());
                    
                } catch (Exception e) {
                    log.error("模板[{}]的日度性能分析失败: {}", template.getId(), e.getMessage());
                }
            }
            
            log.info("每日Prompt性能分析任务完成，处理模板数量: {}", activeTemplates.size());
            
        } catch (Exception e) {
            log.error("每日Prompt性能分析任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每周执行Prompt性能分析和自动优化检查
     * 每周一凌晨3点执行
     */
    @Scheduled(cron = "0 0 3 ? * MON")
    public void weeklyPromptOptimizationCheck() {
        log.info("开始执行每周Prompt优化检查任务");
        
        try {
            // 获取所有活跃的Prompt模板
            List<PromptTemplate> activeTemplates = promptTemplateMapper.findActiveTemplates();
            
            for (PromptTemplate template : activeTemplates) {
                try {
                    // 执行周度性能分析
                    promptPerformanceAnalyticsService.analyzePromptPerformance(
                            template.getId(), null, template.getTenantId(), "WEEKLY");
                    
                    // 检查是否需要自动优化
                    var optimizationResult = promptPerformanceAnalyticsService.autoOptimizePrompt(
                            template.getId(), template.getTenantId(), 0L); // 系统自动操作
                    
                    Boolean autoOptimized = (Boolean) optimizationResult.get("autoOptimized");
                    if (Boolean.TRUE.equals(autoOptimized)) {
                        log.info("模板[{}]被标记为需要优化", template.getId());
                    }
                    
                    log.debug("完成模板[{}]的周度优化检查", template.getId());
                    
                } catch (Exception e) {
                    log.error("模板[{}]的周度优化检查失败: {}", template.getId(), e.getMessage());
                }
            }
            
            log.info("每周Prompt优化检查任务完成，处理模板数量: {}", activeTemplates.size());
            
        } catch (Exception e) {
            log.error("每周Prompt优化检查任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每月执行全面的性能分析
     * 每月1号凌晨4点执行
     */
    @Scheduled(cron = "0 0 4 1 * ?")
    public void monthlyComprehensiveAnalysis() {
        log.info("开始执行每月全面性能分析任务");
        
        try {
            // 获取所有活跃的Prompt模板
            List<PromptTemplate> activeTemplates = promptTemplateMapper.findActiveTemplates();
            
            for (PromptTemplate template : activeTemplates) {
                try {
                    // 执行月度性能分析
                    promptPerformanceAnalyticsService.analyzePromptPerformance(
                            template.getId(), null, template.getTenantId(), "MONTHLY");
                    
                    // 获取性能趋势数据
                    var trendData = promptPerformanceAnalyticsService.getPerformanceTrend(
                            template.getId(), template.getTenantId(), 6); // 最近6个月的趋势
                    
                    log.debug("完成模板[{}]的月度全面分析，趋势数据点数: {}", 
                            template.getId(), trendData.size());
                    
                } catch (Exception e) {
                    log.error("模板[{}]的月度全面分析失败: {}", template.getId(), e.getMessage());
                }
            }
            
            log.info("每月全面性能分析任务完成，处理模板数量: {}", activeTemplates.size());
            
        } catch (Exception e) {
            log.error("每月全面性能分析任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理过期的性能分析数据
     * 每周日凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 ? * SUN")
    public void cleanupExpiredAnalyticsData() {
        log.info("开始执行过期性能分析数据清理任务");
        
        try {
            // 这里可以添加清理逻辑，比如删除6个月前的日度分析数据
            // 保留重要的月度和周度数据
            
            log.info("过期性能分析数据清理任务完成");
            
        } catch (Exception e) {
            log.error("过期性能分析数据清理任务执行失败: {}", e.getMessage(), e);
        }
    }
}
