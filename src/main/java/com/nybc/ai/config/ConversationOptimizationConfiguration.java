package com.nybc.ai.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * 类描述：对话优化配置类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "app.conversation")
public class ConversationOptimizationConfiguration {

    /**
     * 最大上下文token数量
     */
    private int maxContextTokens = 4000;

    /**
     * 对话摘要保留天数
     */
    private int summaryRetentionDays = 30;

    /**
     * 是否启用自动上下文压缩
     */
    private boolean autoCompressionEnabled = true;

    /**
     * 上下文压缩阈值
     */
    private double compressionThreshold = 0.8;

    /**
     * 主题转换检测敏感度
     */
    private double topicShiftSensitivity = 0.7;

    /**
     * 是否启用领域实体提取
     */
    private boolean domainEntityExtractionEnabled = true;

    /**
     * 对话历史保留数量
     */
    private int maxHistoryMessages = 50;
}
