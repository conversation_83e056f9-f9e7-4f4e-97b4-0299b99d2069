package com.nybc.ai.config.mybatis;


import com.nybc.ai.config.mybatis.plugin.MultiTenantInterceptor;
import com.nybc.ai.config.tenant.TenantIdProvider;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Configuration
@Slf4j
@EnableTransactionManagement
public class MybatisConfig {

    /**
     * 配置类型别名
     */
    @Value("${mybatis.typeAliasesPackage}")
    private String typeAliasesPackage;
    /**
     * 配置mapper的扫描，找到所有的mapper.xml映射文件
     */
    @Value("${mybatis.mapperLocations}")
    private String mapperLocations;

    /**
     * 加载全局的配置文件
     */
    @Value("${mybatis.configLocation}")
    private String configLocation;

    @Resource
    private DataSource dataSource;

    @Resource
    private TenantIdProvider tenantIdProvider;

    /**
     * 提供SqlSeesion
     */
    @Bean(name = "sqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean sessionFactoryBean = new SqlSessionFactoryBean();
        sessionFactoryBean.setDataSource(dataSource);
        sessionFactoryBean.setTypeAliasesPackage(typeAliasesPackage);
        sessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(mapperLocations));
        sessionFactoryBean.setConfigLocation(new DefaultResourceLoader().getResource(configLocation));
        sessionFactoryBean.addPlugins(new SqlPrintInterceptor());
        sessionFactoryBean.addPlugins(new MultiTenantInterceptor(tenantIdProvider));

        // 注册自定义 TypeHandler
        SqlSessionFactory factory = sessionFactoryBean.getObject();
        if (factory != null) {
            TypeHandlerRegistry typeHandlerRegistry = factory.getConfiguration().getTypeHandlerRegistry();
            log.info("mybatis sqlSessionFactoryBean create success");
            return factory;
        } else {
            // 这种情况理论上不应该发生，如果 sessionFactoryBean.getObject() 返回 null 但没抛异常
            log.error("SqlSessionFactoryBean.getObject() returned null without throwing an exception.");
            throw new RuntimeException("Failed to create SqlSessionFactory: getObject() returned null.");
        }
    }


    @Bean
    public SqlSessionTemplate sqlSessionTemplate(SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }


}