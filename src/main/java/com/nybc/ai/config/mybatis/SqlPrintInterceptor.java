package com.nybc.edu.core.mybatis;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.ParameterMode;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 类描述：MyBatis SQL 慢查询监控拦截器 (PostgreSQL优化版)
 * 只记录执行时间超过500ms的慢SQL，展示完整可读的SQL语句和耗时
 * 专门针对PostgreSQL数据库进行优化，支持PostgreSQL特有数据类型和语法
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
})
@Slf4j
public class SqlPrintInterceptor implements Interceptor {

    /**
     * 慢SQL执行时间阈值（毫秒），超过此阈值才记录日志
     */
    private static final long SLOW_SQL_THRESHOLD_MS = 500L;

    /**
     * 用于替换SQL中的问号占位符的正则表达式
     */
    private static final Pattern PARAMETER_REGEX = Pattern.compile("\\?");

    /**
     * PostgreSQL SQL美化正则表达式：在关键字前添加换行
     * 包含PostgreSQL特有关键字如RETURNING、WITH、WINDOW等
     */
    private static final Pattern SQL_FORMAT_REGEX = Pattern.compile(
            "\\b(SELECT|FROM|WHERE|AND|OR|ORDER BY|GROUP BY|HAVING|LIMIT|OFFSET|" +
                    "INSERT|UPDATE|DELETE|SET|VALUES|RETURNING|" +
                    "WITH|RECURSIVE|WINDOW|PARTITION BY|OVER|" +
                    "UNION|INTERSECT|EXCEPT|" +
                    "LEFT JOIN|RIGHT JOIN|INNER JOIN|FULL OUTER JOIN|CROSS JOIN|" +
                    "CASE|WHEN|THEN|ELSE|END)\\b",
            Pattern.CASE_INSENSITIVE);

    /**
     * PostgreSQL兼容的日期时间格式化器
     */
    private static final DateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    private static final DateTimeFormatter LOCAL_DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private static final DateTimeFormatter LOCAL_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter OFFSET_DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSXXX");
    private static final DateTimeFormatter ZONED_DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS VV");

    /**
     * 格式化参数值为PostgreSQL兼容的SQL字面量
     * 针对PostgreSQL特有数据类型进行优化
     *
     * @param value               参数值
     * @param typeHandlerRegistry MyBatis的TypeHandler注册中心
     * @return 格式化后的PostgreSQL兼容SQL字面量字符串
     */
    private static String getParameterValue(Object value, TypeHandlerRegistry typeHandlerRegistry) {
        if (value == null) {
            return "NULL";
        }

        // 性能优化：按PostgreSQL中使用频率排序instanceof检查
        if (value instanceof String) {
            return "'" + escapePostgreSql(value.toString()) + "'";
        } else if (value instanceof Number) {
            return value.toString();
        } else if (value instanceof Boolean) {
            // PostgreSQL布尔值
            return ((Boolean) value) ? "true" : "false";
        } else if (value instanceof UUID) {
            // PostgreSQL UUID类型
            return "'" + value + "'::uuid";
        } else if (value instanceof Date) {
            return "'" + DATE_FORMAT.format(value) + "'::timestamp";
        } else if (value instanceof Timestamp) {
            return "'" + DATE_FORMAT.format(value) + "'::timestamp";
        } else if (value instanceof LocalDateTime) {
            return "'" + LOCAL_DATE_TIME_FORMAT.format((LocalDateTime) value) + "'::timestamp";
        } else if (value instanceof LocalDate) {
            return "'" + LOCAL_DATE_FORMAT.format((LocalDate) value) + "'::date";
        } else if (value instanceof OffsetDateTime) {
            // PostgreSQL timestamptz类型
            return "'" + OFFSET_DATE_TIME_FORMAT.format((OffsetDateTime) value) + "'::timestamptz";
        } else if (value instanceof ZonedDateTime) {
            // PostgreSQL timestamptz类型
            return "'" + ZONED_DATE_TIME_FORMAT.format((ZonedDateTime) value) + "'::timestamptz";
        } else if (value instanceof byte[]) {
            // PostgreSQL bytea类型
            return "E'\\\\x" + bytesToHex((byte[]) value) + "'::bytea";
        } else if (value.getClass().isArray()) {
            // PostgreSQL数组类型
            return formatPostgreSqlArray(value);
        } else if (typeHandlerRegistry.hasTypeHandler(value.getClass())) {
            return "'" + escapePostgreSql(value.toString()) + "'";
        } else {
            return "'" + escapePostgreSql(value.toString()) + "'";
        }
    }

    /**
     * PostgreSQL字符串转义
     * 处理PostgreSQL特有的转义规则
     *
     * @param sql 要转义的字符串
     * @return 转义后的字符串
     */
    private static String escapePostgreSql(String sql) {
        if (sql == null) {
            return null;
        }
        // PostgreSQL字符串转义：单引号转义为两个单引号
        // 反斜杠需要转义（如果使用标准SQL字符串）
        return sql.replace("'", "''").replace("\\", "\\\\");
    }

    /**
     * 将字节数组转换为十六进制字符串（用于PostgreSQL bytea类型）
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 格式化PostgreSQL数组类型
     *
     * @param arrayValue 数组值
     * @return PostgreSQL数组字面量
     */
    private static String formatPostgreSqlArray(Object arrayValue) {
        if (arrayValue instanceof int[] array) {
            StringBuilder sb = new StringBuilder("ARRAY[");
            for (int i = 0; i < array.length; i++) {
                if (i > 0) {
                    sb.append(",");
                }
                sb.append(array[i]);
            }
            sb.append("]");
            return sb.toString();
        } else if (arrayValue instanceof String[] array) {
            StringBuilder sb = new StringBuilder("ARRAY[");
            for (int i = 0; i < array.length; i++) {
                if (i > 0) {
                    sb.append(",");
                }
                sb.append("'").append(escapePostgreSql(array[i])).append("'");
            }
            sb.append("]");
            return sb.toString();
        } else {
            // 其他数组类型的通用处理
            return "'" + escapePostgreSql(arrayValue.toString()) + "'";
        }
    }

    /**
     * 美化PostgreSQL SQL格式，提升可读性
     * 针对PostgreSQL语法特点进行优化
     *
     * @param sql 原始SQL
     * @return 格式化后的SQL
     */
    private static String formatSql(String sql) {
        if (sql == null) {
            return null;
        }

        // 移除多余的空白字符
        String cleanSql = sql.replaceAll("\\s+", " ").trim();

        // 在PostgreSQL关键字前添加换行和缩进，增强可读性
        Matcher matcher = SQL_FORMAT_REGEX.matcher(cleanSql);
        String formattedSql = matcher.replaceAll("\n    $1");

        // 移除开头的换行
        if (formattedSql.startsWith("\n")) {
            formattedSql = formattedSql.substring(1);
        }

        // PostgreSQL特有的格式化：处理类型转换操作符::
        formattedSql = formattedSql.replaceAll("\\s*::\\s*", "::");

        return formattedSql;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object returnValue;

        try {
            // 执行原始SQL
            returnValue = invocation.proceed();
        } finally {
            long elapsedTime = System.currentTimeMillis() - startTime;

            // 只记录超过阈值的慢SQL
            if (elapsedTime >= SLOW_SQL_THRESHOLD_MS) {
                try {
                    logSlowSql(invocation, elapsedTime);
                } catch (Exception logEx) {
                    // 确保日志记录异常不影响主流程
                    log.error("记录PostgreSQL慢SQL日志时发生异常: {}", logEx.getMessage());
                }
            }
        }

        return returnValue;
    }

    /**
     * 记录PostgreSQL慢SQL日志
     *
     * @param invocation  方法调用信息
     * @param elapsedTime 执行耗时
     */
    private void logSlowSql(Invocation invocation, long elapsedTime) {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs().length > 1 ? invocation.getArgs()[1] : null;

        // 获取BoundSql
        BoundSql boundSql = getBoundSql(invocation, mappedStatement, parameter);
        if (boundSql == null) {
            return;
        }

        Configuration configuration = mappedStatement.getConfiguration();
        String statementId = mappedStatement.getId();

        // 构建完整可读的PostgreSQL SQL
        String completeSql = buildCompleteSql(boundSql, configuration);
        String formattedSql = formatSql(completeSql);

        // 记录慢SQL日志，标明是PostgreSQL
        log.warn("\n[PostgreSQL慢SQL] 执行耗时: {}ms\nMapper: {}\nSQL:\n{}",
                 elapsedTime, statementId, formattedSql);
    }

    /**
     * 根据方法签名获取BoundSql
     *
     * @param invocation      方法调用信息
     * @param mappedStatement Mapper语句
     * @param parameter       参数
     * @return BoundSql对象
     */
    private BoundSql getBoundSql(Invocation invocation, MappedStatement mappedStatement, Object parameter) {
        Object[] args = invocation.getArgs();

        if (args.length == 6 && args[5] instanceof BoundSql) {
            // query(MappedStatement, Object, RowBounds, ResultHandler, CacheKey, BoundSql) 签名
            return (BoundSql) args[5];
        } else if (args.length == 4 && args[3] instanceof ResultHandler) {
            // query(MappedStatement, Object, RowBounds, ResultHandler) 签名
            return mappedStatement.getBoundSql(parameter);
        } else if (args.length == 2) {
            // update(MappedStatement, Object) 签名
            return mappedStatement.getBoundSql(parameter);
        } else {
            // fallback
            return mappedStatement.getBoundSql(parameter);
        }
    }

    /**
     * 构建完整的可执行PostgreSQL SQL（将参数替换进SQL中）
     *
     * @param boundSql      BoundSql对象
     * @param configuration MyBatis配置
     * @return 完整的PostgreSQL SQL字符串
     */
    private String buildCompleteSql(BoundSql boundSql, Configuration configuration) {
        String sql = boundSql.getSql();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        Object parameterObject = boundSql.getParameterObject();

        if (CollectionUtils.isEmpty(parameterMappings) || parameterObject == null) {
            return sql;
        }

        TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
        List<String> parameterValues = new ArrayList<>(parameterMappings.size());
        MetaObject metaObject = configuration.newMetaObject(parameterObject);

        // 收集所有参数值，使用PostgreSQL兼容格式
        for (ParameterMapping parameterMapping : parameterMappings) {
            if (parameterMapping.getMode() != ParameterMode.OUT) {
                String propertyName = parameterMapping.getProperty();
                Object value = getParameterObjectValue(boundSql, metaObject, propertyName, parameterObject);
                parameterValues.add(getParameterValue(value, typeHandlerRegistry));
            }
        }

        // 替换SQL中的参数占位符
        return replaceParameters(sql, parameterValues);
    }

    /**
     * 获取参数对象的值
     *
     * @param boundSql        BoundSql对象
     * @param metaObject      元对象
     * @param propertyName    属性名
     * @param parameterObject 参数对象
     * @return 参数值
     */
    private Object getParameterObjectValue(BoundSql boundSql, MetaObject metaObject, String propertyName, Object parameterObject) {
        if (boundSql.hasAdditionalParameter(propertyName)) {
            return boundSql.getAdditionalParameter(propertyName);
        } else if (metaObject.hasGetter(propertyName)) {
            return metaObject.getValue(propertyName);
        } else {
            return parameterObject;
        }
    }

    /**
     * 替换SQL中的参数占位符
     *
     * @param sql             原始SQL
     * @param parameterValues 参数值列表
     * @return 替换参数后的SQL
     */
    private String replaceParameters(String sql, List<String> parameterValues) {
        if (parameterValues.isEmpty()) {
            return sql;
        }

        Matcher matcher = PARAMETER_REGEX.matcher(sql);
        StringBuilder sb = new StringBuilder();
        int paramIndex = 0;

        while (matcher.find() && paramIndex < parameterValues.size()) {
            matcher.appendReplacement(sb, parameterValues.get(paramIndex));
            paramIndex++;
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以从配置文件读取PostgreSQL特定的配置参数
        // 例如：时间阈值、是否启用类型转换显示等
    }

}
