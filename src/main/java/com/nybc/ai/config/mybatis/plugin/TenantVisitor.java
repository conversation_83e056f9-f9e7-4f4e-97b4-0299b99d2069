package com.nybc.edu.core.mybatis.plugin;

import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.SelectVisitor;
import net.sf.jsqlparser.util.TablesNamesFinder;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 租户访问者 - 使用JSQLParser官方推荐的访问者模式
 * 
 * 优势：
 * 1. 代码简洁：只需覆盖关心的方法，JSQLParser自动遍历整个AST
 * 2. 逻辑健壮：不会遗漏任何嵌套层次的表
 * 3. 性能优秀：JSQLParser内部优化的遍历算法
 * 4. 易于维护：符合访问者模式的标准实现
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class TenantVisitor extends TablesNamesFinder {

    private static final String TENANT_ID_COLUMN = "tenant_id";
    
    private final Long tenantId;
    private final Set<String> excludedTables;
    private final Map<String, Boolean> tableMatchCache;

    public TenantVisitor(Long tenantId, Set<String> excludedTables, Map<String, Boolean> tableMatchCache) {
        this.tenantId = tenantId;
        this.excludedTables = excludedTables;
        this.tableMatchCache = tableMatchCache;
    }

    /**
     * 核心方法：访问每个表
     * JSQLParser会自动遍历所有SQL结构，包括：
     * - 主表和JOIN表
     * - 所有层级的子查询
     * - WHERE/HAVING子句中的子查询
     * - EXISTS、IN等子查询
     * - WITH子句中的表
     * - UNION中的所有SELECT
     */
    @Override
    public void visit(Table table) {
        // 调用父类方法，确保正常的表名收集
        super.visit(table);
        
        // 检查是否需要租户隔离
        if (isTenantTable(table)) {
            log.debug("Found tenant table: {}", table.getName());
            // 这里我们只是标记，实际的条件添加在PlainSelect中处理
        }
    }

    /**
     * 访问PlainSelect，为需要租户隔离的表添加条件
     */
    @Override
    public void visit(PlainSelect plainSelect) {
        // 先让父类处理，收集所有表名
        super.visit(plainSelect);
        
        // 为当前SELECT添加租户条件
        addTenantConditionsToSelect(plainSelect);
    }

    /**
     * 为SELECT语句添加租户条件
     */
    private void addTenantConditionsToSelect(PlainSelect plainSelect) {
        try {
            // 收集当前SELECT中的所有表
            TenantTableCollector collector = new TenantTableCollector(excludedTables, tableMatchCache);
            // 明确指定使用SelectVisitor接口
            plainSelect.accept((SelectVisitor) collector);

            // 为需要租户隔离的表创建条件
            Expression tenantCondition = createTenantConditionsForTables(collector.getTenantTables());
            
            if (tenantCondition != null) {
                // 合并到现有WHERE条件
                Expression currentWhere = plainSelect.getWhere();
                if (currentWhere == null) {
                    plainSelect.setWhere(tenantCondition);
                } else {
                    plainSelect.setWhere(new AndExpression(currentWhere, tenantCondition));
                }
                
                log.debug("Added tenant conditions to PlainSelect");
            }
        } catch (Exception e) {
            log.debug("Failed to add tenant conditions to PlainSelect", e);
        }
    }

    /**
     * 为多个表创建租户条件
     */
    private Expression createTenantConditionsForTables(Set<Table> tables) {
        if (tables.isEmpty()) {
            return null;
        }

        Expression combined = null;
        for (Table table : tables) {
            Expression condition = createTenantCondition(table);
            if (combined == null) {
                combined = condition;
            } else {
                combined = new AndExpression(combined, condition);
            }
        }
        
        return combined;
    }

    /**
     * 为单个表创建租户条件
     */
    private Expression createTenantCondition(Table table) {
        String tableAlias = table.getAlias() != null ? 
                           table.getAlias().getName() : table.getName();

        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(new Column(tableAlias + "." + TENANT_ID_COLUMN));
        equalsTo.setRightExpression(new LongValue(tenantId));
        return equalsTo;
    }

    /**
     * 判断表是否需要租户隔离
     */
    private boolean isTenantTable(Table table) {
        String tableName = cleanTableName(table.getName());
        return tableMatchCache.computeIfAbsent(tableName, this::checkTenantTable);
    }

    /**
     * 检查表是否需要租户隔离（黑名单模式）
     * 除了在排除列表中的表，其他所有表都需要租户隔离
     */
    private boolean checkTenantTable(String tableName) {
        return !excludedTables.contains(tableName);
    }

    /**
     * 清理表名
     */
    private String cleanTableName(String tableName) {
        if (tableName.contains(".")) {
            tableName = tableName.substring(tableName.lastIndexOf(".") + 1);
        }
        return tableName.replaceAll("[\"'`]", "").toLowerCase();
    }

    /**
     * 租户表收集器 - 专门收集需要租户隔离的表
     */
    private static class TenantTableCollector extends TablesNamesFinder {
        private final Set<String> excludedTables;
        private final Map<String, Boolean> tableMatchCache;
        private final Set<Table> collectedTenantTables = ConcurrentHashMap.newKeySet();

        public TenantTableCollector(Set<String> excludedTables, Map<String, Boolean> tableMatchCache) {
            this.excludedTables = excludedTables;
            this.tableMatchCache = tableMatchCache;
        }

        @Override
        public void visit(Table table) {
            super.visit(table);
            
            if (isTenantTable(table)) {
                collectedTenantTables.add(table);
            }
        }

        private boolean isTenantTable(Table table) {
            String tableName = cleanTableName(table.getName());
            return tableMatchCache.computeIfAbsent(tableName, this::checkTenantTable);
        }

        private boolean checkTenantTable(String tableName) {
            return !excludedTables.contains(tableName);
        }

        private String cleanTableName(String tableName) {
            if (tableName.contains(".")) {
                tableName = tableName.substring(tableName.lastIndexOf(".") + 1);
            }
            return tableName.replaceAll("[\"'`]", "").toLowerCase();
        }

        public Set<Table> getTenantTables() {
            return collectedTenantTables;
        }
    }
}
