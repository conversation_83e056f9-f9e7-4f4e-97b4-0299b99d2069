package com.nybc.ai.config.mybatis.plugin;

import com.nybc.ai.config.tenant.NoTenant;
import com.nybc.ai.config.tenant.TenantIdProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.StatementVisitorAdapter;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 多租户拦截器，自动为SQL语句添加租户隔离条件
 * <p>
 * 优化特点：
 * 1. 缓存优化：缓存@NoTenant注解检查和表名匹配结果，避免重复反射和字符串处理
 * 2. 功能完整：支持复杂SQL（子查询、WITH子句、UNION、JOIN）
 * 3. API兼容：兼容JSQLParser不同版本的API变化（如getSelectBody弃用）
 * 4. 错误处理：完善的异常处理，SQL解析失败时使用原SQL，不影响业务
 * 5. 配置灵活：保持原有的表名配置方式，支持指定表的租户隔离
 *
 * <AUTHOR>
 * @version 2.0 (优化版)
 */
@Slf4j
@RequiredArgsConstructor
@Intercepts({@Signature(
        type = StatementHandler.class,
        method = "prepare",
        args = {Connection.class, Integer.class}
)})
public class MultiTenantInterceptor implements Interceptor {

    private static final String TENANT_ID_COLUMN = "tenant_id";

    /**
     * 租户隔离排除表黑名单 - 不需要租户隔离的表
     * 除了在此黑名单中的表之外，其他所有表都会自动添加租户隔离条件
     */
    private static final Set<String> EXCLUDED_TABLES = Set.of(
            "t_tenant_info",
            "t_tenant_school"
    );
    private final TenantIdProvider tenantIdProvider;


    private final Map<String, Boolean> noTenantCache = new ConcurrentHashMap<>();

    /**
     * 表名匹配结果缓存
     */
    private final Map<String, Boolean> tableMatchCache = new ConcurrentHashMap<>();

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");

        if (hasNoTenantAnnotation(mappedStatement)) {
            return invocation.proceed();
        }

        BoundSql boundSql = statementHandler.getBoundSql();
        String originalSql = boundSql.getSql();
        Long tenantId = tenantIdProvider.getTenantId();

        if (tenantId == null) {
            return invocation.proceed();
        }

        String modifiedSql = addTenantIdToSql(originalSql, tenantId);

        if (!originalSql.equals(modifiedSql)) {
            log.debug("Modified SQL for tenant [{}]: {}", tenantId, modifiedSql);
            metaObject.setValue("delegate.boundSql.sql", modifiedSql);
        }

        return invocation.proceed();
    }

    /**
     * 缓存优化的@NoTenant注解检查
     */
    private boolean hasNoTenantAnnotation(MappedStatement mappedStatement) {
        String id = mappedStatement.getId();
        return noTenantCache.computeIfAbsent(id, this::checkNoTenantAnnotation);
    }

    /**
     * 检查@NoTenant注解
     */
    private boolean checkNoTenantAnnotation(String mappedStatementId) {
        try {
            String className = mappedStatementId.substring(0, mappedStatementId.lastIndexOf('.'));
            String methodName = mappedStatementId.substring(mappedStatementId.lastIndexOf('.') + 1);
            Class<?> clazz = Class.forName(className);

            return Arrays.stream(clazz.getMethods())
                    .filter(method -> method.getName().equals(methodName))
                    .anyMatch(method -> method.isAnnotationPresent(NoTenant.class));
        } catch (Exception e) {
            log.debug("Could not check for @NoTenant annotation on {}", mappedStatementId, e);
            return false;
        }
    }

    private String addTenantIdToSql(String sql, Long tenantId) {
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                processSelect((Select) statement, tenantId);
            } else if (statement instanceof Insert) {
                processInsert((Insert) statement, tenantId);
            } else if (statement instanceof Update) {
                processUpdate((Update) statement, tenantId);
            } else if (statement instanceof Delete) {
                processDelete((Delete) statement, tenantId);
            }
            return statement.toString();
        } catch (Exception e) {
            // 改进错误处理，不影响业务
            log.warn("Failed to modify SQL for tenant isolation, using original SQL. Error: {}", e.getMessage());
            log.debug("SQL that failed to parse: {}", sql, e);
            return sql; // 返回原SQL，确保业务不受影响
        }
    }

    /**
     * 处理SELECT语句 - 使用访问者模式（JSQLParser官方推荐）
     *
     * 优势：
     * 1. 代码简洁：只需一行代码，JSQLParser自动遍历整个AST
     * 2. 逻辑健壮：不会遗漏任何嵌套层次的表（子查询、WITH、UNION等）
     * 3. 性能优秀：JSQLParser内部优化的遍历算法
     */
    private void processSelect(Select select, Long tenantId) {
        try {
            // 使用访问者模式 - JSQLParser会自动遍历所有SQL结构
            TenantVisitor visitor = new TenantVisitor(tenantId, EXCLUDED_TABLES, tableMatchCache);
            // 明确指定使用SelectVisitor接口
            select.accept((SelectVisitor) visitor);

            log.debug("Successfully processed SELECT using visitor pattern for tenant: {}", tenantId);
        } catch (Exception e) {
            // 降级到原有处理方式
            log.debug("Visitor pattern failed, falling back to legacy processing", e);
            processSelectLegacy(select, tenantId);
        }
    }

    /**
     * 降级处理方式（保持向后兼容）
     */
    private void processSelectLegacy(Select select, Long tenantId) {
        // 处理WITH子句
        if (select.getWithItemsList() != null) {
            select.getWithItemsList().forEach(withItem -> {
                try {
                    Object selectBody = withItem.getClass().getMethod("getSelect").invoke(withItem);
                    if (selectBody instanceof Select) {
                        processSelect((Select) selectBody, tenantId);
                    }
                } catch (Exception e) {
                    log.debug("Failed to process WITH clause", e);
                }
            });
        }

        // 处理主查询体
        try {
            Object selectBodyObj = select.getClass().getMethod("getSelectBody").invoke(select);
            if (selectBodyObj instanceof PlainSelect) {
                processPlainSelect((PlainSelect) selectBodyObj, tenantId);
            } else if (selectBodyObj instanceof SetOperationList setOp) {
                for (Object body : setOp.getSelects()) {
                    if (body instanceof PlainSelect) {
                        processPlainSelect((PlainSelect) body, tenantId);
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Could not access SelectBody, API may have changed", e);
        }
    }

    private void processPlainSelect(PlainSelect plainSelect, Long tenantId) {
        // 收集所有需要租户隔离的表
        List<Table> tenantTables = collectTenantTables(plainSelect);

        if (tenantTables.isEmpty()) {
            return; // 没有需要租户隔离的表
        }

        // 为每个表创建租户条件
        List<Expression> tenantConditions = new ArrayList<>();
        for (Table table : tenantTables) {
            Expression condition = createTenantConditionForTable(table, tenantId);
            tenantConditions.add(condition);
        }

        // 组合所有租户条件
        Expression combinedTenantCondition = combineConditions(tenantConditions);

        // 合并到现有WHERE条件
        Expression currentWhere = plainSelect.getWhere();
        if (currentWhere == null) {
            plainSelect.setWhere(combinedTenantCondition);
        } else {
            plainSelect.setWhere(new AndExpression(currentWhere, combinedTenantCondition));
        }

        // 处理子查询
        processSubQueries(plainSelect, tenantId);
    }

    /**
     * 处理子查询
     */
    private void processSubQueries(PlainSelect plainSelect, Long tenantId) {
        // 处理FROM子句中的子查询
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem != null && "SubSelect".equals(fromItem.getClass().getSimpleName())) {
            try {
                Object selectBodyObj = fromItem.getClass().getMethod("getSelectBody").invoke(fromItem);
                if (selectBodyObj instanceof PlainSelect) {
                    processPlainSelect((PlainSelect) selectBodyObj, tenantId);
                }
            } catch (Exception e) {
                log.debug("Could not process FROM subquery", e);
            }
        }

        // 处理JOIN中的子查询
        if (plainSelect.getJoins() != null) {
            for (Join join : plainSelect.getJoins()) {
                FromItem rightItem = join.getRightItem();
                if (rightItem != null && "SubSelect".equals(rightItem.getClass().getSimpleName())) {
                    try {
                        Object selectBodyObj = rightItem.getClass().getMethod("getSelectBody").invoke(rightItem);
                        if (selectBodyObj instanceof PlainSelect) {
                            processPlainSelect((PlainSelect) selectBodyObj, tenantId);
                        }
                    } catch (Exception e) {
                        log.debug("Could not process JOIN subquery", e);
                    }
                }
            }
        }
    }

    /**
     * 收集所有需要租户隔离的表（包括JOIN表）
     */
    private List<Table> collectTenantTables(PlainSelect plainSelect) {
        List<Table> tenantTables = new ArrayList<>();

        // 处理主表
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem instanceof Table table) {
            if (isTenantTable(table)) {
                tenantTables.add(table);
            }
        }

        // 处理JOIN表
        if (plainSelect.getJoins() != null) {
            for (Join join : plainSelect.getJoins()) {
                FromItem joinItem = join.getRightItem();
                if (joinItem instanceof Table joinTable) {
                    if (isTenantTable(joinTable)) {
                        tenantTables.add(joinTable);
                    }
                }
            }
        }

        return tenantTables;
    }

    /**
     * 判断表是否需要租户隔离 - 缓存优化版
     */
    private boolean isTenantTable(Table table) {
        String tableName = cleanTableName(table.getName());
        return tableMatchCache.computeIfAbsent(tableName, this::checkTenantTable);
    }

    /**
     * 检查表是否需要租户隔离（黑名单模式）
     * 除了在排除列表中的表，其他所有表都需要租户隔离
     */
    private boolean checkTenantTable(String tableName) {
        return !EXCLUDED_TABLES.contains(tableName);
    }

    /**
     * 清理表名
     */
    private String cleanTableName(String tableName) {
        // 去除schema前缀和引号，统一小写
        if (tableName.contains(".")) {
            tableName = tableName.substring(tableName.lastIndexOf(".") + 1);
        }
        return tableName.replaceAll("[\"'`]", "").toLowerCase();
    }

    /**
     * 创建特定表的租户条件
     */
    private Expression createTenantConditionForTable(Table table, Long tenantId) {
        String tableAlias = table.getAlias() != null ?
                            table.getAlias().getName() : table.getName();

        EqualsTo equalsTo = new EqualsTo();
        equalsTo.setLeftExpression(new Column(tableAlias + "." + TENANT_ID_COLUMN));
        equalsTo.setRightExpression(new LongValue(tenantId));
        return equalsTo;
    }

    /**
     * 组合多个条件（使用AND连接）
     */
    private Expression combineConditions(List<Expression> conditions) {
        if (conditions.isEmpty()) {
            return null;
        }
        if (conditions.size() == 1) {
            return conditions.getFirst();
        }

        Expression combined = conditions.getFirst();
        for (int i = 1; i < conditions.size(); i++) {
            combined = new AndExpression(combined, conditions.get(i));
        }
        return combined;
    }

    /**
     * 处理INSERT语句 - 支持INSERT ... SELECT
     */
    private void processInsert(Insert insert, Long tenantId) {
        // 检查表是否需要租户隔离
        if (insert.getTable() == null || !isTenantTable(insert.getTable())) {
            return;
        }

        // 处理INSERT ... SELECT语句
        if (insert.getSelect() != null) {
            processInsertSelect(insert, tenantId);
            return;
        }

        // 处理普通INSERT VALUES语句
        processInsertValues(insert, tenantId);
    }

    /**
     * 处理INSERT ... SELECT语句
     */
    private void processInsertSelect(Insert insert, Long tenantId) {
        try {
            // 1. 为INSERT表添加tenant_id列（如果还没有）
            if (insert.getColumns() != null && insert.getColumns().stream()
                    .noneMatch(c -> TENANT_ID_COLUMN.equalsIgnoreCase(c.getColumnName()))) {
                insert.addColumns(new Column(TENANT_ID_COLUMN));

                // 2. 为SELECT子句添加tenant_id值
                Select select = insert.getSelect();
                addTenantIdToSelectForInsert(select, tenantId);
            }

            // 3. 处理SELECT子句中的租户隔离（确保FROM表也有租户过滤）
            processSelect(insert.getSelect(), tenantId);

            log.debug("Processed INSERT ... SELECT statement for tenant: {}", tenantId);
        } catch (Exception e) {
            log.debug("Could not process INSERT ... SELECT statement", e);
        }
    }

    /**
     * 为INSERT ... SELECT的SELECT子句添加tenant_id值
     */
    private void addTenantIdToSelectForInsert(Select select, Long tenantId) {
        try {
            Object selectBodyObj = select.getClass().getMethod("getSelectBody").invoke(select);
            if (selectBodyObj instanceof PlainSelect plainSelect) {
                // 在SELECT列表中添加tenant_id值
                if (plainSelect.getSelectItems() != null) {
                    // 使用反射创建SelectExpressionItem，兼容不同版本
                    Class<?> selectExpressionItemClass = Class.forName("net.sf.jsqlparser.statement.select.SelectExpressionItem");
                    Object tenantItem = selectExpressionItemClass.getDeclaredConstructor().newInstance();

                    // 设置表达式
                    selectExpressionItemClass.getMethod("setExpression", net.sf.jsqlparser.expression.Expression.class)
                            .invoke(tenantItem, new LongValue(tenantId));

                    // 设置别名
                    selectExpressionItemClass.getMethod("setAlias", Alias.class)
                            .invoke(tenantItem, new Alias(TENANT_ID_COLUMN));

                    // 添加到SELECT列表
                    plainSelect.getClass().getMethod("addSelectItems", Object.class)
                            .invoke(plainSelect, tenantItem);
                }
            }
        } catch (Exception e) {
            log.debug("Could not add tenant_id to SELECT for INSERT", e);
        }
    }

    /**
     * 处理普通INSERT VALUES语句
     */
    private void processInsertValues(Insert insert, Long tenantId) {
        // 检查是否已经包含tenant_id列
        if (insert.getColumns() != null && insert.getColumns().stream()
                .anyMatch(c -> TENANT_ID_COLUMN.equalsIgnoreCase(c.getColumnName()))) {
            return;
        }

        // 添加tenant_id列
        insert.addColumns(new Column(TENANT_ID_COLUMN));

        // 处理VALUES
        processInsertValuesData(insert, tenantId);
    }

    private void processInsertValuesData(Insert insert, Long tenantId) {
        try {
            // 使用访问者模式处理多行插入，这是JSQLParser推荐的方式
            insert.accept(new InsertValuesVisitor(tenantId));
        } catch (Exception e) {
            log.debug("Could not inject tenant_id into INSERT VALUES using visitor pattern, trying fallback", e);
            // 如果访问者模式失败，使用反射作为后备方案
            processInsertValuesDataFallback(insert, tenantId);
        }
    }

    /**
     * 后备方案：使用反射处理INSERT VALUES（可能不完全支持多行插入）
     */
    private void processInsertValuesDataFallback(Insert insert, Long tenantId) {
        try {
            Object valuesList = null;
            String[] methodNames = {"getValues", "getItemsList", "getMultiExpressionList"};

            for (String methodName : methodNames) {
                try {
                    valuesList = insert.getClass().getMethod(methodName).invoke(insert);
                    if (valuesList != null) {
                        break;
                    }
                } catch (Exception ignored) {
                }
            }

            if (valuesList != null) {
                addExpressionToValuesList(valuesList, new LongValue(tenantId));
            }
        } catch (Exception e) {
            log.debug("Could not inject tenant_id into INSERT VALUES", e);
        }
    }

    private void addExpressionToValuesList(Object valuesList, LongValue tenantValue) {
        try {
            String[] methodNames = {"addExpressions", "add", "addExpression"};
            for (String methodName : methodNames) {
                try {
                    valuesList.getClass().getMethod(methodName, net.sf.jsqlparser.expression.Expression.class)
                            .invoke(valuesList, tenantValue);
                    return;
                } catch (Exception ignored) {
                }
            }
        } catch (Exception e) {
            log.debug("Could not add tenant_id expression to VALUES list", e);
        }
    }

    private void processUpdate(Update update, Long tenantId) {
        Table table = null;

        // 兼容不同版本的API
        try {
            // 尝试getTable()方法
            table = update.getTable();
        } catch (Exception e) {
            // 如果失败，尝试其他方法
            try {
                Object tablesObj = update.getClass().getMethod("getTables").invoke(update);
                if (tablesObj instanceof List && !((List<?>) tablesObj).isEmpty()) {
                    table = (Table) ((List<?>) tablesObj).getFirst();
                }
            } catch (Exception ex) {
                log.debug("Could not get table from Update statement", ex);
                return;
            }
        }

        if (table == null || !isTenantTable(table)) {
            return;
        }

        Expression tenantCondition = createTenantConditionForTable(table, tenantId);
        Expression currentWhere = update.getWhere();

        if (currentWhere == null) {
            update.setWhere(tenantCondition);
        } else {
            update.setWhere(new AndExpression(currentWhere, tenantCondition));
        }
    }

    private void processDelete(Delete delete, Long tenantId) {
        // 创建主表的租户条件
        if (!isTenantTable(delete.getTable())) {
            return;
        }

        Expression tenantCondition = createTenantConditionForTable(delete.getTable(), tenantId);
        Expression currentWhere = delete.getWhere();

        if (currentWhere == null) {
            delete.setWhere(tenantCondition);
        } else {
            delete.setWhere(new AndExpression(currentWhere, tenantCondition));
        }
    }

    /**
     * 清理缓存（可选的管理方法）
     */
    public void clearCache() {
        noTenantCache.clear();
        tableMatchCache.clear();
        log.info("Tenant interceptor cache cleared");
    }

    /**
     * 获取缓存大小（可选的监控方法）
     */
    public int getCacheSize() {
        return noTenantCache.size() + tableMatchCache.size();
    }

    /**
     * 暴露EXCLUDED_TABLES给访问者使用
     */
    public static Set<String> getExcludedTables() {
        return EXCLUDED_TABLES;
    }

    /**
     * INSERT VALUES访问者，正确处理多行插入
     */
    private static class InsertValuesVisitor extends StatementVisitorAdapter {
        private final Long tenantId;

        public InsertValuesVisitor(Long tenantId) {
            this.tenantId = tenantId;
        }

        @Override
        public void visit(Insert insert) {
            try {
                // 处理INSERT VALUES的多行插入
                processMultipleValuesList(insert);
            } catch (Exception e) {
                log.debug("Error processing INSERT VALUES in visitor", e);
            }
        }

        private void processMultipleValuesList(Insert insert) throws Exception {
            // 尝试获取VALUES列表
            Object itemsList = getItemsList(insert);
            if (itemsList == null) {
                return;
            }

            // 检查是否是多行插入
            if (itemsList instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> valuesList = (List<Object>) itemsList;

                // 为每一行VALUES添加tenant_id
                for (Object valuesItem : valuesList) {
                    addTenantIdToSingleValues(valuesItem);
                }
            } else {
                // 单行插入
                addTenantIdToSingleValues(itemsList);
            }
        }

        private Object getItemsList(Insert insert) throws Exception {
            // 尝试多种方法获取VALUES列表
            String[] methodNames = {"getItemsList", "getValues", "getMultiExpressionList"};

            for (String methodName : methodNames) {
                try {
                    Object result = insert.getClass().getMethod(methodName).invoke(insert);
                    if (result != null) {
                        return result;
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个方法
                }
            }
            return null;
        }

        private void addTenantIdToSingleValues(Object valuesItem) {
            try {
                // 尝试多种方法添加表达式
                String[] methodNames = {"addExpressions", "add", "addExpression"};
                LongValue tenantValue = new LongValue(tenantId);

                for (String methodName : methodNames) {
                    try {
                        valuesItem.getClass()
                                .getMethod(methodName, net.sf.jsqlparser.expression.Expression.class)
                                .invoke(valuesItem, tenantValue);
                        return; // 成功添加，退出
                    } catch (Exception ignored) {
                        // 继续尝试下一个方法
                    }
                }

                // 如果上述方法都失败，尝试获取表达式列表并直接添加
                tryAddToExpressionsList(valuesItem, tenantValue);

            } catch (Exception e) {
                log.debug("Could not add tenant_id to VALUES item", e);
            }
        }

        private void tryAddToExpressionsList(Object valuesItem, LongValue tenantValue) {
            try {
                // 尝试获取表达式列表
                String[] getterNames = {"getExpressions", "getItems", "getValues"};

                for (String getterName : getterNames) {
                    try {
                        Object expressionsList = valuesItem.getClass().getMethod(getterName).invoke(valuesItem);
                        if (expressionsList instanceof List) {
                            @SuppressWarnings("unchecked")
                            List<Object> list = (List<Object>) expressionsList;
                            list.add(tenantValue);
                            return; // 成功添加
                        }
                    } catch (Exception ignored) {
                        // 继续尝试下一个方法
                    }
                }
            } catch (Exception e) {
                log.debug("Could not add to expressions list", e);
            }
        }
    }

}
