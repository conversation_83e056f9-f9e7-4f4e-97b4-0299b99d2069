package com.nybc.ai.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 类描述：统一的AI配置类
 * 基于Spring AI 1.0.0手动配置，支持阿里百炼
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Configuration
public class UnifiedAiConfiguration {

    // ===================================================================
    // 简化配置 - 依赖Spring AI自动配置
    // ===================================================================

    // 注意：实际的ChatModel和EmbeddingModel将通过application.yml中的
    // spring.ai.openai配置自动创建，无需手动Bean配置

    // ===================================================================
    // 统一ChatClient配置
    // ===================================================================

    @Bean
    @Primary
    public ChatClient.Builder chatClientBuilder(ChatModel chatModel) {
        return ChatClient.builder(chatModel);
    }

}
