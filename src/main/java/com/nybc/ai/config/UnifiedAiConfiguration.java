package com.nybc.ai.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import com.nybc.ai.config.memory.JdbcChatMemoryRepository;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.ollama.OllamaEmbeddingModel;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import reactor.core.publisher.Flux;

import javax.sql.DataSource;

/**
 * 类描述：统一的AI配置类
 * 基于Spring AI 1.0.0，支持Ollama + Milvus向量存储
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Configuration
public class UnifiedAiConfiguration {

    /**
     * 配置ChatClient.Builder
     * 使用Spring AI的自动配置ChatModel
     */
    @Bean
    public ChatClient.Builder chatClientBuilder(ChatModel chatModel) {
        return ChatClient.builder(chatModel);
    }

    /**
     * 配置基于JDBC的ChatMemoryRepository，使用PostgreSQL数据库
     */
    @Bean
    @Primary
    public ChatMemoryRepository chatMemoryRepository(@Autowired DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return new JdbcChatMemoryRepository(jdbcTemplate);
    }

    /**
     * 配置Ollama API客户端
     */
    @Bean
    @Primary
    public OllamaApi ollamaApi(@Value("${spring.ai.ollama.base-url:http://localhost:11434}") String baseUrl) {
        return new OllamaApi(baseUrl);
    }

    /**
     * 配置Ollama嵌入模型
     */
    @Bean
    @Primary
    public EmbeddingModel embeddingModel(OllamaApi ollamaApi,
                                       @Value("${spring.ai.ollama.embedding.model:nomic-embed-text}") String model) {
        return new OllamaEmbeddingModel(ollamaApi, model);
    }

    /**
     * 配置向量存储
     * 注意：当前使用SimpleVectorStore，生产环境建议配置Milvus
     */
    @Bean
    @Primary
    public VectorStore vectorStore() {
        log.info("配置SimpleVectorStore作为向量存储");
        return new SimpleVectorStore();
    }

}
