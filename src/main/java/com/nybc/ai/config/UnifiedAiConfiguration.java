package com.nybc.ai.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import com.nybc.ai.config.memory.JdbcChatMemoryRepository;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.MilvusVectorStore;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.ollama.OllamaEmbeddingModel;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import reactor.core.publisher.Flux;

import javax.sql.DataSource;

/**
 * 类描述：统一的AI配置类
 * 基于Spring AI 1.0.0模拟配置，确保应用正常启动
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Configuration
public class UnifiedAiConfiguration {

    /**
     * 模拟ChatModel实现，用于开发和测试
     */
    @Bean
    @Primary
    public ChatModel mockChatModel() {
        return new ChatModel() {
            @Override
            public ChatResponse call(Prompt prompt) {
                // 模拟AI响应
                String response = "这是一个模拟的AI响应。输入内容：" +
                    prompt.getInstructions().stream()
                        .map(Message::getText)
                        .reduce("", (a, b) -> a + " " + b);

                Generation generation = new Generation(new AssistantMessage(response));
                return new ChatResponse(java.util.List.of(generation));
            }

            @Override
            public Flux<ChatResponse> stream(Prompt prompt) {
                return Flux.just(call(prompt));
            }
        };
    }

    @Bean
    @Primary
    public ChatClient.Builder chatClientBuilder(ChatModel chatModel) {
        return ChatClient.builder(chatModel);
    }

    /**
     * 配置基于JDBC的ChatMemoryRepository，使用PostgreSQL数据库
     */
    @Bean
    @Primary
    public ChatMemoryRepository chatMemoryRepository(@Autowired DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return new JdbcChatMemoryRepository(jdbcTemplate);
    }

    /**
     * 配置Ollama API客户端
     */
    @Bean
    @Primary
    public OllamaApi ollamaApi(@Value("${spring.ai.ollama.base-url:http://localhost:11434}") String baseUrl) {
        return new OllamaApi(baseUrl);
    }

    /**
     * 配置Ollama嵌入模型
     */
    @Bean
    @Primary
    public EmbeddingModel embeddingModel(OllamaApi ollamaApi,
                                       @Value("${spring.ai.ollama.embedding.model:nomic-embed-text}") String model) {
        return new OllamaEmbeddingModel(ollamaApi, model);
    }

    /**
     * 配置Milvus向量存储
     */
    @Bean
    @Primary
    public VectorStore vectorStore(EmbeddingModel embeddingModel,
                                 @Value("${spring.ai.vectorstore.milvus.host:localhost}") String host,
                                 @Value("${spring.ai.vectorstore.milvus.port:19530}") int port,
                                 @Value("${spring.ai.vectorstore.milvus.database-name:default}") String databaseName,
                                 @Value("${spring.ai.vectorstore.milvus.collection-name:vector_store}") String collectionName) {

        try {
            // 构建Milvus连接URI
            String uri = String.format("http://%s:%d", host, port);

            // 创建MilvusVectorStore配置
            MilvusVectorStore.MilvusVectorStoreConfig config = MilvusVectorStore.MilvusVectorStoreConfig.builder()
                    .withUri(uri)
                    .withDatabaseName(databaseName)
                    .withCollectionName(collectionName)
                    .withEmbeddingDimension(768) // nomic-embed-text的维度
                    .build();

            return new MilvusVectorStore(config, embeddingModel);
        } catch (Exception e) {
            // 如果Milvus连接失败，使用简单向量存储作为备选
            log.warn("Milvus连接失败，使用简单向量存储作为备选: {}", e.getMessage());
            return new org.springframework.ai.vectorstore.SimpleVectorStore();
        }
    }

}
