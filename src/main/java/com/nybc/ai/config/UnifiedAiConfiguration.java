package com.nybc.ai.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestClient;

/**
 * 类描述：统一的AI配置类
 * 基于Spring AI 1.0.0手动配置，支持阿里百炼
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Configuration
public class UnifiedAiConfiguration {

    // ===================================================================
    // 阿里百炼 配置 (OpenAI兼容API) - 默认启用
    // ===================================================================

    @Bean("dashscopeApi")
    @ConditionalOnProperty(name = "spring.ai.dashscope.enabled", havingValue = "true", matchIfMissing = true)
    public OpenAiApi dashscopeApi(@Value("${spring.ai.dashscope.api-key:sk-de657e2f9231440a8fb5585614e94611}") String apiKey) {
        return new OpenAiApi("https://dashscope.aliyuncs.com/compatible-mode/v1", apiKey, RestClient.builder());
    }

    @Bean("dashscopeChatModel")
    @Primary
    @ConditionalOnProperty(name = "spring.ai.dashscope.enabled", havingValue = "true", matchIfMissing = true)
    public ChatModel dashscopeChatModel(
            @Qualifier("dashscopeApi") OpenAiApi dashscopeApi,
            @Value("${spring.ai.dashscope.chat.model:deepseek-r1}") String model) {
        return new OpenAiChatModel(dashscopeApi, OpenAiChatOptions.builder()
                .withModel(model)
                .withTemperature(0.7)
                .withMaxTokens(4096)
                .build());
    }

    // ===================================================================
    // OpenAI 官方配置 - 可选
    // ===================================================================

    @Bean("openaiApi")
    @ConditionalOnProperty(name = "spring.ai.openai.enabled", havingValue = "true")
    public OpenAiApi openaiApi(@Value("${spring.ai.openai.api-key:${OPENAI_API_KEY}}") String apiKey) {
        return new OpenAiApi("https://api.openai.com/v1", apiKey, RestClient.builder());
    }

    @Bean("openaiChatModel")
    @ConditionalOnProperty(name = "spring.ai.openai.enabled", havingValue = "true")
    public ChatModel openaiChatModel(
            @Qualifier("openaiApi") OpenAiApi openaiApi,
            @Value("${spring.ai.openai.chat.model:gpt-4o}") String model) {
        return new OpenAiChatModel(openaiApi, OpenAiChatOptions.builder()
                .withModel(model)
                .withTemperature(0.7)
                .withMaxTokens(4096)
                .build());
    }

    @Bean("openaiEmbeddingModel")
    @ConditionalOnProperty(name = "spring.ai.openai.enabled", havingValue = "true")
    public EmbeddingModel openaiEmbeddingModel(
            @Qualifier("openaiApi") OpenAiApi openaiApi,
            @Value("${spring.ai.openai.embedding.model:text-embedding-3-large}") String model) {
        return new OpenAiEmbeddingModel(openaiApi, OpenAiEmbeddingOptions.builder()
                .withModel(model)
                .build());
    }

    // ===================================================================
    // Ollama 配置 - 可选
    // ===================================================================

    @Bean("ollamaApi")
    @ConditionalOnProperty(name = "spring.ai.ollama.enabled", havingValue = "true")
    public OllamaApi ollamaApi(@Value("${spring.ai.ollama.base-url:http://localhost:11434}") String baseUrl) {
        return new OllamaApi(baseUrl);
    }

    @Bean("ollamaChatModel")
    @ConditionalOnProperty(name = "spring.ai.ollama.enabled", havingValue = "true")
    public ChatModel ollamaChatModel(
            @Qualifier("ollamaApi") OllamaApi ollamaApi,
            @Value("${spring.ai.ollama.chat.model:llama3.2}") String model) {
        // 简化Ollama配置，使用默认设置
        return new OllamaChatModel(ollamaApi);
    }

    // ===================================================================
    // 统一ChatClient配置
    // ===================================================================

    @Bean
    @Primary
    public ChatClient.Builder chatClientBuilder(@Primary ChatModel chatModel) {
        return ChatClient.builder(chatModel);
    }

}
