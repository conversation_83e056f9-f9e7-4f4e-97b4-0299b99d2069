package com.nybc.ai.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.OllamaChatOptions;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 类描述：统一的AI配置类
 * 基于Spring AI框架，支持多个AI提供商的统一配置
 * 所有提供商都通过OpenAI兼容API或原生支持接入
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Configuration
public class UnifiedAiConfiguration {

    // ===================================================================
    // DeepSeek 配置 (OpenAI兼容API)
    // ===================================================================

    @Bean("deepseekApi")
    @ConditionalOnProperty(name = "spring.ai.deepseek.enabled", havingValue = "true", matchIfMissing = true)
    public OpenAiApi deepseekApi(@Value("${spring.ai.deepseek.api-key:${DEEPSEEK_API_KEY}}") String apiKey) {
        return OpenAiApi.builder()
                .apiKey(apiKey)
                .baseUrl("https://api.deepseek.com/v1")
                .build();
    }

    @Bean("deepseekChatModel")
    @ConditionalOnProperty(name = "spring.ai.deepseek.enabled", havingValue = "true", matchIfMissing = true)
    public ChatModel deepseekChatModel(@Qualifier("deepseekApi") OpenAiApi deepseekApi,
                                      @Value("${spring.ai.deepseek.chat.model:deepseek-chat}") String model) {
        return new OpenAiChatModel(deepseekApi, OpenAiChatOptions.builder()
                .model(model)
                .temperature(0.7)
                .build());
    }

    // ===================================================================
    // 阿里百炼 配置 (OpenAI兼容API)
    // ===================================================================

    @Bean("dashscopeApi")
    @ConditionalOnProperty(name = "spring.ai.dashscope.enabled", havingValue = "true")
    public OpenAiApi dashscopeApi(@Value("${spring.ai.dashscope.api-key:${DASHSCOPE_API_KEY}}") String apiKey) {
        return OpenAiApi.builder()
                .apiKey(apiKey)
                .baseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1")
                .build();
    }

    @Bean("dashscopeChatModel")
    @ConditionalOnProperty(name = "spring.ai.dashscope.enabled", havingValue = "true")
    public ChatModel dashscopeChatModel(@Qualifier("dashscopeApi") OpenAiApi dashscopeApi,
                                       @Value("${spring.ai.dashscope.chat.model:qwen-plus}") String model) {
        return new OpenAiChatModel(dashscopeApi, OpenAiChatOptions.builder()
                .model(model)
                .temperature(0.7)
                .build());
    }

    // ===================================================================
    // OpenAI 官方配置
    // ===================================================================

    @Bean("openaiApi")
    @ConditionalOnProperty(name = "spring.ai.openai.enabled", havingValue = "true")
    public OpenAiApi openaiApi(@Value("${spring.ai.openai.api-key:${OPENAI_API_KEY}}") String apiKey) {
        return OpenAiApi.builder()
                .apiKey(apiKey)
                .baseUrl("https://api.openai.com/v1")
                .build();
    }

    @Bean("openaiChatModel")
    @ConditionalOnProperty(name = "spring.ai.openai.enabled", havingValue = "true")
    public ChatModel openaiChatModel(@Qualifier("openaiApi") OpenAiApi openaiApi,
                                    @Value("${spring.ai.openai.chat.model:gpt-4o}") String model) {
        return new OpenAiChatModel(openaiApi, OpenAiChatOptions.builder()
                .model(model)
                .temperature(0.7)
                .build());
    }

    @Bean("openaiEmbeddingModel")
    @ConditionalOnProperty(name = "spring.ai.openai.enabled", havingValue = "true")
    public EmbeddingModel openaiEmbeddingModel(@Qualifier("openaiApi") OpenAiApi openaiApi,
                                              @Value("${spring.ai.openai.embedding.model:text-embedding-3-large}") String model) {
        return new OpenAiEmbeddingModel(openaiApi, OpenAiEmbeddingOptions.builder()
                .model(model)
                .build());
    }

    // ===================================================================
    // Ollama 配置 (Spring AI原生支持)
    // ===================================================================

    @Bean("ollamaApi")
    @ConditionalOnProperty(name = "spring.ai.ollama.enabled", havingValue = "true")
    public OllamaApi ollamaApi(@Value("${spring.ai.ollama.base-url:http://localhost:11434}") String baseUrl) {
        return new OllamaApi(baseUrl);
    }

    @Bean("ollamaChatModel")
    @ConditionalOnProperty(name = "spring.ai.ollama.enabled", havingValue = "true")
    public ChatModel ollamaChatModel(@Qualifier("ollamaApi") OllamaApi ollamaApi,
                                    @Value("${spring.ai.ollama.chat.model:llama3.2}") String model) {
        return new OllamaChatModel(ollamaApi, OllamaChatOptions.builder()
                .model(model)
                .temperature(0.7)
                .build());
    }

    // ===================================================================
    // 主要ChatModel选择 (按优先级)
    // ===================================================================

    @Bean
    @Primary
    public ChatModel primaryChatModel(
            @Qualifier("deepseekChatModel") ChatModel deepseekChatModel,
            @Qualifier("dashscopeChatModel") ChatModel dashscopeChatModel,
            @Qualifier("openaiChatModel") ChatModel openaiChatModel,
            @Qualifier("ollamaChatModel") ChatModel ollamaChatModel) {
        
        // 按优先级选择可用的ChatModel
        if (deepseekChatModel != null) return deepseekChatModel;
        if (dashscopeChatModel != null) return dashscopeChatModel;
        if (openaiChatModel != null) return openaiChatModel;
        if (ollamaChatModel != null) return ollamaChatModel;
        
        throw new IllegalStateException("没有可用的ChatModel配置");
    }

    @Bean
    @Primary
    public EmbeddingModel primaryEmbeddingModel(
            @Qualifier("openaiEmbeddingModel") EmbeddingModel openaiEmbeddingModel) {
        
        // 目前只有OpenAI提供嵌入模型，后续可扩展
        if (openaiEmbeddingModel != null) return openaiEmbeddingModel;
        
        throw new IllegalStateException("没有可用的EmbeddingModel配置");
    }

    // ===================================================================
    // 统一ChatClient配置
    // ===================================================================

    @Bean
    @Primary
    public ChatClient.Builder chatClientBuilder(@Primary ChatModel primaryChatModel) {
        return ChatClient.builder(primaryChatModel);
    }

    /**
     * 为不同提供商创建专用的ChatClient
     */
    @Bean("deepseekChatClient")
    @ConditionalOnProperty(name = "spring.ai.deepseek.enabled", havingValue = "true", matchIfMissing = true)
    public ChatClient.Builder deepseekChatClientBuilder(@Qualifier("deepseekChatModel") ChatModel deepseekChatModel) {
        return ChatClient.builder(deepseekChatModel);
    }

    @Bean("dashscopeChatClient")
    @ConditionalOnProperty(name = "spring.ai.dashscope.enabled", havingValue = "true")
    public ChatClient.Builder dashscopeChatClientBuilder(@Qualifier("dashscopeChatModel") ChatModel dashscopeChatModel) {
        return ChatClient.builder(dashscopeChatModel);
    }

    @Bean("openaiChatClient")
    @ConditionalOnProperty(name = "spring.ai.openai.enabled", havingValue = "true")
    public ChatClient.Builder openaiChatClientBuilder(@Qualifier("openaiChatModel") ChatModel openaiChatModel) {
        return ChatClient.builder(openaiChatModel);
    }

    @Bean("ollamaChatClient")
    @ConditionalOnProperty(name = "spring.ai.ollama.enabled", havingValue = "true")
    public ChatClient.Builder ollamaChatClientBuilder(@Qualifier("ollamaChatModel") ChatModel ollamaChatModel) {
        return ChatClient.builder(ollamaChatModel);
    }
}
