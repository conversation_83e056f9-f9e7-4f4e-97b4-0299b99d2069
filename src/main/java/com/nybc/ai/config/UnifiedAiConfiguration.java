package com.nybc.ai.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.memory.ChatMemoryRepository;
import com.nybc.ai.config.memory.JdbcChatMemoryRepository;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import reactor.core.publisher.Flux;

import javax.sql.DataSource;

/**
 * 类描述：统一的AI配置类
 * 基于Spring AI 1.0.0模拟配置，确保应用正常启动
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Configuration
public class UnifiedAiConfiguration {

    /**
     * 模拟ChatModel实现，用于开发和测试
     */
    @Bean
    @Primary
    public ChatModel mockChatModel() {
        return new ChatModel() {
            @Override
            public ChatResponse call(Prompt prompt) {
                // 模拟AI响应
                String response = "这是一个模拟的AI响应。输入内容：" +
                    prompt.getInstructions().stream()
                        .map(Message::getText)
                        .reduce("", (a, b) -> a + " " + b);

                Generation generation = new Generation(response, null);
                return new ChatResponse(java.util.List.of(generation));
            }

            @Override
            public Flux<ChatResponse> stream(Prompt prompt) {
                return Flux.just(call(prompt));
            }
        };
    }

    @Bean
    @Primary
    public ChatClient.Builder chatClientBuilder(ChatModel chatModel) {
        return ChatClient.builder(chatModel);
    }

    /**
     * 配置基于JDBC的ChatMemoryRepository，使用PostgreSQL数据库
     */
    @Bean
    @Primary
    public ChatMemoryRepository chatMemoryRepository(@Autowired DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return new JdbcChatMemoryRepository(jdbcTemplate);
    }

}
