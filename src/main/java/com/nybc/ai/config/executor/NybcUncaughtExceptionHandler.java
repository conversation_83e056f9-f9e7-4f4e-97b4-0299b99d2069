package com.nybc.ai.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Getter
@Slf4j
public class NybcUncaughtExceptionHandler implements Thread.UncaughtExceptionHandler {
    private int exceptionCount;
    @Override
    public void uncaughtException(Thread t, Throwable e) {
        if (e != null) {
            exceptionCount++;
            log.error("线程池中线程[{}]执行出现未捕获的异常:--》 {} ", t.getName(), e.getMessage());
        }
    }

}
