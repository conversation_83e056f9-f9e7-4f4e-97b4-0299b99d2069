package com.nybc.ai.config;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
public class NybcThreadFactory {

    public NybcThreadFactory() {
    }

    public static ThreadFactory build(final String name) {

        return new ThreadFactory() {
            final AtomicLong count = name != null ? new AtomicLong(0L) : null;

            @Override
            public Thread newThread(@NotNull Runnable r) {
                Thread t = new Thread(r);
                t.setDaemon(true);
                if (t.getPriority() != Thread.NORM_PRIORITY) {
                    t.setPriority(Thread.NORM_PRIORITY);
                }
                if (this.count != null) {
                    t.setName(name + "-" + this.count.getAndIncrement());
                }
                t.setUncaughtExceptionHandler(new NybcUncaughtExceptionHandler());
                return t;
            }
        };
    }
}