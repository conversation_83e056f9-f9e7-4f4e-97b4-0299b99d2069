package com.nybc.ai.config;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 类描述：自定义扩展拒绝策略
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
public class NybcRejectPolicy extends ThreadPoolExecutor.AbortPolicy {

    // 最大重试次数
    private static final int MAX_RETRY_ATTEMPTS = 4;

    // 每次重试的延迟时间（5秒）
    private static final long RETRY_DELAY_SECONDS = 5;

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
        if (e.isShutdown()) {
            // 如果线程池已经关闭，直接记录日志并返回
            log.warn("ThreadPool is shutdown, task rejected: {}", r.toString());
            return;
        }
        // 记录详细日志
        log.warn("触发线程拒绝策略：\r\n" +
                         "Task: {}\r\n" +
                         "ThreadPool State: {}\r\n" +
                         "Core Pool Size: {}\r\n" +
                         "Maximum Pool Size: {}\r\n" +
                         "Active Threads: {}\r\n" +
                         "Queue Size: {}\r\n" +
                         "Completed Tasks: {}\r\n" +
                         "Task Rejected",
                 r.toString(),
                 e.toString(),
                 e.getCorePoolSize(),
                 e.getMaximumPoolSize(),
                 e.getActiveCount(),
                 e.getQueue().size(),
                 e.getCompletedTaskCount()
        );
        // 尝试重新提交任务
        retrySubmitTask(r, e, 0);
    }

    /**
     * 尝试重新提交任务到线程池
     *
     * @param r       被拒绝的任务
     * @param e       线程池
     * @param attempt 当前重试次数
     */
    private void retrySubmitTask(Runnable r, ThreadPoolExecutor e, int attempt) {
        if (attempt >= MAX_RETRY_ATTEMPTS) {
            // 达到最大重试次数，放弃重试并抛出异常
            log.error("Max retry attempts reached for task: {}", r.toString());
            super.rejectedExecution(r, e);
            return;
        }

        // 检查线程池是否已关闭
        if (e.isShutdown()) {
            log.warn("ThreadPool is shutdown, task rejected: {}", r.toString());
            return;
        }

        // 延迟 5 秒后检查队列状态
        SCHEDULE.schedule(() -> {
            try {
                // 获取当前队列大小和剩余容量
                BlockingQueue<Runnable> queue = e.getQueue();
                int currentQueueSize = queue.size();
                int remainingCapacity = ((BlockingQueue<?>) queue).remainingCapacity();

                // 检查队列的剩余容量是否大于当前队列大小的两倍
                if (remainingCapacity > currentQueueSize * 2) {
                    // 队列有足够空间，尝试重新提交任务
                    log.info("Queue has enough capacity ({} > {} * 2), retrying to submit task: {}",
                             remainingCapacity, currentQueueSize, r.toString());
                    e.execute(r);  // 重新提交任务
                } else {
                    // 队列空间不足，继续等待并重试
                    log.warn("Queue does not have enough capacity ({} <= {} * 2), waiting and retrying... Attempt: {}",
                             remainingCapacity, currentQueueSize, attempt + 1);
                    retrySubmitTask(r, e, attempt + 1);
                }
            } catch (RejectedExecutionException ree) {
                // 如果再次被拒绝，记录日志并抛出异常
                log.error("Failed to resubmit task after retry: {}", r.toString(), ree);
                throw ree;
            }
        }, RETRY_DELAY_SECONDS, TimeUnit.SECONDS);
    }

}
