package com.nybc.common.tool.thread;

import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.*;

import static com.nybc.common.tool.NytcToolConstant.CORE_POOL_SIZE;
import static com.nybc.common.tool.NytcToolConstant.MAX_POOL_SIZE;


/**
 * 类描述：
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
public class NybcExecutor extends ThreadPoolExecutor {


    public NybcExecutor() {
        super(CORE_POOL_SIZE, MAX_POOL_SIZE, 3, TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(4096),
                NybcThreadFactory.build("NYBC_线程池-"),
                new NybcRejectPolicy());
        this.allowCoreThreadTimeOut(true);
    }

    public NybcExecutor(int corePoolSize,
                        int maximumPoolSize,
                        long keepAliveTime,
                        TimeUnit unit,
                        LinkedBlockingQueue<Runnable> workQueue,
                        ThreadFactory threadFactory,
                        RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);

    }


    @Override
    public void execute(@NotNull Runnable command) {
        super.execute(command);
    }

    @NotNull
    @Override
    public Future<?> submit(@NotNull Runnable task) {
        return super.submit(task);
    }

    @NotNull
    @Override
    public Future<?> submit(@NotNull Callable task) {
        return super.submit(task);
    }


    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        super.beforeExecute(t, r);
    }

    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        super.afterExecute(r, t);
        if (t == null && r instanceof Future) {
            try {
                ((Future<?>) r).get();
            } catch (CancellationException ce) {
                t = ce;
            } catch (ExecutionException ee) {
                t = ee.getCause();
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
            }
        }
        if (t != null) {
            log.error("beforeExecute-线程-->{}", t.getMessage());
        }
    }

    @Override
    protected void terminated() {
        super.terminated();
        log.debug("线程池关闭！！");
    }
    public static ExecutorService getTtlExecutorService() {
        // 创建 NybcExecutor 实例
        NybcExecutor nybcExecutor = new NybcExecutor();
        // 使用 TtlExecutors 包装 NybcExecutor，确保 ThreadLocal 的值在异步任务中传递
        return TtlExecutors.getTtlExecutorService(nybcExecutor);
    }
}
