package com.nybc.ai.config;

import com.alibaba.ttl.TtlRunnable;
import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.concurrent.*;


/**
 * 类描述：异步线程配置
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Configuration
@Slf4j
public class AsyncExecutorConfig implements AsyncConfigurer {
    /**
     * 线程队列
     */
    public static final int QUEUE = 256;

    public static final int KEEP_LIVE = 60 * 5;

    public static final int CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors();
    public static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 3 - 1;

    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(CORE_POOL_SIZE);
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        executor.setQueueCapacity(QUEUE);
        executor.setThreadNamePrefix("Async-pool-");
        executor.setThreadFactory(NybcThreadFactory.build("Async-pool"));
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setKeepAliveSeconds(KEEP_LIVE);
        executor.setRejectedExecutionHandler(new NybcRejectPolicy());
        executor.initialize();
        return TtlExecutors.getTtlExecutor(executor);
    }

    @Bean("applicationEventTaskExecutor")
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(2);
        scheduler.setThreadNamePrefix("Nybc-edu-Scheduler-");
        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 调度器shutdown被调用时等待当前被调度的任务完成
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时长
        scheduler.setAwaitTerminationSeconds(60);
        scheduler.setTaskDecorator(TtlRunnable::get);
        return scheduler;
    }

    @Bean("nybcExecutor")
    public ExecutorService nybcExecutorService() {
        NybcExecutor nybcExecutor = new NybcExecutor(
                CORE_POOL_SIZE,
                CORE_POOL_SIZE * 3 - 1,
                KEEP_LIVE,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1024),
                NybcThreadFactory.build("NybcExecutorPool"),
                new CustomRejectedExecutionHandler()
        );
        log.info("Custom NybcExecutor 'nybcExecutorService' configured and wrapped with TtlExecutorService.");
        return TtlExecutors.getTtlExecutorService(nybcExecutor);
    }

    /**
     * 异步执行异常处理
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (throwable, method, params) -> {
            // 获取异常的堆栈轨迹
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            throwable.printStackTrace(pw);
            String stackTrace = sw.toString();

            // 记录详细日志，包含方法名、参数、异常信息和堆栈
            log.error("""
                      在 @Async 方法中捕获到未处理异常：
                        方法名称: {}
                        参数值: {}
                        异常类型: {}
                        异常消息: {}
                        堆栈轨迹:
                      {}""",
                      method.getName(),
                      Arrays.toString(params),
                      throwable.getClass().getName(),
                      throwable.getMessage(),
                      stackTrace);

            // TODO: 后续可以集成告警服务，例如发送到消息队列、邮件或告警平台
            // alertService.sendAsyncErrorAlert(method.getName(), params, throwable);
            // 这里不重新抛出异常，让异步任务的线程能够正常结束或返回线程池
        };
    }

}