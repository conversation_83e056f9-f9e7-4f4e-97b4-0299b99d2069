package com.nybc.ai.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.image.ImageModel;
import org.springframework.ai.openai.*;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.OllamaChatOptions;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 类描述：Spring AI 相关配置
 * <p>
 * 该配置类用于手动创建和管理 Spring AI 的核心组件 Bean,
 * 解决了在特定版本下自动配置不生效的问题。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Configuration
public class AiConfiguration {

    /**
     * 创建一个 OpenAiApi 实例.
     *
     * @param apiKey 从配置文件中注入的 OpenAI API Key
     * @return OpenAiApi 实例
     */
    @Bean
    public OpenAiApi openAiApi(@Value("${spring.ai.openai.api-key:YOUR_API_KEY}") String apiKey) {
        return OpenAiApi.builder()
                .apiKey(apiKey)
                .build();
    }

    /**
     * 创建 OpenAI ChatModel Bean
     * 支持 GPT-4o, GPT-4, GPT-3.5-turbo 等模型
     *
     * @param openAiApi OpenAiApi 实例
     * @return OpenAiChatModel 实例
     */
    @Bean
    @ConditionalOnProperty(name = "spring.ai.openai.api-key")
    public ChatModel openAiChatModel(OpenAiApi openAiApi,
                                    @Value("${spring.ai.openai.chat.options.model:gpt-4o}") String model,
                                    @Value("${spring.ai.openai.chat.options.temperature:0.7}") Double temperature) {
        return new OpenAiChatModel(openAiApi, OpenAiChatOptions.builder()
                .model(model)
                .temperature(temperature)
                .build());
    }

    /**
     * 创建 OpenAI EmbeddingModel Bean
     * 支持 text-embedding-3-large, text-embedding-ada-002 等模型
     *
     * @param openAiApi OpenAiApi 实例
     * @return OpenAiEmbeddingModel 实例
     */
    @Bean
    @ConditionalOnProperty(name = "spring.ai.openai.api-key")
    public EmbeddingModel openAiEmbeddingModel(OpenAiApi openAiApi,
                                              @Value("${spring.ai.openai.embedding.options.model:text-embedding-3-large}") String model) {
        return new OpenAiEmbeddingModel(openAiApi, OpenAiEmbeddingOptions.builder()
                .model(model)
                .build());
    }

    /**
     * 创建 OpenAI ImageModel Bean
     * 支持 DALL-E 3, DALL-E 2 图像生成
     *
     * @param openAiApi OpenAiApi 实例
     * @return OpenAiImageModel 实例
     */
    @Bean
    @ConditionalOnProperty(name = "spring.ai.openai.api-key")
    public ImageModel openAiImageModel(OpenAiApi openAiApi,
                                      @Value("${spring.ai.openai.image.options.model:dall-e-3}") String model) {
        return new OpenAiImageModel(openAiApi, OpenAiImageOptions.builder()
                .model(model)
                .quality("hd")
                .size("1024x1024")
                .build());
    }

    /**
     * 创建 Ollama API 实例
     *
     * @param baseUrl Ollama服务的基础URL
     * @return OllamaApi 实例
     */
    @Bean
    @ConditionalOnProperty(name = "spring.ai.ollama.enabled", havingValue = "true", matchIfMissing = false)
    public OllamaApi ollamaApi(@Value("${spring.ai.ollama.base-url:http://localhost:11434}") String baseUrl) {
        return new OllamaApi(baseUrl);
    }

    /**
     * 创建 Ollama ChatModel Bean
     *
     * @param ollamaApi OllamaApi 实例
     * @return OllamaChatModel 实例
     */
    @Bean
    @ConditionalOnProperty(name = "spring.ai.ollama.enabled", havingValue = "true", matchIfMissing = false)
    public ChatModel ollamaChatModel(OllamaApi ollamaApi,
                                    @Value("${spring.ai.ollama.chat.options.model:llama3.2}") String model) {
        return new OllamaChatModel(ollamaApi, OllamaChatOptions.builder()
                .model(model)
                .temperature(0.7)
                .build());
    }

    /**
     * 根据 ChatModel 创建 ChatClient.Builder Bean.
     * 这个 Builder 将被注入到其他服务中, 如 DocumentQueryService.
     * 优先使用OpenAI ChatModel，如果不可用则使用Ollama
     *
     * @param chatModel 自动注入的 ChatModel Bean
     * @return ChatClient.Builder 实例
     */
    @Bean
    @Primary
    public ChatClient.Builder chatClientBuilder(ChatModel chatModel) {
        return ChatClient.builder(chatModel);
    }
}
