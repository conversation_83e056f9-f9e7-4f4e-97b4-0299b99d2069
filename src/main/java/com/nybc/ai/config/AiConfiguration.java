package com.nybc.ai.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 类描述：Spring AI 相关配置
 * <p>
 * 该配置类用于手动创建和管理 Spring AI 的核心组件 Bean,
 * 解决了在特定版本下自动配置不生效的问题。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Configuration
public class AiConfiguration {

    /**
     * 创建一个 OpenAiApi 实例.
     *
     * @param apiKey 从配置文件中注入的 OpenAI API Key
     * @return OpenAiApi 实例
     */
    @Bean
    public OpenAiApi openAiApi(@Value("${spring.ai.openai.api-key:YOUR_API_KEY}") String apiKey) {
        return OpenAiApi.builder()
                .apiKey(apiKey)
                .build();
    }

    /**
     * 创建 ChatModel Bean，这里以 OpenAiChatModel 为例.
     * <p>
     * 如果您使用其他模型, 只需替换此处的实现,
     * 例如 ZhipuAiChatModel 等。
     *
     * @param openAiApi OpenAiApi 实例
     * @return ChatModel 实例
     */
    @Bean
    public ChatModel openAiChatModel(OpenAiApi openAiApi) {
        // 使用默认选项创建一个 OpenAiChatModel
        // 您可以根据需要自定义 OpenAiChatOptions, 例如更换模型
        return new OpenAiChatModel(openAiApi, OpenAiChatOptions.builder()
                .model("gpt-3.5-turbo")
                .build());
    }

    /**
     * 根据 ChatModel 创建 ChatClient.Builder Bean.
     * 这个 Builder 将被注入到其他服务中, 如 DocumentQueryService.
     *
     * @param chatModel 自动注入的 ChatModel Bean
     * @return ChatClient.Builder 实例
     */
    @Bean
    public ChatClient.Builder chatClientBuilder(ChatModel chatModel) {
        return ChatClient.builder(chatModel);
    }
}
