package com.nybc.ai.config.memory;

import org.springframework.ai.chat.memory.ChatMemoryRepository;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 类描述：基于JDBC的ChatMemoryRepository实现
 * 使用PostgreSQL数据库存储聊天记忆
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Slf4j
@Repository
public class JdbcChatMemoryRepository implements ChatMemoryRepository {

    private final JdbcTemplate jdbcTemplate;

    public JdbcChatMemoryRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 添加消息到聊天记忆
     *
     * @param conversationId 会话ID
     * @param messages 消息列表
     */
    @Override
    public void add(String conversationId, List<Message> messages) {
        try {
            for (Message message : messages) {
                String sql = """
                    INSERT INTO chat_memory (conversation_id, message_type, content, metadata, created_at, updated_at)
                    VALUES (?, ?, ?, ?::jsonb, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """;
                
                String messageType = getMessageType(message);
                String content = message.getContent();
                String metadata = JSON.toJSONString(message.getMetadata());
                
                jdbcTemplate.update(sql, conversationId, messageType, content, metadata);
                log.debug("添加消息到聊天记忆: conversationId={}, type={}", conversationId, messageType);
            }
            
            // 更新会话的最后活动时间
            updateSessionActivity(conversationId);
            
        } catch (Exception e) {
            log.error("添加聊天记忆失败: conversationId={}", conversationId, e);
            throw new RuntimeException("添加聊天记忆失败", e);
        }
    }

    /**
     * 获取聊天记忆
     *
     * @param conversationId 会话ID
     * @param lastN 获取最后N条消息
     * @return 消息列表
     */
    @Override
    public List<Message> get(String conversationId, int lastN) {
        try {
            String sql = """
                SELECT conversation_id, message_type, content, metadata, created_at
                FROM chat_memory
                WHERE conversation_id = ?
                ORDER BY created_at DESC
                LIMIT ?
                """;
            
            List<Message> messages = jdbcTemplate.query(sql, new MessageRowMapper(), conversationId, lastN);
            log.debug("获取聊天记忆: conversationId={}, count={}", conversationId, messages.size());
            
            // 按时间正序返回（最早的在前面）
            return messages.reversed();
            
        } catch (Exception e) {
            log.error("获取聊天记忆失败: conversationId={}", conversationId, e);
            throw new RuntimeException("获取聊天记忆失败", e);
        }
    }

    /**
     * 清除聊天记忆
     *
     * @param conversationId 会话ID
     */
    @Override
    public void clear(String conversationId) {
        try {
            String sql = "DELETE FROM chat_memory WHERE conversation_id = ?";
            int deletedCount = jdbcTemplate.update(sql, conversationId);
            log.info("清除聊天记忆: conversationId={}, deletedCount={}", conversationId, deletedCount);
            
        } catch (Exception e) {
            log.error("清除聊天记忆失败: conversationId={}", conversationId, e);
            throw new RuntimeException("清除聊天记忆失败", e);
        }
    }

    /**
     * 获取消息类型字符串
     */
    private String getMessageType(Message message) {
        if (message instanceof UserMessage) {
            return "USER";
        } else if (message instanceof AssistantMessage) {
            return "ASSISTANT";
        } else {
            return message.getMessageType().name();
        }
    }

    /**
     * 更新会话活动时间
     */
    private void updateSessionActivity(String conversationId) {
        try {
            // 首先检查会话是否存在
            String checkSql = "SELECT COUNT(*) FROM chat_sessions WHERE conversation_id = ?";
            Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class, conversationId);
            
            if (count == null || count == 0) {
                // 创建新会话记录
                String insertSql = """
                    INSERT INTO chat_sessions (conversation_id, session_name, status, created_at, updated_at, last_activity_at)
                    VALUES (?, ?, 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """;
                jdbcTemplate.update(insertSql, conversationId, "会话-" + conversationId.substring(0, Math.min(8, conversationId.length())));
                log.debug("创建新会话记录: conversationId={}", conversationId);
            } else {
                // 更新现有会话的活动时间
                String updateSql = """
                    UPDATE chat_sessions 
                    SET last_activity_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                    WHERE conversation_id = ?
                    """;
                jdbcTemplate.update(updateSql, conversationId);
                log.debug("更新会话活动时间: conversationId={}", conversationId);
            }
            
        } catch (Exception e) {
            log.warn("更新会话活动时间失败: conversationId={}", conversationId, e);
            // 不抛出异常，避免影响主要功能
        }
    }

    /**
     * 消息行映射器
     */
    private static class MessageRowMapper implements RowMapper<Message> {
        @Override
        public Message mapRow(ResultSet rs, int rowNum) throws SQLException {
            String messageType = rs.getString("message_type");
            String content = rs.getString("content");
            String metadataJson = rs.getString("metadata");
            
            // 解析元数据
            Map<String, Object> metadata = null;
            if (metadataJson != null && !metadataJson.trim().isEmpty()) {
                try {
                    metadata = JSON.parseObject(metadataJson, Map.class);
                } catch (Exception e) {
                    // 忽略元数据解析错误
                    metadata = Map.of();
                }
            }
            if (metadata == null) {
                metadata = Map.of();
            }
            
            // 根据消息类型创建对应的Message对象
            return switch (messageType) {
                case "USER" -> new UserMessage(content, metadata);
                case "ASSISTANT" -> new AssistantMessage(content, metadata);
                default -> new UserMessage(content, metadata); // 默认为用户消息
            };
        }
    }

    /**
     * 获取会话统计信息
     *
     * @param conversationId 会话ID
     * @return 消息数量
     */
    public int getMessageCount(String conversationId) {
        try {
            String sql = "SELECT COUNT(*) FROM chat_memory WHERE conversation_id = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, conversationId);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("获取消息数量失败: conversationId={}", conversationId, e);
            return 0;
        }
    }

    /**
     * 获取活跃会话列表
     *
     * @param limit 限制数量
     * @return 会话ID列表
     */
    public List<String> getActiveSessions(int limit) {
        try {
            String sql = """
                SELECT conversation_id 
                FROM chat_sessions 
                WHERE status = 'ACTIVE' 
                ORDER BY last_activity_at DESC 
                LIMIT ?
                """;
            return jdbcTemplate.queryForList(sql, String.class, limit);
        } catch (Exception e) {
            log.error("获取活跃会话列表失败", e);
            return List.of();
        }
    }

    /**
     * 清理过期会话（超过指定天数未活动）
     *
     * @param days 天数
     * @return 清理的会话数量
     */
    public int cleanupExpiredSessions(int days) {
        try {
            // 标记过期会话为已删除
            String updateSql = """
                UPDATE chat_sessions 
                SET status = 'DELETED', updated_at = CURRENT_TIMESTAMP
                WHERE status = 'ACTIVE' 
                AND last_activity_at < CURRENT_TIMESTAMP - INTERVAL '%d days'
                """.formatted(days);
            
            int updatedCount = jdbcTemplate.update(updateSql);
            
            // 删除过期会话的聊天记忆
            String deleteSql = """
                DELETE FROM chat_memory 
                WHERE conversation_id IN (
                    SELECT conversation_id FROM chat_sessions WHERE status = 'DELETED'
                )
                """;
            
            int deletedCount = jdbcTemplate.update(deleteSql);
            
            log.info("清理过期会话: 标记删除{}个会话, 删除{}条消息", updatedCount, deletedCount);
            return updatedCount;
            
        } catch (Exception e) {
            log.error("清理过期会话失败", e);
            return 0;
        }
    }
}
