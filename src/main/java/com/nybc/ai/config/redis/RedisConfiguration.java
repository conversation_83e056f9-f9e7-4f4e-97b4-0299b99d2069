package com.nybc.ai.config.redis;

import io.netty.channel.nio.NioEventLoopGroup;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.redisson.config.TransportMode;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;

import static com.nybc.common.tool.json.JsonConfig.OBJECT_MAPPER;

/**
 * 类描述：redis cache 缓存 + Stream配置
 *
 * <AUTHOR> 庆之
 * @version : 2.0
 */
@Configuration
@Slf4j
@EnableCaching
public class RedisConfiguration implements CachingConfigurer {

    private static final String REDIS_PROTOCOL_PREFIX = "redis://";

    @Resource
    private RedisProperties redisProperties;

    @Resource(name = "nybcExecutor")
    private ExecutorService executorService;

    /**
     * 重写cache key 生成方式 时间+class方法信息
     *
     * @return
     */
    @Override
    public KeyGenerator keyGenerator() {
        return (o, method, objects) -> {
            StringBuilder builder = new StringBuilder();
            builder.append(o.getClass().getName()).append(method.getName());
            for (Object obj : objects) {
                builder.append(obj.toString());
            }
            return builder.toString();
        };
    }

    @Bean(name = "redisson")
    public RedissonClient redisson() {
        Config config = new Config();
        config.setThreads(8)
                .setNettyThreads(16)
                .setExecutor(executorService)
                .setTransportMode(TransportMode.NIO)
                .setEventLoopGroup(new NioEventLoopGroup(8))
                .setCodec(new JsonJacksonCodec(OBJECT_MAPPER))
                .useSingleServer()
                .setAddress(REDIS_PROTOCOL_PREFIX + redisProperties.getHost() + ":" + redisProperties.getPort())
                .setConnectTimeout(((Long) redisProperties.getTimeout().toMillis()).intValue())
                .setDatabase(redisProperties.getDatabase())
                .setPassword(StringUtils.isNotBlank(redisProperties.getPassword())
                             ? redisProperties.getPassword() : null)
                .setTimeout(10000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setSubscriptionsPerConnection(5)
                .setClientName("Nybc_EDU_Redisson_Stream")
                .setIdleConnectionTimeout(10000)
                .setSubscriptionConnectionMinimumIdleSize(5)
                .setSubscriptionConnectionPoolSize(10)
                .setConnectionMinimumIdleSize(10)
                .setConnectionPoolSize(20)
                .setDnsMonitoringInterval(5000);
        
        log.info("初始化Redis配置 (支持Stream处理)");
        return Redisson.create(config);
    }

    /**
     * 整合spring-cache
     */
    @Bean
    public CacheManager cacheManager(RedissonClient redissonClient) {
        Map<String, CacheConfig> config = new HashMap<>();
        // 增加Git统计相关的缓存配置
        config.put("Nybc_GateWay", new CacheConfig(30 * 60 * 1000, 10 * 60 * 1000));
        config.put("GitStats", new CacheConfig(60 * 60 * 1000, 30 * 60 * 1000)); // Git统计缓存1小时
        config.put("ProjectInfo", new CacheConfig(30 * 60 * 1000, 15 * 60 * 1000)); // 项目信息缓存30分钟
        config.put("UserContribution", new CacheConfig(2 * 60 * 60 * 1000, 60 * 60 * 1000)); // 用户贡献缓存2小时
        
        return new RedissonSpringCacheManager(redissonClient, config);
    }

    /**
     * Redis Stream配置属性
     */
    @Bean
    @ConfigurationProperties(prefix = "app.redis.stream")
    public StreamProperties streamProperties() {
        return new StreamProperties();
    }

    /**
     * Stream属性配置类
     */
    @Setter
    @Getter
    public static class StreamProperties {
        
        private boolean enabled = true;
        private int consumerThreads = 8;
        private int batchSize = 50;
        private long retryInterval = 5000;
        private int maxRetries = 3;
        private long monitorInterval = 300000; 
        private long maintenanceInterval = 3600000;
        
        // Stream TTL配置
        private StreamTtlConfig ttl = new StreamTtlConfig();
        
        // Git统计配置
        private GitStatsConfig gitStats = new GitStatsConfig();

        /**
         * Stream TTL配置
         */
        @Setter
        @Getter
        public static class StreamTtlConfig {
            private long projectStats = 86400; 
            private long userContribution = 604800; 
            private long branchStats = 259200; 
            private long codeQuality = 86400; 
            private long taskStats = 86400; 
            private long deadLetter = 2592000; 

        }
        
        /**
         * Git统计配置
         */
        @Setter
        @Getter
        public static class GitStatsConfig {
            
            private boolean asyncMode = true;
            private int batchSize = 50;
            private int memoryLimit = 500; // MB
            private CacheConfig cache = new CacheConfig();
            private PerformanceConfig performance = new PerformanceConfig();
            private MonitorConfig monitor = new MonitorConfig();

            /**
             * 缓存配置
             */
            @Setter
            @Getter
            public static class CacheConfig {
                
                private long defaultTtl = 600;
                private int maxSize = 1000;
                private boolean asyncRefresh = true;

            }
            
            /**
             * 性能配置
             */
            @Setter
            @Getter
            public static class PerformanceConfig {
                
                private int maxConcurrentTasks = 10;
                private int threadPoolSize = 16;
                private int queueCapacity = 500;

            }
            
            /**
             * 监控配置
             */
            @Setter
            @Getter
            public static class MonitorConfig {
                private int memoryAlertThreshold = 80; 
                private int cacheHitRateThreshold = 60;

            }
        }
    }

    @Override
    @Bean
    @Lazy
    public CacheErrorHandler errorHandler() {
        // 异常处理，当Redis发生异常时，打印日志，但是程序正常走
        return new CacheErrorHandler() {
            @Override
            public void handleCacheGetError(@NotNull RuntimeException e, @NotNull Cache cache, @NotNull Object key) {
                log.error("Redis缓存Get key -> [{}]发生异常-->{}", key, e);
            }

            @Override
            public void handleCachePutError(@NotNull RuntimeException e, @NotNull Cache cache, @NotNull Object key, Object value) {
                log.error("Redis缓存Put key -> [{}]；value -> [{}]", key, value, e);
            }

            @Override
            public void handleCacheEvictError(RuntimeException e, Cache cache, Object key) {
                log.error("Redis缓存remove key -> [{}]", key, e);
            }

            @Override
            public void handleCacheClearError(RuntimeException e, Cache cache) {
                log.error("Redis occur 异常", e);
            }
        };
    }
}