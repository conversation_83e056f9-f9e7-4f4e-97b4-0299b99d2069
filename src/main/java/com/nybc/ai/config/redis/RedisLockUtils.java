package com.nybc.ai.config.redis;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类描述：redis单节点锁
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Service
public class RedisLockUtils {

    /**
     * 用来存放自动生成的uuid
     */
    private final TransmittableThreadLocal<Set<String>> threadValue = new TransmittableThreadLocal<>();

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 获取分布式锁
     *
     * @param key 键
     */
    public void acquireLock(String key) {
        acquireLock(key, 60, 100, 30, TimeUnit.SECONDS);
    }

    /**
     * 获取分布式锁
     *
     * @param key 键
     */
    public void acquireLock2(String key) {
        acquireLock(key, 60, 100, 90, TimeUnit.SECONDS);
    }

    /**
     * 获取一次性分布式锁
     *
     * @param key 键
     */
    public boolean acquireLockOnce(String key, Long leaseTime, TimeUnit unit) {
        RLock rlock = redissonClient.getLock(key);
        try {
            return rlock.tryLock(-1, leaseTime, unit);
        } catch (InterruptedException e) {
            log.error("获取单次锁错误 key:" + key, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 释放一次性分布式锁
     *
     * @param key 键
     */
    public void releaseLockOnce(String key) {
        RLock rlock = redissonClient.getLock(key);
        rlock.unlock();
    }

    /**
     * 获取分布式锁
     *
     * @param keys 键集合
     */
    public void acquireLock(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        keys = keys.stream().distinct().sorted().collect(Collectors.toList());
        keys.forEach(key -> acquireLock(key, 30, 100, 30, TimeUnit.SECONDS));
    }

    /**
     * 释放分布式锁
     *
     * @param keys 键集合
     */
    public void releaseLock(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        keys = keys.stream().distinct().sorted().collect(Collectors.toList());
        keys.forEach(key -> {
            String redisValue = stringRedisTemplate.opsForValue().get(key);
            boolean pass = StringUtils.isNotEmpty(redisValue) && threadValue.get() != null && threadValue.get().contains(redisValue);
            if (pass) {
                stringRedisTemplate.delete(key);
            }
        });
        threadValue.remove();
    }

    /**
     * 释放分布式锁
     *
     * @param key 键
     */
    public void releaseLock(String key) {
        releaseLock(Lists.newArrayList(key));
    }

    /**
     * 执行
     *
     * @param function 需要分布式锁的代码块
     * @param key      键
     */
    public <R> R execute(Function<StringRedisTemplate, R> function, String key) {
        acquireLock(key);
        try {
            return function.apply(stringRedisTemplate);
        } finally {
            releaseLock(key);
        }
    }

    /**
     * 执行
     *
     * @param function 需要分布式锁的代码块
     * @param keys     键集合
     */
    public <R> R execute(Function<StringRedisTemplate, R> function, List<String> keys) {
        acquireLock(keys);
        try {
            return function.apply(stringRedisTemplate);
        } finally {
            releaseLock(keys);
        }
    }

    /**
     * 获取分布式锁
     *
     * @param key           键
     * @param maxRetryCount 重试次数
     * @param gap           每次重试间隔 毫秒
     * @param expireTime    键过期时间
     * @param unit          键过期时间单位
     */
    public void acquireLock(String key, int maxRetryCount, int gap, int expireTime, TimeUnit unit) {
        acquireLock(key, maxRetryCount, gap, expireTime, unit, "业务繁忙，请稍后再试");
    }

    /**
     * 获取分布式锁
     *
     * @param key              键
     * @param maxRetryCount    重试次数
     * @param gap              每次重试间隔 毫秒
     * @param expireTime       键过期时间
     * @param unit             键过期时间单位
     * @param exceptionMessage 自定义报错的信息
     */
    public void acquireLock(String key, int maxRetryCount, int gap, int expireTime, TimeUnit unit, String exceptionMessage) {
        if (threadValue.get() == null) {
            threadValue.set(new HashSet<>());
        }
        String tag = IdTimeService.getId().toString();
        threadValue.get().add(tag);
        Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(key, tag, expireTime, unit);
        int count = 1;
        while (success != null && !success && count < maxRetryCount) {
            try {
                log.debug("重试获取锁 key -> {} count -> {}", key, count);
                Thread.sleep(gap);
                success = stringRedisTemplate.opsForValue().setIfAbsent(key, tag, expireTime, unit);
                count += 1;
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        if (count > maxRetryCount) {
            log.error("分布式锁 重试 {} 次获取锁 {} 失败", count, key);
            throw new RuntimeException(exceptionMessage);
        }
    }


}
