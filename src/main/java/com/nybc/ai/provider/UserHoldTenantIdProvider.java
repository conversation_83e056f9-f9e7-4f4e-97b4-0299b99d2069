package com.nybc.user.provider;

import com.nybc.edu.common.provider.TenantIdProvider;
import com.nybc.user.context.UserHold;
import org.springframework.stereotype.Component;

/**
 * 基于UserHold的租户ID提供者实现
 */
@Component
public class UserHoldTenantIdProvider implements TenantIdProvider {

    @Override
    public Long getTenantId() {
        // 直接委托给UserHold获取租户ID
        return UserHold.getTenantId();
    }
} 