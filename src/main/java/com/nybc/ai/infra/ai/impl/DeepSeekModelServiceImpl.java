package com.nybc.ai.infra.ai.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nybc.ai.infra.ai.AiModelService;
import com.nybc.ai.infra.ai.AiProviderProperties;
import jakarta.annotation.PostConstruct;
import okhttp3.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 类描述：DeepSeek模型调用服务的实现（已废弃）
 *
 * @deprecated 请使用UnifiedAiService替代
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Service
@ConditionalOnProperty(name = "app.ai.legacy.model-service.enabled", havingValue = "true", matchIfMissing = false)
@Deprecated
public class DeepSeekModelServiceImpl implements AiModelService {

    private static final String PROVIDER_NAME = "deepseek";
    private final AiProviderProperties aiProviderProperties;
    private AiProviderProperties.ProviderConfig config;
    private OkHttpClient httpClient;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public DeepSeekModelServiceImpl(AiProviderProperties aiProviderProperties) {
        this.aiProviderProperties = aiProviderProperties;
    }

    @PostConstruct
    private void init() {
        this.config = aiProviderProperties.getProvider().get(PROVIDER_NAME);
        if (config == null || !config.isEnabled() || !StringUtils.hasText(config.getApiKey())) {
            // 如果未配置或未启用，则不初始化
            return;
        }
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    @Override
    public String call(String prompt, Map<String, Object> parameters) {
        if (httpClient == null) {
            throw new IllegalStateException("DeepSeek服务未配置或未启用。");
        }

        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");

        Map<String, Object> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", prompt);

        Map<String, Object> requestBodyMap = new HashMap<>();
        requestBodyMap.put("model", config.getModel());
        requestBodyMap.put("messages", Collections.singletonList(message));
        if (parameters != null) {
            requestBodyMap.putAll(parameters);
        }

        try {
            String jsonBody = objectMapper.writeValueAsString(requestBodyMap);
            RequestBody body = RequestBody.create(jsonBody, mediaType);
            Request request = new Request.Builder()
                    .url(config.getEndpoint())
                    .post(body)
                    .addHeader("Authorization", "Bearer " + config.getApiKey())
                    .addHeader("Content-Type", "application/json")
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful() || response.body() == null) {
                    throw new IOException("调用DeepSeek API失败: " + response);
                }
                // TODO: 更精细地解析JSON，目前直接返回choice中的content
                Map<String,Object> responseMap = objectMapper.readValue(response.body().string(), Map.class);
                return (String) ((Map)((java.util.List)responseMap.get("choices")).get(0)).get("message.content");

            }
        } catch (IOException e) {
            throw new RuntimeException("调用DeepSeek API时发生网络或IO异常", e);
        }
    }

    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }
} 