package com.nybc.ai.infra.ai;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类描述：AI模型服务的工厂类
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Component
public class AiModelServiceFactory {

    private final Map<String, AiModelService> services;

    public AiModelServiceFactory(List<AiModelService> serviceList) {
        this.services = serviceList.stream()
                .collect(Collectors.toMap(AiModelService::getProviderName, Function.identity()));
    }

    /**
     * 根据Provider名称获取对应的服务实例
     *
     * @param providerName "deepseek", "zhipu", etc.
     * @return 对应的服务实例
     * @throws IllegalArgumentException 如果找不到指定的服务
     */
    public AiModelService getService(String providerName) {
        AiModelService service = services.get(providerName);
        if (service == null) {
            throw new IllegalArgumentException("未找到或未配置名称为 '" + providerName + "' 的AI服务。");
        }
        return service;
    }
} 