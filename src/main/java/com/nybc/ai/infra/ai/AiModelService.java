package com.nybc.ai.infra.ai;

import java.util.Map;

/**
 * 类描述：通用AI模型调用服务接口
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
public interface AiModelService {

    /**
     * 调用AI模型并获取响应
     *
     * @param prompt     完整的请求Prompt
     * @param parameters 其他调用参数，如 temperature, max_tokens 等
     * @return AI模型的原始响应字符串
     */
    String call(String prompt, Map<String, Object> parameters);

    /**
     * 获取此服务对应的Provider名称
     *
     * @return "deepseek", "zhipu", etc.
     */
    String getProviderName();
} 