package com.nybc.ai.infra.ai;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 类描述：映射 application.yml 中 `spring.ai.provider` 的配置
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 **/
@Data
@Component
@ConfigurationProperties(prefix = "spring.ai.provider")
public class AiProviderProperties {

    private Map<String, ProviderConfig> provider;

    @Data
    public static class ProviderConfig {
        private boolean enabled = true; // 默认启用
        private String apiKey;
        private String endpoint;
        private String model;
        private String baseUrl;
    }
} 