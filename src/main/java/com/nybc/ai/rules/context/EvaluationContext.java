package com.nybc.ai.rules.context;

import com.nybc.ai.rules.enums.SubmissionType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;

/**
 * 类描述：评估上下文
 * 这个类是规则引擎的核心数据对象（事实对象）。
 * 它在整个LiteFlow执行链中流动，每个组件都可以读取和修改它。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Data
@Accessors(chain = true)
public class EvaluationContext implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 代码是否编译成功。
     * 此状态由 CodeCompileCmp 组件设置，并由 IsCompilationSuccessfulCondition 条件组件使用。
     */
    private boolean compilationSuccessful = false;

    /**
     * 当前执行的规则链名称
     */
    private String chainName;

    // --- 核心输入信息 ---
    /**
     * 学生ID
     */
    private Long studentId;
    /**
     * 租户ID (用于决定使用哪套规则)
     */
    private Long tenantId;
    /**
     * 作业ID
     */
    private Long assignmentId;
    /**
     * 提交类型 (代码/文档/图片)
     */
    private SubmissionType submissionType;
    /**
     * 提交的原始内容
     */
    private String submissionContent;

    // --- 各审核链的分析指标 (由组件填充) ---
    // 代码指标
    private Integer syntaxErrorCount = 0;
    private Double cyclomaticComplexity = 0.0;
    private Double plagiarismScore = 0.0;

    // 文档指标
    private Boolean isContentComplete = true;
    private Integer formatViolationCount = 0;
    private Map<String, Boolean> requiredSections = new HashMap<>();

    // 图像指标
    private List<String> detectedElements = new ArrayList<>();
    private Double designPrincipleScore = 100.0;

    // --- 新增：代码专项深入分析指标 ---
    /**
     * 数据库设计评分 (0-100)
     */
    private Double dbDesignScore;
    /**
     * 数据库连接是否成功
     */
    private Boolean dbConnectionSuccessful;
    /**
     * 技术架构评分 (0-100)
     */
    private Double techArchitectureScore;
    /**
     * 需求完成度评分 (0-100)
     */
    private Double requirementCompletionScore;

    // --- 新增：测试专项分析指标 ---
    /**
     * 测试用例覆盖度 (0-100)
     */
    private Double testCaseCoverage;
    /**
     * 自动化测试脚本逻辑评分 (0-100)
     */
    private Double autoTestScriptScore;
    /**
     * 性能测试报告评分 (0-100)
     */
    private Double perfTestReportScore;

    // --- 规则引擎的输出 ---
    /**
     * 最终评分 (初始为100, 规则可以对其进行扣分或加分)
     */
    private double finalScore = 100.0;

    /**
     * 反馈信息列表 (各个组件和规则会向这里添加反馈建议)
     */
    private List<String> feedbackMessages = new ArrayList<>();

    /**
     * 触发的规则标签 (用于后续RAG检索或数据分析)
     */
    private Set<String> triggeredRuleTags = new HashSet<>();

    /**
     * 用于存储各个评估组件的结果
     */
    private Map<String, Object> results = new HashMap<>();

    /**
     * 向上下文中添加评估结果
     *
     * @param key   结果的键
     * @param value 结果的值
     */
    public void addResult(String key, Object value) {
        this.results.put(key, value);
    }
} 