package com.nybc.ai.rules.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "目录解析请求")
public class DirectoryParseRequest {

    @NotBlank(message = "目录路径不能为空")
    @Schema(description = "要解析的服务器本地目录的绝对路径")
    private String directoryPath;

    @NotBlank(message = "提示词不能为空")
    @Schema(description = "要向AI提出的问题")
    private String prompt;
} 