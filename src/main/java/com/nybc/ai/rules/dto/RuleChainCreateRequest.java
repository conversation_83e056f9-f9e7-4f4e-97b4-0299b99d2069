package com.nybc.ai.rules.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * DTO类：创建或更新规则链请求
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Data
@Schema(description = "用于动态创建或更新规则链的请求")
public class RuleChainCreateRequest {

    @NotBlank(message = "规则链名称不能为空")
    @Schema(description = "规则链的唯一名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "myCustomCodeChain")
    private String chainName;

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "此规则链所属的租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Long tenantId;

    @Schema(description = "关联的特定作业ID，如果为null则为该租户的默认链", example = "101")
    private Long assignmentId;

    @NotEmpty(message = "组件列表不能为空")
    @Schema(description = "按执行顺序排列的LiteFlow组件ID列表",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "[\"initContextCmp\", \"codeCompileCmp\", \"syntaxCheckCmp\"]")
    private List<String> componentIds;
} 