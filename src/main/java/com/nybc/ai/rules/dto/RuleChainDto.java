package com.nybc.ai.rules.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "规则链数据传输对象")
public class RuleChainDto {

    @NotBlank(message = "规则链名称不能为空")
    @Schema(description = "规则链的唯一名称", example = "my_custom_chain")
    private String chainName;

    @NotBlank(message = "规则链的EL表达式不能为空")
    @Schema(description = "LiteFlow的规则表达式(EL)", example = "THEN(componentA, componentB, componentC);")
    private String elData;
} 