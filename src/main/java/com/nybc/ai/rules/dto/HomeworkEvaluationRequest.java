package com.nybc.ai.rules.dto;

import com.nybc.ai.rules.enums.SubmissionType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * DTO类：作业评估请求
 * 用于封装前端/客户端发起的作业评估请求的数据。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Data
@Schema(description = "作业评估请求的数据传输对象")
public class HomeworkEvaluationRequest {

    @NotNull(message = "学生ID不能为空")
    @Schema(description = "提交作业的学生ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long studentId;

    @Schema(description = "要执行的LiteFlow规则链的名称。如果为空，将根据租户和作业ID自动匹配默认规则链。")
    private String chainName;

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "所属的租户ID (例如学校或学院的ID)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Long tenantId;

    @NotNull(message = "作业ID不能为空")
    @Schema(description = "当前作业的唯一ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "88")
    private Long assignmentId;

    @NotNull(message = "提交类型不能为空")
    @Schema(description = "作业提交的类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private SubmissionType submissionType;

    @NotBlank(message = "提交内容不能为空")
    @Schema(description = "作业的原始内容 (例如：代码字符串、文档文本)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String submissionContent;
} 