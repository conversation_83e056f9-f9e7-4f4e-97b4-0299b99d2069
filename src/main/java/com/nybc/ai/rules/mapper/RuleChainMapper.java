package com.nybc.ai.rules.mapper;

import com.nybc.ai.rules.entity.RuleChain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Mapper接口：规则链仓库
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Repository
public interface RuleChainMapper {

    /**
     * 根据租户ID和作业ID查询规则链，按作业ID降序排序，实现优先级匹配
     *
     * @param tenantId     租户ID
     * @param assignmentId 作业ID
     * @return 匹配的规则链列表
     */
    List<RuleChain> findByTenantIdAndAssignmentIdWithPriority(@Param("tenantId") Long tenantId, @Param("assignmentId") Long assignmentId);

    /**
     * 根据租户ID查询规则链
     *
     * @param tenantId 租户ID
     * @return 匹配的规则链列表
     */
    List<RuleChain> findByTenantId(@Param("tenantId") Long tenantId);

    /**
     * 插入或更新规则链。
     * 如果链名已存在，则更新；否则，插入新记录。
     *
     * @param ruleChain 规则链对象
     */
    void insertOrUpdate(RuleChain ruleChain);

    /**
     * 查询所有的规则链路
     *
     * @return
     */
    List<RuleChain> findAll();

    /**
     * 根据租户ID和作业ID查询规则链
     *
     * @param tenantId
     * @param assignmentId
     * @return
     */
    String findChain(@Param("tenantId") Long tenantId, @Param("assignmentId") Long assignmentId);

}