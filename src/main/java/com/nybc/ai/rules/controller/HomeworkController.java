package com.nybc.ai.rules.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.rules.context.EvaluationContext;
import com.nybc.ai.rules.dto.HomeworkEvaluationRequest;
import com.nybc.ai.rules.service.LiteFlowRuleEngineService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller类：作业评估API
 * 提供对外暴露的RESTful接口，用于接收作业评估请求。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/homework")
@Tag(name = "作业智能评估接口", description = "提供作业提交、评估与反馈功能")
public class HomeworkController {

    @Resource
    private LiteFlowRuleEngineService ruleEngineService;

    /**
     * 提交作业进行自动化评估
     *
     * @param request 包含作业内容和评估所需元数据的请求
     * @return 包含评分、反馈和分析结果的统一响应对象
     */
    @PostMapping("/evaluate")
    @Operation(summary = "提交作业进行评估", description = "根据指定的规则链对提交的作业内容进行分析和评分")
    public ResultInfo<EvaluationContext> evaluateHomework(@Valid @RequestBody HomeworkEvaluationRequest request) {
        try {
            log.info("接收到作业评估请求: 学生ID[{}], 租户ID[{}]", request.getStudentId(), request.getTenantId());
            EvaluationContext resultContext = ruleEngineService.execute(request);
            log.info("作业评估完成: 学生ID[{}], 最终得分[{}]", request.getStudentId(), resultContext.getFinalScore());
            return ResultInfo.success(resultContext);
        } catch (Exception e) {
            log.error("评估作业时发生异常: {}", e.getMessage(), e);
            return ResultInfo.error("评估过程中发生内部错误，请稍后重试。");
        }
    }
} 