package com.nybc.ai.rules.controller;

import com.nybc.ai.common.model.ResultInfo;
import com.nybc.ai.rules.dto.RuleChainDto;
import com.nybc.ai.rules.entity.RuleChain;
import com.nybc.ai.rules.service.RuleAdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller类：规则链管理API
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@RestController
@RequestMapping("/api/rules/admin")
@Tag(name = "规则链管理", description = "提供LiteFlow规则链的动态创建、查询和更新功能")
public class RuleAdminController {

    private final RuleAdminService ruleAdminService;

    public RuleAdminController(RuleAdminService ruleAdminService) {
        this.ruleAdminService = ruleAdminService;
    }

    @PostMapping("/save")
    @Operation(summary = "创建或更新规则链", description = "如果链名已存在，则更新其EL表达式；否则创建新规则链。")
    public ResultInfo<Void> saveOrUpdateChain(@RequestBody RuleChainDto ruleChainDto) {
        ruleAdminService.saveOrUpdateChain(ruleChainDto.getChainName(), ruleChainDto.getElData());
        return ResultInfo.success();
    }

    @GetMapping("/list")
    @Operation(summary = "获取所有规则链")
    public ResultInfo<List<RuleChain>> getAllChains() {
        return ResultInfo.success(ruleAdminService.getAllChains());
    }

    @PostMapping("/reload")
    @Operation(summary = "手动重新加载所有规则", description = "在数据库层面修改规则后，可调用此接口使规则在系统中生效。")
    public ResultInfo<Void> reloadRules() {
        ruleAdminService.reloadRules();
        return ResultInfo.success();
    }
} 