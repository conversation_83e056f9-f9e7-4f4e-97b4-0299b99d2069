package com.nybc.ai.rules.service;

import com.nybc.ai.rules.dto.RuleChainCreateRequest;
import com.nybc.ai.rules.entity.RuleChain;
import com.nybc.ai.rules.mapper.RuleChainMapper;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.FlowBus;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 服务类：规则管理服务
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Service
public class RuleAdminService {

    @Resource
    private RuleChainMapper ruleChainMapper;

    @Resource
    private FlowExecutor flowExecutor;

    @Value("${spring.application.name}")
    private String applicationName;

    /**
     * 应用启动时，自动创建并加载企业级复杂规则链示例
     */
    @PostConstruct
    public void createComprehensiveEvaluationChainOnStartup() {
        // 子链：代码分析
        final String codeAnalysisSubChain = "THEN(codeCompile, IF(isCompilationSuccessful, THEN(syntaxCheck, logicAnalysis)));";
        saveOrUpdateChain("code_analysis_sub_chain", codeAnalysisSubChain);

        // 子链：元数据分析
        final String metaAnalysisSubChain = "THEN(plagiarismCheck, taskUniquenessCheck);";
        saveOrUpdateChain("meta_analysis_sub_chain", metaAnalysisSubChain);

        // 主链：综合评估
        final String mainChain = "THEN(initContext, WHEN(code_analysis_sub_chain, meta_analysis_sub_chain), summarizeAndScore);";
        saveOrUpdateChain("comprehensive_evaluation_chain", mainChain);

        // 重新加载规则，确保新添加的链生效
        flowExecutor.reloadRule();
    }

    /**
     * 创建或更新规则链
     *
     * @param request 创建请求
     */
    @Transactional
    public void createOrUpdateChain(RuleChainCreateRequest request) {
        // 1. 根据组件ID列表生成LiteFlow的EL表达式XML
        String elData = buildElData(request.getChainName(), request.getComponentIds());

        // 2. 构建实体对象
        RuleChain ruleChain = new RuleChain()
                .setApplicationName(applicationName)
                .setChainName(request.getChainName())
                .setTenantId(request.getTenantId())
                .setAssignmentId(request.getAssignmentId())
                .setElData(elData);

        // 3. 保存到数据库
        ruleChainMapper.insertOrUpdate(ruleChain);

        // 4. 动态刷新LiteFlow的规则缓存，使新规则立即生效
        flowExecutor.reloadRule();
    }

    /**
     * 根据组件ID列表构建LiteFlow的XML格式的EL-Data
     */
    private String buildElData(String chainName, java.util.List<String> componentIds) {
        String thenBody = String.join(",\n            ", componentIds);
        return String.format("""
                <?xml version="1.0" encoding="UTF-8"?>
                <flow>
                    <chain name="%s">
                        THEN(
                            %s
                        );
                    </chain>
                </flow>""", chainName, thenBody);
    }

    public void saveOrUpdateChain(String chainName, String elData) {
        RuleChain ruleChain = new RuleChain();
        ruleChain.setApplicationName(applicationName);
        ruleChain.setChainName(chainName);
        ruleChain.setElData(elData);
        ruleChainMapper.insertOrUpdate(ruleChain);
        // 保存后立即刷新，确保规则生效
        reloadRules();
    }

    public List<RuleChain> getAllChains() {
        return ruleChainMapper.findAll();
    }

    public void reloadRules() {
        flowExecutor.reloadRule();
    }
} 