package com.nybc.ai.rules.service;

import com.nybc.ai.rules.context.EvaluationContext;
import com.nybc.ai.rules.dto.HomeworkEvaluationRequest;
import com.nybc.ai.rules.entity.RuleChain;
import com.nybc.ai.rules.mapper.RuleChainMapper;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * 服务类：LiteFlow规则引擎服务
 * 负责接收业务请求，自动查找并调用LiteFlow执行器，处理返回结果。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Service
public class LiteFlowRuleEngineService {

    private static final Long PLATFORM_TENANT_ID = 1L; // 假设1为平台通用租户ID

    @Resource
    private FlowExecutor flowExecutor;

    @Resource
    private RuleChainMapper ruleChainMapper;

    /**
     * 执行作业评估
     *
     * @param request 作业评估的请求数据
     * @return 经过规则链处理后的评估上下文
     */
    public EvaluationContext execute(HomeworkEvaluationRequest request) {
        // 1. 自动查找需要执行的规则链
        String chainName = findChain(request.getTenantId(), request.getAssignmentId())
                .orElseThrow(() -> new IllegalStateException("未能为租户ID " + request.getTenantId() + " 和作业ID " + request.getAssignmentId() + " 找到匹配的规则链"));

        // 2. 创建用于本次执行的评估上下文
        EvaluationContext context = new EvaluationContext();
        BeanUtils.copyProperties(request, context);
        log.info("自动匹配到规则链: [{}], 开始执行... 租户ID: {}", chainName, request.getTenantId());
        // 3. 调用LiteFlow执行器
        LiteflowResponse response = flowExecutor.execute2Resp(chainName, context);

        // 4. 处理执行结果
        if (response.isSuccess()) {
            log.info("规则链 [{}] 执行成功", chainName);
            return response.getContextBean(EvaluationContext.class);
        } else {
            log.error("规则链 [{}] 执行失败! 异常: {}", chainName, response.getMessage(), response.getCause());
            throw new RuntimeException("规则链执行失败: " + response.getMessage(), response.getCause());
        }
    }

    /**
     * 查找规则链，遵循"最具体"原则
     * 1. 找租户的特定作业规则
     * 2. 找租户的通用规则
     * 3. 找平台的特定作业规则
     * 4. 找平台的通用规则
     */
    private Optional<String> findChain(Long tenantId, Long assignmentId) {
        // 1 & 2: 查找租户的特定规则，如果找不到则查找其通用规则 (MyBatis中已合并为一个查询)
        List<RuleChain> chains = ruleChainMapper.findByTenantIdAndAssignmentIdWithPriority(tenantId, assignmentId);
        if (!CollectionUtils.isEmpty(chains)) {
            return Optional.of(chains.getFirst().getChainName());
        }

        // 如果当前租户不是平台通用租户，则尝试回退到平台通用规则
        if (!tenantId.equals(PLATFORM_TENANT_ID)) {
            log.warn("租户 {} 没有找到任何专属规则, 将回退到平台通用规则进行查找。", tenantId);
            // 3 & 4: 查找平台的特定规则，如果找不到则查找其通用规则
            List<RuleChain> platformChains = ruleChainMapper.findByTenantIdAndAssignmentIdWithPriority(PLATFORM_TENANT_ID, assignmentId);
            if (!CollectionUtils.isEmpty(platformChains)) {
                return Optional.of(platformChains.getFirst().getChainName());
            }
        }
        return Optional.empty();
    }
} 