package com.nybc.ai.rules.component.test;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：测试用例创建与需求匹配规则
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("testCaseCheck")
public class TestCaseCheckCmp extends NodeComponent {
    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("TestCaseCheckCmp => 模拟检查测试用例与需求的匹配度...");
        // 模拟分析测试用例对需求的覆盖情况
        context.setTestCaseCoverage(85.0);
        context.getFeedbackMessages().add("测试用例检查：用例覆盖了85%的核心需求。建议针对用户权限模块补充更多异常场景的测试用例。");
        context.addResult("test_case_check", "测试用例覆盖度98%");
    }
} 