package com.nybc.ai.rules.component;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 组件：代码编译
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("codeCompile")
public class CodeCompileCmp extends NodeComponent {

    @Override
    public void process() {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("CodeCompileCmp => 开始执行代码编译检查...");

        // 模拟逻辑
        if (context.getSubmissionContent() != null && context.getSubmissionContent().contains("编译失败")) {
            context.setCompilationSuccessful(false);
            context.addResult("compile_check", "编译失败。");
            log.error("CodeCompileCmp => 编译失败。");
        } else {
            context.setCompilationSuccessful(true);
            log.info("CodeCompileCmp => 编译成功。");
        }
    }

}