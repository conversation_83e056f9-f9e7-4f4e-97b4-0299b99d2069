package com.nybc.ai.rules.component;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：逻辑分析
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("logicAnalysisCmp")
public class LogicAnalysisCmp extends NodeComponent {

    /**
     * 组件核心处理逻辑
     * <p>
     * 真实场景下，这里可能会：
     * 1. 计算代码的圈复杂度。
     * 2. 调用大语言模型（LLM）对代码的实现逻辑、效率、可读性进行分析和打分。
     * <p>
     * 这里我们使用简单的字符串检查来模拟计算圈复杂度。
     */
    @Override
    public void process() {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("LogicAnalysisCmp => 开始执行逻辑分析...");

        // 模拟逻辑：如果代码中包含 "complex_logic" 关键字，则认为圈复杂度较高
        if (context.getSubmissionContent() != null && context.getSubmissionContent().contains("高复杂度")) {
            double complexity = 12.5; // 模拟计算出的圈复杂度
            context.setCyclomaticComplexity(complexity);
            context.getFeedbackMessages().add("代码逻辑较为复杂 (圈复杂度: " + complexity + ")，可以考虑进行重构简化。");
            context.getTriggeredRuleTags().add("high_complexity_detected");
            log.warn("LogicAnalysisCmp => 检测到高复杂度逻辑。");
        } else {
            context.setCyclomaticComplexity(4.0); // 默认复杂度
            log.info("LogicAnalysisCmp => 逻辑分析通过，复杂度较低。");
        }
    }
} 