package com.nybc.ai.rules.component.code;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：技术架构检查 (非AI)
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("techArchCheck")
public class TechArchCheckCmp extends NodeComponent {
    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("TechArchCheckCmp => 模拟执行技术架构检查...");
        // 模拟逻辑：检查是否包含 .gitignore, pom.xml/build.gradle 等
        if (!context.getSubmissionContent().contains(".gitignore")) {
            context.getFeedbackMessages().add("技术架构检查：项目缺少 .gitignore 文件，建议添加以忽略不必要的文件。");
        } else {
             context.getFeedbackMessages().add("技术架构检查：项目结构符合基本规范。");
        }
        context.addResult("tech_arch_check", "技术架构检查通过");
    }
} 