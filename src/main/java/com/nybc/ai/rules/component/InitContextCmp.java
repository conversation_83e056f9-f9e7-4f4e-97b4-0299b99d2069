package com.nybc.ai.rules.component;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：初始化上下文
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("initContextCmp")
public class InitContextCmp extends NodeComponent {

    /**
     * 组件核心处理逻辑
     * <p>
     * 在这个组件中，可以执行一些准备工作，例如：
     * 1. 根据传入的assignmentId，从数据库加载作业的具体要求（如必需章节、评分细则等）。
     * 2. 根据studentId加载学生历史提交数据，用于更复杂的分析。
     * 3. 对上下文进行预处理。
     * <p>
     * 这里我们仅作打印，演示该节点被执行。
     */
    @Override
    public void process() throws Exception {
        EvaluationContext context = getContextBean(EvaluationContext.class);
        log.info("InitContextCmp => 执行上下文初始化...");
        // 这里可以进行一些初始化操作，比如设置默认值
    }

}