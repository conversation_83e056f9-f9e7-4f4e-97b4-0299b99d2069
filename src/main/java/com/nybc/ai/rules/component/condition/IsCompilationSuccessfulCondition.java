package com.nybc.ai.rules.component.condition;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import org.springframework.stereotype.Component;

/**
 * 条件组件：该组件仅负责将"代码是否编译成功"的结果写入上下文。
 * <p>
 * 在EL规则中，应直接使用此结果进行判断，例如：
 * THEN(
 *     compilationChecker,
 *     IF(evaluationContext.isCompilationSuccessful, success_branch, failure_branch)
 * );
 *
 * <AUTHOR> 庆之
 * @version : 1.2
 */
@Component("compilationChecker")
public class IsCompilationSuccessfulCondition extends NodeComponent {

    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        // 此组件不再进行条件判断，而是确保isCompilationSuccessful的值已经存在于上下文中。
        // 真正的判断逻辑转移到EL表达式中。
        // 为了确保逻辑清晰，我们甚至可以在这里重新设置一次，但这通常是不必要的，
        // 因为上游组件应该已经设置过这个值了。
        // context.setCompilationSuccessful(some_new_check);
    }
} 