package com.nybc.ai.rules.component.code;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：数据库连接检查
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("dbConnectionCheck")
public class DbConnectionCheckCmp extends NodeComponent {
    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("DbConnectionCheckCmp => 模拟执行数据库连接检查...");
        context.addResult("db_connection_check", "数据库连接成功");
    }
} 