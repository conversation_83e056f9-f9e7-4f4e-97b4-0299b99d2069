package com.nybc.ai.rules.component;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：代码语法检查
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("syntaxCheckCmp")
public class SyntaxCheckCmp extends NodeComponent {

    /**
     * 组件核心处理逻辑
     * <p>
     * 在真实的业务场景中，这里会调用代码分析工具（如PMD, Checkstyle，或AI模型）
     * 来对`submissionContent`进行静态分析。
     * <p>
     * 这里我们使用简单的字符串检查来模拟。
     */
    @Override
    public void process() {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("SyntaxCheckCmp => 开始执行语法检查...");

        // 模拟逻辑：如果代码中包含 "syntax_error" 关键字，则认为存在语法错误
        if (context.getSubmissionContent().contains("syntax_error")) {
            int errorCount = 5; // 模拟发现5个错误
            context.setSyntaxErrorCount(errorCount);
            context.setFinalScore(context.getFinalScore() - (errorCount * 2)); // 每个错误扣2分
            context.getFeedbackMessages().add("检测到 " + errorCount + " 处语法错误，建议使用IDE进行检查。已扣 " + (errorCount * 2) + " 分。");
            context.getTriggeredRuleTags().add("syntax_error_detected");
            log.warn("SyntaxCheckCmp => 检测到语法错误。");
        } else {
            log.info("SyntaxCheckCmp => 语法检查通过。");
        }
    }
} 