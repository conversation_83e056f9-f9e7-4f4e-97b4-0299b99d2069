package com.nybc.ai.rules.component.test;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：自动化测试脚本逻辑AI检查
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("autoTestScriptCheck")
public class AutoTestScriptCheckCmp extends NodeComponent {
    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("AutoTestScriptCheckCmp => 模拟AI检查自动化测试脚本逻辑...");
        // 模拟调用LLM分析脚本的断言、等待机制等
        context.setAutoTestScriptScore(78.0);
        context.getFeedbackMessages().add("自动化脚本AI分析：脚本逻辑基本正确，但存在硬编码等待时间，可能导致执行不稳定，建议改为显式等待。");
        context.addResult("auto_test_script_check", "脚本逻辑清晰，断言准确");
    }
} 