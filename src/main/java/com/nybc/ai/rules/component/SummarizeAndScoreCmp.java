package com.nybc.ai.rules.component;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 组件：总结与评分
 * <p>
 * 该组件在所有分析步骤完成后执行，用于汇总结果并计算最终分数。
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("summarizeAndScore")
public class SummarizeAndScoreCmp extends NodeComponent {

    @Override
    public void process() throws Exception {
        EvaluationContext context = getContextBean(EvaluationContext.class);
        log.info("SummarizeAndScoreCmp => 开始进行总结和最终评分...");

        // 示例：根据前面的结果计算最终分数
        double finalScore = 100.0;
        if (context.getResults().containsKey("plagiarism")) {
            finalScore -= 20;
            log.warn("检测到抄袭，扣20分");
        }
        if (context.getResults().containsKey("syntax")) {
            finalScore -= 10;
            log.warn("存在语法错误，扣10分");
        }

        context.setFinalScore(finalScore);
        context.addResult("final_score", finalScore);
        log.info("最终评分为: {}", finalScore);
    }
} 