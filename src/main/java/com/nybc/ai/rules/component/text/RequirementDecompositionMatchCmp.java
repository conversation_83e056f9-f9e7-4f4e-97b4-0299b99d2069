package com.nybc.ai.rules.component.text;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：需求拆解与预设题目匹配度
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("requirementDecompositionMatch")
public class RequirementDecompositionMatchCmp extends NodeComponent {

    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("RequirementDecompositionMatchCmp => 模拟执行需求拆解匹配度检查...");
        context.getFeedbackMessages().add("需求拆解匹配度：分析完成，您的需求文档与预设题目的匹配度为95%。");
        // 模拟实现
        context.addResult("requirement_decomposition_match", 95.5);
    }
} 