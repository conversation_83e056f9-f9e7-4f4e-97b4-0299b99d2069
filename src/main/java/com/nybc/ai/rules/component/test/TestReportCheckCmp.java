package com.nybc.ai.rules.component.test;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：测试报告检查
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("testReportCheck")
public class TestReportCheckCmp extends NodeComponent {
    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("TestReportCheckCmp => 模拟执行测试报告检查...");
        context.addResult("test_report_check", "测试报告完整");
    }
} 