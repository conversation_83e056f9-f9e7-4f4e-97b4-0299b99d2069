package com.nybc.ai.rules.component.test;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：自动化测试结果检查
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("autoTestResultCheck")
public class AutoTestResultCheckCmp extends NodeComponent {
    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("AutoTestResultCheckCmp => 模拟执行自动化测试结果检查...");
        // 模拟逻辑: 解析测试报告，统计成功率
        if (context.getSubmissionContent().contains("all tests passed")) {
             context.getFeedbackMessages().add("自动化测试结果检查：所有测试用例均执行通过。");
        } else {
             context.setFinalScore(context.getFinalScore() - 10);
             context.getFeedbackMessages().add("自动化测试结果检查：检测到部分测试用例执行失败，已扣分。");
        }
        context.addResult("auto_test_result_check", "通过率99.8%");
    }
} 