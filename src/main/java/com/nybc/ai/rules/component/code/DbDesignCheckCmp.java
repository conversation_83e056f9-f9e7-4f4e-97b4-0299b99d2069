package com.nybc.ai.rules.component.code;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：数据库设计检查
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("dbDesignCheck")
public class DbDesignCheckCmp extends NodeComponent {
    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("DbDesignCheckCmp => 模拟执行数据库设计检查...");
        // 模拟逻辑：如果提交内容（可能是DDL脚本）缺少索引，则扣分
        if (context.getSubmissionContent().contains("no_index")) {
            context.setDbDesignScore(60.0);
            context.getFeedbackMessages().add("数据库设计检查：发现部分关键字段缺少索引，影响查询性能。");
        } else {
            context.setDbDesignScore(95.0);
            context.getFeedbackMessages().add("数据库设计检查：表结构设计规范，主键、外键、索引设置合理。");
        }
        context.addResult("db_design_check", "数据库设计符合第三范式");
    }
} 