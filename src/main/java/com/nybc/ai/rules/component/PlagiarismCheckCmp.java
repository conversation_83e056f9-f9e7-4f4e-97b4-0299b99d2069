package com.nybc.ai.rules.component;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：抄袭检测
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("plagiarismCheckCmp")
public class PlagiarismCheckCmp extends NodeComponent {

    /**
     * 组件核心处理逻辑
     * <p>
     * 真实场景下，这里会调用专业的抄袭检测服务（如SimHash, JPlag）
     * 或者将代码文本转换为向量，在向量数据库中进行相似度检索。
     * <p>
     * 这里我们使用简单的字符串检查来模拟。
     */
    @Override
    public void process() {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("PlagiarismCheckCmp => 开始执行抄袭检测...");

        // 模拟逻辑：如果代码中包含 "plagiarized_code" 关键字，则认为存在抄袭
        if (context.getSubmissionContent().contains("plagiarized_code")) {
            double plagiarismRate = 35.5; // 模拟抄袭率为35.5%
            context.setPlagiarismScore(plagiarismRate);
            context.setFinalScore(context.getFinalScore() - 20); // 抄袭扣20分
            context.getFeedbackMessages().add("检测到作业存在抄袭嫌疑，相似度约为 " + plagiarismRate + "%。请确保代码的原创性。已扣 20 分。");
            context.getTriggeredRuleTags().add("plagiarism_detected");
            log.warn("PlagiarismCheckCmp => 检测到抄袭内容。");
        } else {
            log.info("PlagiarismCheckCmp => 抄袭检测通过。");
        }
    }
} 