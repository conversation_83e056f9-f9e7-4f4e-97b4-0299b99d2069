package com.nybc.ai.rules.component.code;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：代码需求完成度AI检查
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("requirementCompletionCheck")
public class RequirementCompletionCheckCmp extends NodeComponent {
    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("RequirementCompletionCheckCmp => 模拟AI检查代码需求完成度...");
        // 模拟调用LLM，将代码与需求文档进行比对
        context.setRequirementCompletionScore(92.0);
        context.getFeedbackMessages().add("AI需求完成度分析：代码实现了约92%的需求点。部分可选需求（如导出PDF功能）未实现。");
        context.addResult("requirement_completion_check", "需求完成度100%");
    }
} 