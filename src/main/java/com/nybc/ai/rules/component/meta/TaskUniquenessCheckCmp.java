package com.nybc.ai.rules.component.meta;

import com.nybc.ai.rules.context.EvaluationContext;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * LiteFlow组件：项目任务唯一性检查
 *
 * <AUTHOR> 庆之
 * @version : 1.0
 */
@Slf4j
@Component("taskUniquenessCheck")
public class TaskUniquenessCheckCmp extends NodeComponent {
    @Override
    public void process() throws Exception {
        EvaluationContext context = this.getContextBean(EvaluationContext.class);
        log.info("TaskUniquenessCheckCmp => 模拟执行项目任务唯一性检查...");
        context.addResult("task_uniqueness_check", "唯一性检查通过");
    }
} 