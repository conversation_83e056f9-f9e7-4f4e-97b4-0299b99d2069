# AI对话优化与领域特定Prompt功能说明

## 功能概述

本模块实现了两个关键的AI能力，专门针对计算机、软件学院课程教学场景进行优化：

1. **多轮对话优化、上下文压缩能力**
2. **自动Prompt调优、领域特定优化**

## 1. 多轮对话优化、上下文压缩能力

### 核心功能

#### 1.1 智能上下文压缩
- **功能描述**：当对话历史过长时，智能压缩保留关键信息
- **技术特点**：
  - 基于消息重要性评分的智能筛选
  - 保留关键技术讨论和代码片段
  - 动态调整压缩比例
  - 支持自定义token限制

#### 1.2 对话摘要生成
- **功能描述**：对长对话进行智能摘要，提取关键信息
- **摘要类型**：
  - `BRIEF`：简要摘要，突出核心要点
  - `DETAILED`：详细摘要，包含完整讨论过程
  - `TECHNICAL`：技术摘要，专注于技术要点和代码

#### 1.3 多轮对话Prompt优化
- **功能描述**：基于对话历史和上下文，优化下一轮对话的Prompt
- **优化策略**：
  - 避免重复询问已讨论的内容
  - 结合领域上下文增强专业性
  - 根据学生水平调整表达方式

#### 1.4 主题转换检测
- **功能描述**：识别对话是否转向新主题，决定是否需要重置上下文
- **检测维度**：
  - 技术领域变化
  - 讨论深度转换
  - 问题类型切换

#### 1.5 领域实体提取
- **功能描述**：专门针对计算机、软件学院的专业术语和概念进行提取
- **提取内容**：
  - 编程语言和框架
  - 算法和数据结构
  - 设计模式和架构
  - 开发工具和数据库

### API接口

```http
# 压缩对话上下文
POST /api/ai/conversation/context/compress?sessionId={sessionId}&maxTokens={maxTokens}

# 生成对话摘要
POST /api/ai/conversation/summary/generate?sessionId={sessionId}&summaryType={summaryType}

# 优化多轮对话Prompt
POST /api/ai/conversation/prompt/optimize?sessionId={sessionId}&currentPrompt={prompt}

# 检测对话主题转换
POST /api/ai/conversation/topic/detect-shift?sessionId={sessionId}&newMessage={message}

# 智能上下文管理
POST /api/ai/conversation/context/manage?sessionId={sessionId}&maxContextLength={length}

# 提取领域实体
GET /api/ai/conversation/entities/extract?sessionId={sessionId}
```

### 使用示例

```java
@Service
public class ConversationService {
    
    @Resource
    private ConversationOptimizationService conversationOptimizationService;
    
    public void handleLongConversation(String sessionId) {
        // 1. 检查是否需要压缩上下文
        ConversationContext context = conversationOptimizationService.manageContext(sessionId, 4000);
        
        // 2. 如果对话过长，生成摘要
        if (context.getTokenCount() > 3000) {
            ConversationSummary summary = conversationOptimizationService.generateSummary(
                sessionId, "TECHNICAL");
        }
        
        // 3. 优化下一轮对话
        String optimizedPrompt = conversationOptimizationService.optimizeMultiTurnPrompt(
            sessionId, userInput, domainContext);
    }
}
```

## 2. 自动Prompt调优、领域特定优化

### 核心功能

#### 2.1 计算机科学领域专项优化
- **目标领域**：
  - `COMPUTER_SCIENCE`：计算机科学基础
  - `SOFTWARE_ENGINEERING`：软件工程
  - `DATA_STRUCTURE`：数据结构
  - `ALGORITHM`：算法设计
  - `DATABASE`：数据库技术

#### 2.2 场景化Prompt优化

##### 代码评估场景
- **优化重点**：代码质量、性能、可维护性评估
- **技术标准**：语法正确性、算法效率、设计模式应用
- **输出格式**：结构化评估报告

##### 作业批改场景
- **作业类型**：
  - `PROGRAMMING`：编程作业
  - `DESIGN`：设计作业
  - `REPORT`：报告类作业
  - `PROJECT`：项目作业
- **评分维度**：技术实现、文档质量、创新性

##### 学生答疑场景
- **学生水平适配**：
  - `BEGINNER`：基础概念解释，循序渐进
  - `INTERMEDIATE`：深入分析，实践结合
  - `ADVANCED`：高级技术，创新思维
- **教学策略**：启发式提问、案例分析

##### 技术文档分析场景
- **文档类型**：
  - `API_DOC`：API文档分析
  - `DESIGN_DOC`：设计文档评审
  - `USER_MANUAL`：用户手册检查
  - `TECHNICAL_SPEC`：技术规范验证

#### 2.3 自动Prompt调优
- **基于历史数据**：分析执行日志和反馈数据
- **优化目标**：
  - `ACCURACY`：提高准确性
  - `SPEED`：优化响应速度
  - `COMPREHENSIVENESS`：增强全面性
- **调优策略**：动态参数调整、模板优化

#### 2.4 最佳模式自动应用
- **智能检测**：自动识别Prompt类型和应用场景
- **模式库**：预定义的最佳实践模式
- **自适应优化**：根据上下文自动选择最佳策略

### API接口

```http
# 计算机科学领域Prompt优化
POST /api/ai/domain-prompt/optimize/computer-science

# 代码评估场景优化
POST /api/ai/domain-prompt/optimize/code-evaluation

# 作业批改场景优化
POST /api/ai/domain-prompt/optimize/assignment-grading

# 学生答疑场景优化
POST /api/ai/domain-prompt/optimize/student-tutoring

# 技术文档分析优化
POST /api/ai/domain-prompt/optimize/document-analysis

# 自动应用最佳模式
POST /api/ai/domain-prompt/auto-apply-pattern

# 基于历史数据自动调优
POST /api/ai/domain-prompt/auto-tune

# 生成领域特定模板
POST /api/ai/domain-prompt/generate-template
```

### 使用示例

```java
@Service
public class PromptOptimizationService {
    
    @Resource
    private DomainSpecificPromptService domainPromptService;
    
    public void optimizeCodeReviewPrompt() {
        // 1. 构建优化请求
        DomainPromptOptimizationRequest request = new DomainPromptOptimizationRequest()
            .setOriginalPrompt("请评估这段代码")
            .setTargetDomain("SOFTWARE_ENGINEERING")
            .setScenario("CODE_REVIEW")
            .setStudentLevel("INTERMEDIATE")
            .setOptimizationGoal("ACCURACY");
        
        // 2. 执行领域优化
        DomainPromptOptimizationResponse response = 
            domainPromptService.optimizeForComputerScience(request);
        
        // 3. 获取优化后的Prompt
        String optimizedPrompt = response.getOptimizedPrompt();
    }
    
    public void autoOptimizePrompt(String promptKey) {
        // 基于历史数据自动调优
        DomainPromptOptimizationResponse response = 
            domainPromptService.autoTunePrompt(promptKey, tenantId, "ACCURACY");
    }
}
```

## 配置说明

### 应用配置

```yaml
app:
  ai:
    default-provider: deepseek
  conversation:
    max-context-tokens: 4000          # 最大上下文token数量
    summary-retention-days: 30        # 对话摘要保留天数
    auto-compression-enabled: true    # 启用自动上下文压缩
    compression-threshold: 0.8        # 上下文压缩阈值
    topic-shift-sensitivity: 0.7      # 主题转换检测敏感度
    domain-entity-extraction-enabled: true  # 启用领域实体提取
    max-history-messages: 50          # 对话历史保留数量
  feedback:
    auto-optimization: true           # 启用自动优化
    analysis-retention-days: 180      # 分析数据保留天数
```

## 技术特点

### 1. 领域专业性
- **专业术语库**：内置计算机科学领域术语
- **技术上下文**：理解编程语言、框架、工具
- **教学适配**：符合教学场景的表达方式

### 2. 智能化程度
- **自动检测**：智能识别场景和需求
- **自适应优化**：根据反馈持续改进
- **上下文感知**：充分利用对话历史

### 3. 性能优化
- **高效压缩**：保持信息完整性的同时减少token消耗
- **缓存机制**：避免重复计算
- **异步处理**：支持大量并发请求

## 应用价值

1. **提升对话质量**：通过智能优化提高AI回答的专业性和准确性
2. **降低成本**：通过上下文压缩减少token消耗
3. **增强用户体验**：提供更加个性化和专业的交互体验
4. **支持长对话**：解决长对话中的上下文管理问题
5. **领域专业化**：针对计算机教育场景的深度优化

这两个AI能力的实现，显著提升了AI助教平台在计算机、软件学院教学场景中的专业性和实用性，为师生提供了更加智能和高效的AI交互体验。
