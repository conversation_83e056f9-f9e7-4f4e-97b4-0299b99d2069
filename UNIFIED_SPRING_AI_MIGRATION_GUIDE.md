# 统一Spring AI架构迁移指南

## 🎯 迁移目标

将现有的**3套AI集成方式**统一为**1套Spring AI标准架构**：

### 迁移前（复杂）
```
├── Spring AI配置 (OpenAI/Ollama)
├── 自定义ChatService (DashScope SDK + 自定义实现)
└── AiModelService (DeepSeek + 智谱等自定义实现)
```

### 迁移后（简洁）
```
└── 统一Spring AI架构
    ├── DeepSeek (OpenAI兼容API)
    ├── 阿里百炼 (OpenAI兼容API)  
    ├── OpenAI (原生支持)
    └── Ollama (原生支持)
```

## 📊 **为什么要统一？**

### **技术优势**
- ✅ **代码简化**：从3套API减少到1套Spring AI标准API
- ✅ **维护性**：统一的配置、错误处理、监控
- ✅ **功能完整**：流式响应、嵌入向量、函数调用等
- ✅ **类型安全**：Spring AI提供完整的类型支持
- ✅ **自动配置**：减少手动配置代码

### **业务优势**
- ✅ **无缝切换**：运行时动态切换AI提供商
- ✅ **成本优化**：根据成本和性能选择最优提供商
- ✅ **风险分散**：多提供商备份，避免单点故障
- ✅ **标准化**：统一的API调用方式

## 🔧 **迁移步骤**

### **第1步：启用统一配置**

#### 1.1 更新application.yml
```yaml
spring:
  profiles:
    active: unified-ai  # 启用统一AI配置

  ai:
    # DeepSeek (默认启用)
    deepseek:
      enabled: true
      api-key: ${DEEPSEEK_API_KEY}
      
    # 阿里百炼 (OpenAI兼容)
    dashscope:
      enabled: true
      api-key: ${DASHSCOPE_API_KEY}
      
    # OpenAI (可选)
    openai:
      enabled: false
      api-key: ${OPENAI_API_KEY}
      
    # Ollama (本地部署)
    ollama:
      enabled: false
      base-url: http://localhost:11434
```

#### 1.2 环境变量配置
```bash
# 必需
export DEEPSEEK_API_KEY=sk-your-deepseek-key

# 可选 - 阿里百炼
export DASHSCOPE_API_KEY=sk-your-dashscope-key

# 可选 - OpenAI
export OPENAI_API_KEY=sk-your-openai-key
```

### **第2步：迁移现有服务**

#### 2.1 替换AiModelService调用
```java
// 迁移前
@Service
public class OldPromptOptimizationService {
    @Resource
    private AiModelServiceFactory aiModelServiceFactory;
    
    public String optimizePrompt(String prompt) {
        AiModelService service = aiModelServiceFactory.getService("deepseek");
        return service.call(prompt, parameters);
    }
}

// 迁移后
@Service
public class NewPromptOptimizationService {
    @Resource
    private UnifiedAiService unifiedAiService;
    
    public String optimizePrompt(String prompt) {
        return unifiedAiService.chat(prompt);
    }
    
    // 或者指定提供商
    public String optimizePromptWithProvider(String prompt, String provider) {
        return unifiedAiService.chatWithProvider(provider, prompt);
    }
}
```

#### 2.2 替换ChatService调用
```java
// 迁移前
@RestController
public class OldChatController {
    @Resource
    private ChatServiceFactory chatServiceFactory;
    
    @PostMapping("/stream/{provider}")
    public Flux<String> streamChat(@PathVariable String provider, @RequestBody ChatRequest request) {
        return chatServiceFactory.getChatService(provider)
            .map(service -> service.streamChat(request))
            .orElseThrow();
    }
}

// 迁移后
@RestController
public class NewChatController {
    @Resource
    private UnifiedAiService unifiedAiService;
    
    @PostMapping("/stream")
    public Flux<String> streamChat(@RequestBody String prompt) {
        return unifiedAiService.streamChat(prompt);
    }
    
    @PostMapping("/stream/{provider}")
    public Flux<String> streamChatWithProvider(@PathVariable String provider, @RequestBody String prompt) {
        return unifiedAiService.streamChatWithProvider(provider, prompt);
    }
}
```

#### 2.3 替换Spring AI ChatClient调用
```java
// 迁移前
@Service
public class OldDocumentService {
    @Resource
    private ChatClient.Builder chatClientBuilder;
    
    public String queryDocument(String query) {
        return chatClientBuilder.build()
            .prompt(query)
            .call()
            .content();
    }
}

// 迁移后 - 可以保持不变，或者使用统一服务
@Service
public class NewDocumentService {
    @Resource
    private UnifiedAiService unifiedAiService;
    
    public String queryDocument(String query) {
        return unifiedAiService.chat(query);
    }
    
    // 或者继续使用ChatClient（推荐）
    @Resource
    private ChatClient.Builder chatClientBuilder;
    
    public String queryDocumentAdvanced(String query, List<Document> context) {
        return chatClientBuilder.build()
            .prompt("基于上下文回答：{context}\n问题：{query}")
            .param("context", context)
            .param("query", query)
            .call()
            .content();
    }
}
```

### **第3步：逐步禁用旧实现**

#### 3.1 禁用自定义ChatService
```java
// 在配置中添加条件注解
@Service
@ConditionalOnProperty(name = "app.ai.legacy.chat-service.enabled", havingValue = "true", matchIfMissing = false)
public class DashScopeChatServiceImpl implements ChatService {
    // 现有实现保持不变，但默认禁用
}
```

#### 3.2 禁用自定义AiModelService
```java
// 在配置中添加条件注解
@Service
@ConditionalOnProperty(name = "app.ai.legacy.model-service.enabled", havingValue = "true", matchIfMissing = false)
public class DeepSeekAiModelServiceImpl implements AiModelService {
    // 现有实现保持不变，但默认禁用
}
```

### **第4步：验证迁移效果**

#### 4.1 创建测试接口
```java
@RestController
@RequestMapping("/api/ai/unified")
public class UnifiedAiTestController {
    
    @Resource
    private UnifiedAiService unifiedAiService;
    
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        return unifiedAiService.getSystemStatus();
    }
    
    @PostMapping("/test/{provider}")
    public String testProvider(@PathVariable String provider, @RequestBody String prompt) {
        return unifiedAiService.chatWithProvider(provider, prompt);
    }
    
    @GetMapping("/providers")
    public List<String> getAvailableProviders() {
        return unifiedAiService.getAvailableProviders();
    }
}
```

#### 4.2 测试各个提供商
```bash
# 测试系统状态
curl http://localhost:8080/api/ai/unified/status

# 测试DeepSeek
curl -X POST http://localhost:8080/api/ai/unified/test/deepseek \
  -H "Content-Type: application/json" \
  -d "你好，请介绍一下自己"

# 测试阿里百炼
curl -X POST http://localhost:8080/api/ai/unified/test/dashscope \
  -H "Content-Type: application/json" \
  -d "你好，请介绍一下自己"
```

## 🎯 **迁移后的使用方式**

### **1. 基础聊天**
```java
@Service
public class ChatService {
    @Resource
    private UnifiedAiService aiService;
    
    // 使用默认提供商
    public String chat(String prompt) {
        return aiService.chat(prompt);
    }
    
    // 指定提供商
    public String chatWithDeepSeek(String prompt) {
        return aiService.chatWithProvider("deepseek", prompt);
    }
    
    // 流式响应
    public Flux<String> streamChat(String prompt) {
        return aiService.streamChat(prompt);
    }
}
```

### **2. 参数化提示**
```java
@Service
public class PromptService {
    @Resource
    private UnifiedAiService aiService;
    
    public String analyzeCode(String code, String language) {
        String template = """
            请分析以下{language}代码：
            
            ```{language}
            {code}
            ```
            
            请从以下方面进行分析：
            1. 代码质量
            2. 性能优化建议
            3. 安全性检查
            """;
            
        return aiService.chat(template, Map.of(
            "code", code,
            "language", language
        ));
    }
}
```

### **3. 嵌入向量生成**
```java
@Service
public class EmbeddingService {
    @Resource
    private UnifiedAiService aiService;
    
    public List<Double> generateEmbedding(String text) {
        return aiService.generateEmbedding(text);
    }
    
    public List<List<Double>> generateEmbeddings(List<String> texts) {
        return aiService.generateEmbeddings(texts);
    }
}
```

### **4. 高级ChatClient使用**
```java
@Service
public class AdvancedChatService {
    @Resource
    private ChatClient.Builder chatClientBuilder;
    
    public String complexQuery(String query, List<Document> context) {
        return chatClientBuilder.build()
            .prompt("基于以下文档回答问题：\n{context}\n\n问题：{query}")
            .param("context", context.stream()
                .map(Document::getContent)
                .collect(Collectors.joining("\n")))
            .param("query", query)
            .call()
            .content();
    }
}
```

## 📈 **迁移收益**

### **代码简化**
- **减少70%的配置代码**
- **统一的API调用方式**
- **自动的错误处理和重试**

### **功能增强**
- **原生流式响应支持**
- **完整的嵌入向量功能**
- **函数调用支持**
- **多模态能力（图像、音频）**

### **运维简化**
- **统一的监控和日志**
- **一致的配置管理**
- **简化的部署流程**

## 🎉 **总结**

通过统一到Spring AI架构，您将获得：

✅ **架构简化**：从3套API统一为1套标准API  
✅ **功能增强**：获得Spring AI的完整功能支持  
✅ **维护简化**：统一的配置、监控、错误处理  
✅ **扩展性**：轻松添加新的AI提供商  
✅ **标准化**：符合Spring生态的最佳实践  

**这是一个非常值得的架构升级！** 🚀
