# 功能设计与流程说明文档

## 1. 系统概述

本系统是一个集成了先进AI能力与动态流程编排的智能分析平台。其核心设计思想是将复杂的业务逻辑拆解为一系列可独立管理、可灵活组合的原子化组件。系统通过两大核心引擎驱动：

*   **AI 智能分析核心**：负责处理和理解非结构化数据（如各类文档和代码），利用大语言模型（LLM）提取、分析和生成内容。
*   **LiteFlow 动态规则引擎**：负责将原子化的AI任务和业务规则，根据不同的业务场景，编排成可执行的、可动态调整的流程。

这种设计使得系统既具备了AI的智能，又拥有了企业级的灵活性和可扩展性，能够快速适应多变的业务需求。

## 2. 核心模块详解

### 2.1. AI 智能分析核心

此模块是系统与AI大语言模型交互的桥梁，提供了通用的文件处理能力。

#### 功能描述：
- **支持多种文件来源**：
    - **API上传**：允许用户通过HTTP接口直接上传文件进行实时分析。
    - **服务器本地读取**：允许系统根据指定的服务器本地路径，读取单个文件或递归解析整个目录。
- **智能文件类型识别**：系统能根据文件扩展名，自动识别并采用相应的解析策略。
- **广泛的格式兼容**：
    - **Office 文档**：深度解析 `.docx`, `.pptx`, `.xlsx` 文件，提取全部文本内容。
    - **代码文件**：支持 `.java`, `.py`, `.xml`, `.html`, `.js`, `css` 等常见代码文件。
    - **文本文件**：支持 `.md`, `.txt`, `.json`, `.yml` 等通用文本格式。
- **提示词（Prompt）驱动分析**：所有的AI分析都由用户提供的**提示词**驱动。用户可以通过不同的提示词，让AI对同一份文件完成不同的任务（如"总结这份文档"、"分析这段代码的逻辑缺陷"、"将这份PPT的内容整理成大纲"等）。

#### API 接口：
- `POST /api/ai/parse-upload`：用于解析用户上传的文件。
- `POST /api/ai/parse-directory`：用于解析服务器本地指定路径的文件或目录。

### 2.2. LiteFlow 动态规则引擎

此模块是系统业务流程的"大脑"，负责执行、编排和管理所有业务逻辑。

#### 功能描述：
- **流程完全可编排**：业务流程（即"规则链"）由简单的EL（Express Language）表达式定义，例如 `THEN(A, B, C)` 表示顺序执行，`WHEN(A, B, C)` 表示并行执行。
- **规则动态化管理**：
    - 所有规则链都存储在数据库中，而非硬编码在代码里。
    - 提供了完整的 **增删改查 API** (`/api/rules/admin/*`)，允许开发或运维人员在**不重启服务**的情况下，动态创建、修改和部署新的业务流程。
- **支持复杂流程模式**：
    - **串行执行 (`THEN`)**：按顺序执行一系列任务。
    - **并行执行 (`WHEN`)**：同时执行多个任务，并等待所有任务完成后再继续，能极大提升处理效率。
    - **条件判断 (`IF`)**：根据前一个组件的执行结果，动态决定后续要执行哪个分支逻辑。
    - **子链嵌套**：可以将一条复杂的规则链拆分为多个可复用的子链，使逻辑更清晰，易于维护。
- **流程执行的灵活性**：
    - 调用方可以在请求时通过 `chainName` 参数**明确指定**要执行哪条规则链。
    - 如果不指定，系统会根据预设的规则（如租户ID、作业ID）**智能匹配**一条最合适的默认规则链。

#### API 接口：
- `POST /api/homework/evaluate`：执行作业评估的入口。
- `POST /api/rules/admin/save`：创建或更新一条规则链。
- `GET /api/rules/admin/list`：获取所有已定义的规则链。

## 3. 关键业务流程图

### 3.1. AI 文件解析流程

此流程展示了用户从发起文件解析请求到获取AI答复的全过程。

```mermaid
graph TD;
    A[用户通过 API 发起请求<br>(上传文件或指定路径, 并附带Prompt)] --> B{FileParseController};
    B --> C[FileParserService];
    C --> D{判断文件来源<br>(上传/本地)};
    D -- 上传 --> E[从 MultipartFile 获取 InputStream];
    D -- 本地 --> F[从本地文件路径获取 InputStream];
    E --> G[extractTextFromStream<br>核心文本提取];
    F --> G;
    G --> H[将提取的文本与Prompt<br>组合成最终的AI请求];
    H --> I[ChatClient<br>调用大语言模型];
    I --> J[获取AI生成的分析结果];
    J --> B;
    B --> K[返回给用户];
```

### 3.2. LiteFlow 动态评估流程

此流程展示了系统如何根据用户请求，灵活地选择并执行一条评估规则链。

```mermaid
graph TD;
    A[用户发起评估请求<br>POST /api/homework/evaluate<br>(可选择性传入 chainName)] --> B{HomeworkController};
    B --> C[LiteFlowRuleEngineService];
    C --> D{请求是否直接<br>指定了 chainName?};
    D -- 是 --> E[使用指定的 chainName];
    D -- 否 --> F[根据租户/作业ID<br>从数据库查找默认 chainName];
    F --> G{找到规则链?};
    G -- 否 --> H[抛出异常, 终止流程];
    G -- 是 --> E;
    E --> I[FlowExecutor.execute2Resp<br>执行规则链];
    I --编排--> J[并行/串行/条件<br>执行组件A, B, C...];
    J --> K[组件与 EvaluationContext<br>进行数据交互];
    K --> I;
    I --> L[返回包含<br>所有结果的上下文];
    L --> C;
    C --> B;
    B --> M[返回最终评估结果给用户];
```

## 4. 预置默认规则链

为了方便开箱即用，系统预置了一条名为 `comprehensive_evaluation_chain` 的综合评估链，其执行逻辑如下：

```mermaid
graph TD;
    subgraph comprehensive_evaluation_chain
        direction LR
        A(开始) --> B(initContext<br>初始化上下文);
        B --> C{WHEN 并行执行};
        C --> D(code_analysis_sub_chain<br>代码分析子链);
        C --> E(meta_analysis_sub_chain<br>元数据分析子链);
        D --> F{summarizeAndScore<br>总结与评分};
        E --> F;
        F --> G(结束);
    end

    subgraph code_analysis_sub_chain
        direction LR
        H(开始) --> I(codeCompile<br>编译代码);
        I --> J{IF 编译成功?};
        J -- 是 --> K(THEN 串行执行);
        K --> L(syntaxCheck<br>语法检查);
        L --> M(logicAnalysis<br>逻辑分析);
        M --> N(结束)
        J -- 否 --> N;
    end
``` 