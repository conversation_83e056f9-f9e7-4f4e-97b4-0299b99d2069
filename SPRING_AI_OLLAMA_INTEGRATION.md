# Spring AI Ollama 集成说明

## 概述

Spring AI 1.0.0 对 Ollama 提供了**原生的一流支持**，您现在拥有了**三种**使用 Ollama 的方式：

1. **Spring AI 原生支持** ✅ (新增)
2. **自定义 ChatService 实现** ✅ (现有)
3. **AiModelService 实现** ✅ (现有)

## 🎯 三种方式对比

| 方式 | 优势 | 使用场景 | 配置复杂度 |
|------|------|----------|------------|
| **Spring AI 原生** | 官方支持、自动配置、功能完整 | 标准AI功能、文档查询、RAG | ⭐ 简单 |
| **自定义 ChatService** | 流式响应、自定义协议 | 实时聊天、流式输出 | ⭐⭐ 中等 |
| **AiModelService** | 高度定制、参数控制 | AI分析、Prompt优化 | ⭐⭐⭐ 复杂 |

## 🔧 配置方式

### 1. Spring AI 原生配置

#### application.yml
```yaml
spring:
  ai:
    ollama:
      enabled: true                    # 启用Ollama
      base-url: http://localhost:11434 # Ollama服务地址
      chat:
        options:
          model: llama3.2              # 聊天模型
          temperature: 0.7
      embedding:
        options:
          model: nomic-embed-text      # 嵌入模型
```

#### 环境变量配置
```bash
# 启用Ollama
export OLLAMA_ENABLED=true
export OLLAMA_BASE_URL=http://localhost:11434
export OLLAMA_MODEL=llama3.2
export OLLAMA_EMBEDDING_MODEL=nomic-embed-text
```

### 2. 自动配置Bean

Spring AI会自动创建以下Bean：
- `OllamaApi` - Ollama API客户端
- `OllamaChatModel` - 聊天模型
- `OllamaEmbeddingModel` - 嵌入模型（如果配置）

## 🚀 使用示例

### 1. Spring AI 标准方式

```java
@Service
public class DocumentQueryService {
    
    @Resource
    private ChatClient.Builder chatClientBuilder;
    
    public String queryWithOllama(String query) {
        return chatClientBuilder.build()
            .prompt(query)
            .call()
            .content();
    }
    
    public Flux<String> streamQueryWithOllama(String query) {
        return chatClientBuilder.build()
            .prompt(query)
            .stream()
            .content();
    }
}
```

### 2. 自定义ChatService方式

```java
@RestController
public class ChatController {
    
    @Resource
    private ChatServiceFactory chatServiceFactory;
    
    @PostMapping("/stream/ollama")
    public Flux<String> streamChat(@RequestBody ChatRequest request) {
        return chatServiceFactory.getChatService("ollama")
            .map(service -> service.streamChat(request))
            .orElseThrow();
    }
}
```

### 3. AiModelService方式

```java
@Service
public class PromptOptimizationService {
    
    @Resource
    private AiModelServiceFactory aiModelServiceFactory;
    
    public String optimizeWithOllama(String prompt) {
        AiModelService ollamaService = aiModelServiceFactory.getService("spring-ai-ollama");
        return ollamaService.call(prompt, Map.of("temperature", 0.3));
    }
}
```

## 📊 功能对比表

| 功能 | Spring AI原生 | 自定义ChatService | AiModelService |
|------|---------------|-------------------|----------------|
| **聊天对话** | ✅ 完整支持 | ✅ 流式支持 | ✅ 基础支持 |
| **流式响应** | ✅ 原生支持 | ✅ 自定义实现 | ❌ 不支持 |
| **嵌入向量** | ✅ 原生支持 | ❌ 不支持 | ❌ 不支持 |
| **函数调用** | ✅ 支持 | ❌ 不支持 | ❌ 不支持 |
| **参数模板** | ✅ PromptTemplate | ❌ 手动处理 | ✅ 支持 |
| **自动配置** | ✅ 完全自动 | ❌ 手动配置 | ❌ 手动配置 |
| **错误处理** | ✅ 框架处理 | ✅ 自定义处理 | ✅ 自定义处理 |

## 🎯 推荐使用策略

### 场景1：文档查询和RAG
```java
// 推荐：Spring AI 原生
@Service
public class DocumentService {
    @Resource private ChatClient.Builder chatClientBuilder;
    
    public String queryDocument(String query, List<Document> context) {
        String contextText = context.stream()
            .map(Document::getContent)
            .collect(Collectors.joining("\n"));
            
        return chatClientBuilder.build()
            .prompt("基于以下上下文回答问题：\n{context}\n\n问题：{query}")
            .param("context", contextText)
            .param("query", query)
            .call()
            .content();
    }
}
```

### 场景2：实时聊天
```java
// 推荐：自定义ChatService
@RestController
public class RealtimeChatController {
    @Resource private ChatServiceFactory chatServiceFactory;
    
    @PostMapping("/chat/stream")
    public Flux<String> streamChat(@RequestBody ChatRequest request) {
        return chatServiceFactory.getChatService("ollama")
            .map(service -> service.streamChat(request))
            .orElseThrow();
    }
}
```

### 场景3：AI分析和优化
```java
// 推荐：AiModelService
@Service
public class AnalysisService {
    @Resource private AiModelServiceFactory aiModelServiceFactory;
    
    public String analyzeCode(String code) {
        AiModelService service = aiModelServiceFactory.getService("spring-ai-ollama");
        return service.call("分析以下代码：\n" + code, Map.of("temperature", 0.2));
    }
}
```

## 🔄 迁移建议

### 从自定义实现迁移到Spring AI

如果您想利用Spring AI的原生功能，可以逐步迁移：

#### 步骤1：启用Spring AI Ollama
```yaml
spring:
  ai:
    ollama:
      enabled: true
      base-url: http://localhost:11434
```

#### 步骤2：更新服务实现
```java
// 原来的实现
@Service
public class OldDocumentService {
    @Resource private ChatServiceFactory chatServiceFactory;
    
    public String query(String prompt) {
        return chatServiceFactory.getChatService("ollama")
            .map(service -> service.streamChat(new ChatRequest(prompt)))
            .orElseThrow()
            .blockLast(); // 阻塞获取最后结果
    }
}

// 新的Spring AI实现
@Service
public class NewDocumentService {
    @Resource private ChatClient.Builder chatClientBuilder;
    
    public String query(String prompt) {
        return chatClientBuilder.build()
            .prompt(prompt)
            .call()
            .content();
    }
}
```

## 🎉 总结

### 最佳实践建议

1. **新功能开发**：优先使用Spring AI原生支持
2. **现有功能**：保持现有实现，逐步迁移
3. **特殊需求**：使用自定义实现满足特定需求

### 架构优势

您现在拥有了**最灵活的Ollama集成方案**：
- ✅ **Spring AI原生**：标准功能、自动配置
- ✅ **自定义ChatService**：流式聊天、自定义协议  
- ✅ **AiModelService**：高级分析、参数控制

这种多层次的架构设计为不同的使用场景提供了最适合的解决方案！🚀
