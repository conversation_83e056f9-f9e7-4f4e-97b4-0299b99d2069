# LiteFlow工作流编排功能说明

## 功能概述

基于LiteFlow实现的智能工作流编排系统，支持根据不同任务类型动态匹配和执行相应的工作流模板，实现灵活的业务流程管理。

## 🎯 核心功能

### 1. 工作流模板管理

#### 功能特点
- **模板创建**：支持可视化创建工作流模板
- **版本管理**：自动版本控制和历史记录
- **模板复制**：快速复制和修改现有模板
- **导入导出**：支持JSON格式的模板导入导出

#### 支持的工作流类型
- **SEQUENTIAL**：顺序执行工作流
- **PARALLEL**：并行执行工作流
- **CONDITIONAL**：条件分支工作流
- **HYBRID**：混合型复杂工作流

### 2. 智能任务匹配

#### 匹配维度
- **任务类型**：HOMEWORK_EVALUATION, CODE_REVIEW, DOCUMENT_ANALYSIS, PROJECT_ASSESSMENT
- **文件类型**：java, py, js, cpp, sql等
- **编程语言**：支持多种编程语言识别
- **学生水平**：BEGINNER, INTERMEDIATE, ADVANCED
- **院校类型**：VOCATIONAL, UNDERGRADUATE, SOFTWARE_COLLEGE
- **课程类型**：理论课、实践课、项目课等

#### 匹配策略
```java
// 智能匹配示例
Map<String, Object> taskContext = Map.of(
    "fileType", "java",
    "studentLevel", "INTERMEDIATE", 
    "courseType", "PROGRAMMING",
    "complexity", "MEDIUM"
);

WorkflowTemplate template = workflowService.matchWorkflowByTask(
    "HOMEWORK_EVALUATION", taskContext, tenantId, assignmentId);
```

### 3. 动态工作流编排

#### LiteFlow EL表达式支持
```java
// 顺序执行
"THEN(initContextCmp, syntaxCheckCmp, logicAnalysisCmp, finalScoreCmp)"

// 并行执行
"THEN(initContextCmp, WHEN(codeQualityCmp, securityCheckCmp, performanceAnalysisCmp), summaryCmp)"

// 条件分支
"THEN(initContextCmp, IF(isJavaCodeCmp, javaAnalysisCmp, pythonAnalysisCmp), finalScoreCmp)"

// 复杂混合
"THEN(initContextCmp, WHEN(syntaxCheckCmp, IF(hasTestCmp, testAnalysisCmp)), logicAnalysisCmp, finalScoreCmp)"
```

## 📊 数据模型

### 工作流模板结构
```json
{
  "id": 1,
  "templateName": "Java作业评估工作流",
  "taskType": "HOMEWORK_EVALUATION",
  "workflowType": "SEQUENTIAL",
  "chainName": "java_homework_evaluation",
  "elExpression": "THEN(initContextCmp, syntaxCheckCmp, logicAnalysisCmp, finalScoreCmp)",
  "nodes": [
    {
      "nodeId": "init",
      "nodeName": "初始化上下文",
      "nodeType": "COMPONENT",
      "componentName": "initContextCmp",
      "required": true,
      "order": 1
    }
  ],
  "matchingCondition": {
    "taskTypes": ["HOMEWORK_EVALUATION"],
    "fileTypes": ["java"],
    "programmingLanguages": ["java"],
    "studentLevels": ["BEGINNER", "INTERMEDIATE"],
    "weight": 1.0
  }
}
```

### 任务匹配规则
```json
{
  "ruleId": 1,
  "ruleName": "Java初级作业匹配规则",
  "condition": {
    "taskType": {
      "types": ["HOMEWORK_EVALUATION"],
      "matchMode": "EXACT"
    },
    "file": {
      "extensions": ["java"],
      "programmingLanguages": ["java"]
    },
    "student": {
      "levels": ["BEGINNER"],
      "schoolTypes": ["UNDERGRADUATE"]
    },
    "logicOperator": "AND"
  },
  "targetTemplateId": 1,
  "priority": 100
}
```

## 🔧 API接口

### 工作流模板管理
```http
# 创建工作流模板
POST /api/ai/workflow/templates

# 更新工作流模板
PUT /api/ai/workflow/templates/{templateId}

# 获取工作流模板列表
GET /api/ai/workflow/templates?tenantId={tenantId}

# 获取工作流模板详情
GET /api/ai/workflow/templates/{templateId}

# 删除工作流模板
DELETE /api/ai/workflow/templates/{templateId}
```

### 智能匹配和执行
```http
# 智能匹配工作流
POST /api/ai/workflow/match

# 验证工作流模板
POST /api/ai/workflow/templates/{templateId}/validate

# 复制工作流模板
POST /api/ai/workflow/templates/{sourceTemplateId}/copy
```

### 任务匹配规则
```http
# 配置任务匹配规则
POST /api/ai/workflow/matching-rules

# 获取任务匹配规则
GET /api/ai/workflow/matching-rules?tenantId={tenantId}
```

### 高级功能
```http
# 生成工作流建议
POST /api/ai/workflow/templates/suggest

# 导出工作流模板
GET /api/ai/workflow/templates/{templateId}/export

# 导入工作流模板
POST /api/ai/workflow/templates/import

# 获取执行统计
GET /api/ai/workflow/templates/{templateId}/stats
```

## 🚀 使用示例

### 1. 创建工作流模板

```java
@Service
public class WorkflowManagementService {
    
    @Resource
    private WorkflowOrchestrationService workflowService;
    
    public void createJavaHomeworkWorkflow() {
        WorkflowTemplateRequest request = new WorkflowTemplateRequest()
            .setTemplateName("Java作业评估工作流")
            .setDescription("专门用于Java编程作业的评估")
            .setTaskType("HOMEWORK_EVALUATION")
            .setWorkflowType("SEQUENTIAL")
            .setElExpression("THEN(initContextCmp, javaCompileCmp, syntaxCheckCmp, logicAnalysisCmp, finalScoreCmp)")
            .setPriority(90)
            .setTenantId(1L);
        
        // 配置工作流节点
        List<WorkflowNode> nodes = Arrays.asList(
            new WorkflowNode()
                .setNodeId("init")
                .setNodeName("初始化")
                .setComponentName("initContextCmp")
                .setRequired(true)
                .setOrder(1),
            new WorkflowNode()
                .setNodeId("compile")
                .setNodeName("Java编译")
                .setComponentName("javaCompileCmp")
                .setRequired(true)
                .setOrder(2)
        );
        request.setNodes(nodes);
        
        // 配置匹配条件
        TaskMatchingCondition condition = new TaskMatchingCondition()
            .setTaskTypes(Arrays.asList("HOMEWORK_EVALUATION"))
            .setFileTypes(Arrays.asList("java"))
            .setProgrammingLanguages(Arrays.asList("java"))
            .setWeight(1.0);
        request.setMatchingCondition(condition);
        
        WorkflowTemplateResponse response = workflowService.createWorkflowTemplate(request);
    }
}
```

### 2. 智能匹配工作流

```java
@Service
public class TaskProcessingService {
    
    @Resource
    private WorkflowOrchestrationService workflowService;
    
    public void processStudentSubmission(Long assignmentId, MultipartFile file) {
        // 构建任务上下文
        Map<String, Object> taskContext = new HashMap<>();
        taskContext.put("fileType", getFileExtension(file.getOriginalFilename()));
        taskContext.put("fileSize", file.getSize());
        taskContext.put("studentLevel", "INTERMEDIATE");
        taskContext.put("courseType", "PROGRAMMING");
        
        // 智能匹配工作流
        WorkflowTemplate template = workflowService.matchWorkflowByTask(
            "HOMEWORK_EVALUATION", taskContext, tenantId, assignmentId);
        
        // 执行工作流（这里会调用LiteFlow执行引擎）
        executeWorkflow(template.getChainName(), taskContext);
    }
}
```

### 3. 配置任务匹配规则

```java
@Service
public class RuleConfigurationService {
    
    @Resource
    private WorkflowOrchestrationService workflowService;
    
    public void configureMatchingRules() {
        List<TaskMatchingRule> rules = Arrays.asList(
            // Java初级作业规则
            new TaskMatchingRule()
                .setRuleName("Java初级作业规则")
                .setCondition(new MatchingCondition()
                    .setTaskType(new TaskTypeCondition()
                        .setTypes(Arrays.asList("HOMEWORK_EVALUATION")))
                    .setFile(new FileCondition()
                        .setExtensions(Arrays.asList("java"))
                        .setProgrammingLanguages(Arrays.asList("java")))
                    .setStudent(new StudentCondition()
                        .setLevels(Arrays.asList("BEGINNER"))))
                .setTargetTemplateId(1L)
                .setPriority(100),
            
            // Python高级项目规则
            new TaskMatchingRule()
                .setRuleName("Python高级项目规则")
                .setCondition(new MatchingCondition()
                    .setTaskType(new TaskTypeCondition()
                        .setTypes(Arrays.asList("PROJECT_ASSESSMENT")))
                    .setFile(new FileCondition()
                        .setExtensions(Arrays.asList("py"))
                        .setProgrammingLanguages(Arrays.asList("python")))
                    .setStudent(new StudentCondition()
                        .setLevels(Arrays.asList("ADVANCED"))))
                .setTargetTemplateId(2L)
                .setPriority(90)
        );
        
        workflowService.configureTaskMatchingRules(rules);
    }
}
```

## 📈 监控和统计

### 执行统计
- **执行次数**：工作流模板的执行频率
- **成功率**：工作流执行的成功率统计
- **平均执行时间**：性能监控指标
- **错误分析**：失败原因分析和优化建议

### 性能优化
- **缓存机制**：模板和规则的智能缓存
- **并行执行**：支持节点级别的并行处理
- **资源管理**：合理的线程池和资源分配

## 🎯 应用场景

### 1. 作业评估场景
```
任务类型：HOMEWORK_EVALUATION
匹配条件：文件类型 + 学生水平 + 课程类型
工作流：初始化 → 语法检查 → 逻辑分析 → 评分
```

### 2. 代码审查场景
```
任务类型：CODE_REVIEW
匹配条件：编程语言 + 代码复杂度 + 项目类型
工作流：初始化 → 并行(质量检查 + 安全检查 + 性能分析) → 总结
```

### 3. 文档分析场景
```
任务类型：DOCUMENT_ANALYSIS
匹配条件：文档类型 + 内容复杂度 + 分析目标
工作流：初始化 → 结构分析 → 内容分析 → 质量评估
```

### 4. 项目评估场景
```
任务类型：PROJECT_ASSESSMENT
匹配条件：项目规模 + 技术栈 + 评估标准
工作流：初始化 → 并行(代码评估 + 文档评估 + 功能测试) → 综合评分
```

## 🔧 扩展性设计

### 1. 自定义组件
- 支持开发自定义LiteFlow组件
- 组件热插拔和动态加载
- 组件版本管理和兼容性检查

### 2. 规则引擎集成
- 与现有规则引擎无缝集成
- 支持复杂的业务规则配置
- 规则的动态更新和生效

### 3. AI智能优化
- 基于执行历史的智能优化建议
- 自动识别性能瓶颈和优化点
- 智能推荐最佳工作流配置

## 📝 总结

LiteFlow工作流编排功能提供了完整的工作流管理解决方案，支持：

✅ **灵活的工作流设计**：支持多种工作流模式和复杂业务逻辑
✅ **智能任务匹配**：基于多维度条件的自动工作流匹配
✅ **动态配置管理**：支持运行时的工作流配置和规则更新
✅ **完整的监控体系**：提供详细的执行统计和性能监控
✅ **高度可扩展**：支持自定义组件和规则的灵活扩展

这一功能的实现，使得AI助教平台能够根据不同的任务特征自动选择和执行最适合的处理流程，大大提升了系统的智能化水平和处理效率。
